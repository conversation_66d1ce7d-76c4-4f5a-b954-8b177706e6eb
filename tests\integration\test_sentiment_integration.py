#!/usr/bin/env python3
"""
Test script for sentiment analysis functionality.

This script demonstrates how to use the new sentiment data service
to analyze sentiment for trading symbols.
"""

import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent.parent.parent / "src"))

from services.sentiment_data import SentimentDataService
from services.ai_advisor import AIAdvisor
from utils.config import setup_config


async def test_sentiment_analysis():
    """Test sentiment analysis functionality."""
    print("🧠 Testing Sentiment Analysis")
    print("=" * 50)
    
    # Load configuration
    config = setup_config("config.json")
    config_dict = config.model_dump()
    
    # Test symbols
    symbols = ["AAPL", "TSLA", "NVDA"]
    
    # Initialize services
    sentiment_service = SentimentDataService(config_dict)
    ai_advisor = AIAdvisor(config_dict)
    
    print(f"📊 Testing sentiment analysis for {len(symbols)} symbols...")
    
    for symbol in symbols:
        print(f"\n🔍 Analyzing {symbol}...")
        
        try:
            # Get sentiment data
            sentiment_data = await sentiment_service.get_sentiment_data(symbol, hours=24)
            
            if sentiment_data:
                # Aggregate sentiment
                aggregated = sentiment_service.get_aggregated_sentiment(sentiment_data)
                
                print(f"   📈 Sentiment Metrics:")
                print(f"      Overall Sentiment: {aggregated['overall_sentiment']:.3f}")
                print(f"      Confidence: {aggregated['confidence']:.3f}")
                print(f"      Volume: {aggregated['volume']} articles")
                print(f"      Positive Ratio: {aggregated['positive_ratio']:.1%}")
                print(f"      Negative Ratio: {aggregated['negative_ratio']:.1%}")
                
                # Get AI analysis
                ai_result = await ai_advisor.analyze_sentiment(symbol)
                
                print(f"   🤖 AI Analysis:")
                print(f"      Recommendation: {ai_result.recommendation}")
                print(f"      Confidence: {ai_result.confidence:.3f}")
                print(f"      Reasoning: {ai_result.reasoning[:100]}...")
                
            else:
                print(f"   ⚠️ No sentiment data available for {symbol}")
                
        except Exception as e:
            print(f"   ❌ Error analyzing {symbol}: {e}")
    
    # Cleanup
    await sentiment_service.close()
    await ai_advisor.async_close()
    
    print(f"\n✅ Sentiment analysis test completed!")


async def test_integrated_analysis():
    """Test the integrated sentiment + technical analysis."""
    print("🔗 Testing Integrated Analysis (Sentiment + Technical)")
    print("=" * 60)
    
    # Load configuration
    config = setup_config("config.json")
    config_dict = config.model_dump()
    
    # Initialize AI advisor
    ai_advisor = AIAdvisor(config_dict)
    
    # Test symbol
    symbol = "AAPL"
    
    try:
        print(f"🔍 Testing integrated analysis for {symbol}...")
        
        # Get sample market data (you would normally get this from your market data service)
        import pandas as pd
        import yfinance as yf
        
        # Fetch some sample data
        ticker = yf.Ticker(symbol)
        data = ticker.history(period="30d", interval="1d")
        
        if not data.empty:
            print(f"   📊 Market data: {len(data)} data points")
            print(f"   📈 Latest price: ${data['Close'].iloc[-1]:.2f}")
            
            # Run integrated analysis (this will include sentiment data)
            result = await ai_advisor.detect_patterns(symbol, data)
            
            print(f"   🤖 Integrated Analysis Result:")
            print(f"      Recommendation: {result.recommendation}")
            print(f"      Confidence: {result.confidence:.3f}")
            print(f"      Reasoning: {result.reasoning[:200]}...")
            
            # Check if sentiment data was included
            if 'sentiment_metrics' in result.metadata:
                sentiment = result.metadata['sentiment_metrics']
                print(f"   📊 Sentiment Data Included:")
                print(f"      Overall Sentiment: {sentiment['overall_sentiment']:.3f}")
                print(f"      Volume: {sentiment['volume']} articles")
                print(f"      Headlines Analyzed: {result.metadata.get('headlines_analyzed', 0)}")
            else:
                print(f"   ⚠️ No sentiment data in result metadata")
                
        else:
            print(f"   ❌ No market data available for {symbol}")
            
    except Exception as e:
        print(f"   ❌ Error in integrated analysis: {e}")
        import traceback
        traceback.print_exc()
    
    # Cleanup
    await ai_advisor.async_close()
    
    print(f"\n✅ Integrated analysis test completed!")


async def test_sentiment_data_only():
    """Test sentiment data service only (without AI)."""
    print("📊 Testing Sentiment Data Service")
    print("=" * 40)
    
    # Load configuration
    config = setup_config("config.json")
    config_dict = config.model_dump()
    
    # Initialize service
    sentiment_service = SentimentDataService(config_dict)
    
    # Test symbol
    symbol = "AAPL"
    
    try:
        print(f"🔍 Fetching sentiment data for {symbol}...")
        
        # Get sentiment data
        sentiment_data = await sentiment_service.get_sentiment_data(symbol, hours=24)
        
        if sentiment_data:
            print(f"   ✅ Found {len(sentiment_data)} sentiment data points")
            
            # Show sample data
            for i, item in enumerate(sentiment_data[:3]):
                print(f"   📰 Sample {i+1}:")
                print(f"      Source: {item.source.value}")
                print(f"      Sentiment: {item.sentiment_score:.3f}")
                print(f"      Confidence: {item.confidence:.3f}")
                print(f"      Text: {item.text[:80]}...")
                print()
            
            # Show aggregated metrics
            aggregated = sentiment_service.get_aggregated_sentiment(sentiment_data)
            print(f"   📈 Aggregated Metrics:")
            print(f"      Overall Sentiment: {aggregated['overall_sentiment']:.3f}")
            print(f"      Confidence: {aggregated['confidence']:.3f}")
            print(f"      Volume: {aggregated['volume']}")
            print(f"      Positive Ratio: {aggregated['positive_ratio']:.1%}")
            print(f"      Negative Ratio: {aggregated['negative_ratio']:.1%}")
            
        else:
            print(f"   ⚠️ No sentiment data available for {symbol}")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Cleanup
    await sentiment_service.close()
    
    print(f"\n✅ Sentiment data test completed!")


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Test sentiment analysis functionality")
    parser.add_argument("--data-only", action="store_true", help="Test sentiment data service only")
    parser.add_argument("--integrated", action="store_true", help="Test integrated sentiment + technical analysis")
    
    args = parser.parse_args()
    
    if args.data_only:
        asyncio.run(test_sentiment_data_only())
    elif args.integrated:
        asyncio.run(test_integrated_analysis())
    else:
        asyncio.run(test_sentiment_analysis()) 