#!/usr/bin/env python3
"""
Enhanced Backtest Runner Script

This script runs the enhanced backtesting engine with comprehensive trade logging,
proper PnL calculation, detailed reporting, sentiment analysis, and AI analysis.
"""

import sys
import os
import asyncio
from datetime import datetime, timedelta
from pathlib import Path

# Add the src directory to the path
sys.path.append(str(Path(__file__).parent.parent / "src"))

from backtesting.enhanced_backtester import run_enhanced_backtest
from utils.config import get_config
from utils.logging import get_logger


async def main():
    """Run enhanced backtest with comprehensive reporting."""
    logger = get_logger()
    
    # Get configuration
    config = get_config()
    
    # Default parameters
    symbols = ["AAPL", "GOOGL", "MSFT", "NVDA", "META", "AMZN", "TSLA"]
    end_date = datetime.now().strftime("%Y-%m-%d")
    start_date = (datetime.now() - timedelta(days=90)).strftime("%Y-%m-%d")
    
    # Parse command line arguments
    if len(sys.argv) > 1:
        symbols = sys.argv[1].split(',')
    
    if len(sys.argv) > 2:
        start_date = sys.argv[2]
    
    if len(sys.argv) > 3:
        end_date = sys.argv[3]
    
    logger.info(f"🚀 Starting enhanced backtest for symbols: {symbols}")
    logger.info(f"📅 Date range: {start_date} to {end_date}")
    logger.info(f"🤖 AI Analysis: Enabled (Tier 1 providers)")
    logger.info(f"📊 Sentiment Analysis: Enabled (Reddit)")
    
    try:
        # Run enhanced backtest
        results = await run_enhanced_backtest(symbols, start_date, end_date, config)
        
        # Print comprehensive results
        print("\n" + "="*80)
        print("🎯 ENHANCED BACKTEST RESULTS (AI + Sentiment + Technical)")
        print("="*80)
        
        print(f"\n📊 PERFORMANCE METRICS:")
        print(f"   Initial Capital: ${results.initial_capital:,.2f}")
        print(f"   Final Capital:   ${results.final_capital:,.2f}")
        print(f"   Total P&L:       ${results.total_pnl:,.2f}")
        print(f"   Total Return:    {results.total_return:.2%}")
        print(f"   Annualized Return: {results.annualized_return:.2%}")
        print(f"   Sharpe Ratio:    {results.sharpe_ratio:.3f}")
        print(f"   Max Drawdown:    {results.max_drawdown:.2%}")
        
        print(f"\n💰 TRADING COSTS:")
        print(f"   Total Commission: ${results.total_commission:,.2f}")
        print(f"   Total Slippage:   ${results.total_slippage:,.2f}")
        print(f"   Net P&L:          ${results.total_pnl - results.total_commission - results.total_slippage:,.2f}")
        
        print(f"\n📈 TRADE STATISTICS:")
        print(f"   Total Trades:     {results.total_trades}")
        print(f"   Winning Trades:   {results.winning_trades}")
        print(f"   Losing Trades:    {results.losing_trades}")
        print(f"   Win Rate:         {results.win_rate:.2%}")
        print(f"   Average Win:      ${results.avg_win:,.2f}")
        print(f"   Average Loss:     ${results.avg_loss:,.2f}")
        print(f"   Profit Factor:    {results.profit_factor:.3f}")
        
        print(f"\n⏱️  TRADE DURATION:")
        print(f"   Average Trade Duration: {results.metrics['avg_trade_duration']:.1f} days")
        print(f"   Max Consecutive Losses: {results.metrics['max_consecutive_losses']}")
        
        print(f"\n📅 BACKTEST PERIOD:")
        print(f"   Start Date:       {results.start_date.strftime('%Y-%m-%d')}")
        print(f"   End Date:         {results.end_date.strftime('%Y-%m-%d')}")
        print(f"   Duration:         {(results.end_date - results.start_date).days} days")
        
        # Print recent trades with AI and sentiment data
        if results.trades:
            print(f"\n🔄 ALL TRADES ({len(results.trades)} total) - Enhanced Analysis:")
            print("-" * 140)
            print(f"{'Date':<12} {'Symbol':<8} {'Type':<6} {'Quantity':<10} {'Price':<10} {'P&L':<12} {'Net P&L':<12} {'Sentiment':<10} {'AI Rec':<10} {'AI Conf':<8}")
            print("-" * 140)
            
            # Sort trades by timestamp (newest first)
            sorted_trades = sorted(results.trades, key=lambda x: x.timestamp, reverse=True)
            for trade in sorted_trades:
                sentiment_str = f"{trade.sentiment_score:.2f}" if trade.sentiment_score is not None else "N/A"
                ai_rec = trade.ai_recommendation or "N/A"
                ai_conf = f"{trade.ai_confidence:.2f}" if trade.ai_confidence is not None else "N/A"
                
                print(f"{trade.timestamp.strftime('%Y-%m-%d'):<12} "
                      f"{trade.symbol:<8} "
                      f"{trade.trade_type.value:<6} "
                      f"{trade.quantity:<10.2f} "
                      f"${trade.price:<9.2f} "
                      f"${trade.pnl:<11.2f} "
                      f"${trade.net_pnl:<11.2f} "
                      f"{sentiment_str:<10} "
                      f"{ai_rec:<10} "
                      f"{ai_conf:<8}")
            
            # Check for sentiment consistency issue
            sentiment_scores = [t.sentiment_score for t in results.trades if t.sentiment_score is not None]
            if sentiment_scores:
                unique_sentiments = set(sentiment_scores)
                if len(unique_sentiments) <= 2:  # If only 1-2 unique sentiment scores
                    print(f"\n⚠️  SENTIMENT ANALYSIS NOTE:")
                    print(f"   Detected {len(unique_sentiments)} unique sentiment scores across {len(sentiment_scores)} trades")
                    print(f"   This may indicate caching is working (1-hour cache duration)")
                    print(f"   Unique scores: {sorted(unique_sentiments)}")
        
        print("\n" + "="*80)
        print("✅ Enhanced backtest completed successfully!")
        print("📁 Trade history exported to 'backtests/' directory")
        print("🤖 AI Analysis: Tier 1 providers used for signal enhancement")
        print("📊 Sentiment Analysis: Reddit sentiment integrated (1-hour cache)")
        print("="*80)
        
    except Exception as e:
        logger.log_error(e, {'operation': 'enhanced_backtest'})
        print(f"❌ Error running enhanced backtest: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
