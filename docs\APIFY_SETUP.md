# 🔍 Apify Historical Reddit Sentiment Setup Guide

This guide explains how to set up Apify for historical Reddit sentiment data scraping, which is perfect for backtesting and iterative learning.

## 🎯 **Why Use Apify for Historical Data?**

Apify provides several advantages for historical Reddit sentiment analysis:

- **Historical Data Access**: Scrape Reddit data from any date range
- **Backtesting Support**: Perfect for testing strategies with historical sentiment
- **Iterative Learning**: Train models on historical sentiment patterns
- **Scalable**: Handle large amounts of historical data efficiently
- **Reliable**: Professional scraping infrastructure with high success rates

## 📋 **Prerequisites**

1. **Apify Account**: Create a free account at [apify.com](https://apify.com)
2. **Reddit Scraper**: Use an existing Reddit scraper or create your own
3. **API Token**: Get your Apify API token
4. **Python Package**: Install `httpx` (already included in requirements)

## 🚀 **Step-by-Step Setup**

### **Step 1: Create an Apify Account**

1. **Visit Apify**: Go to [apify.com](https://apify.com)
2. **Sign Up**: Create a free account
3. **Verify Email**: Complete email verification

### **Step 2: Get Your API Token**

1. **Go to Integrations**: Visit [console.apify.com/account/integrations](https://console.apify.com/account/integrations)
2. **Create Token**: Click "Create new token"
3. **Set Permissions**: Select "Full access" for development
4. **Copy Token**: Save your API token securely

### **Step 3: Find or Create a Reddit Scraper**

#### **Option A: Use Existing Reddit Scraper**

1. **Search Store**: Go to [apify.com/store](https://apify.com/store)
2. **Search "Reddit"**: Find Reddit scrapers
3. **Choose Scraper**: Look for "Reddit Scraper" or similar
4. **Note Scraper ID**: Copy the scraper ID (e.g., `reddit-scraper`)

#### **Option B: Create Custom Reddit Scraper**

1. **Create Actor**: Go to [console.apify.com/actors](https://console.apify.com/actors)
2. **New Actor**: Click "Create new"
3. **Choose Template**: Select "Web scraping" template
4. **Configure Scraper**: Set up Reddit scraping logic
5. **Deploy**: Deploy your custom scraper
6. **Note ID**: Copy your custom scraper ID

### **Step 4: Configure Environment Variables**

1. **Copy Environment Template**:
   ```bash
   cp env.example .env
   ```

2. **Add Apify Credentials**:
   ```bash
   # Apify API Credentials (for historical Reddit scraping)
   APIFY_API_TOKEN=your_apify_api_token_here
   APIFY_REDDIT_SCRAPER_ID=your_apify_reddit_scraper_id_here
   ```

3. **Replace with Your Values**:
   - `your_apify_api_token_here`: Your Apify API token from Step 2
   - `your_apify_reddit_scraper_id_here`: Your Reddit scraper ID from Step 3

### **Step 5: Test the Setup**

1. **Run the Test Script**:
   ```bash
   python tests/integration/test_apify_sentiment.py
   ```

2. **Expected Output**:
   ```
   🔍 Testing Apify Historical Reddit Sentiment Analysis (Integration Test)
   =======================================================================
   📊 Testing historical Reddit sentiment for AAPL
      Date range: 2024-01-01 to 2024-01-31
   
   ✅ Found 150 historical sentiment data points
      📝 Posts: 45
      💬 Comments: 105
   ```

## 🔧 **Configuration Options**

### **Apify Configuration**

You can customize Apify settings in `config.json`:

```json
{
  "sentiment_data": {
    "apify": {
      "enabled": true,
      "api_token": "${APIFY_API_TOKEN}",
      "reddit_scraper_id": "${APIFY_REDDIT_SCRAPER_ID}",
      "max_posts": 1000,
      "max_comments": 100,
      "cache_duration": 86400
    }
  }
}
```

### **Scraper Parameters**

```json
{
  "sentiment_data": {
    "apify": {
      "max_posts": 1000,        // Maximum posts per subreddit
      "max_comments": 100,      // Maximum comments per post
      "cache_duration": 86400   // Cache duration in seconds (24 hours)
    }
  }
}
```

## 📊 **Usage Examples**

### **Basic Historical Data Fetching**

```python
from services.apify_service import ApifyService

# Initialize service
apify_service = ApifyService(config)

# Get historical sentiment data
sentiment_data = await apify_service.get_historical_reddit_data(
    symbol="AAPL",
    start_date="2024-01-01",
    end_date="2024-01-31"
)

# Analyze results
for item in sentiment_data:
    print(f"Date: {item.timestamp}")
    print(f"Sentiment: {item.sentiment_score:.3f}")
    print(f"Text: {item.text[:100]}...")
```

### **Backtesting Data Format**

```python
# Get data formatted for backtesting
df = await apify_service.get_backtesting_data(
    symbol="TSLA",
    start_date="2024-01-01",
    end_date="2024-01-31"
)

# Use in backtesting
if not df.empty:
    print(f"Average sentiment: {df['sentiment_score'].mean():.3f}")
    print(f"Sentiment volatility: {df['sentiment_score'].std():.3f}")
```

### **Integration with Backtesting**

```python
from backtesting.ai_weight_backtester import AIWeightBacktester

# Initialize backtester
backtester = AIWeightBacktester(config)

# Get historical sentiment data
sentiment_df = await apify_service.get_backtesting_data("AAPL", "2024-01-01", "2024-01-31")

# Run backtest with sentiment data
results = backtester.run_comprehensive_backtest(
    symbols=["AAPL"],
    start_date="2024-01-01",
    end_date="2024-01-31"
)
```

## 🎯 **Backtesting and Iterative Learning**

### **Historical Sentiment Analysis**

Apify enables powerful backtesting capabilities:

1. **Historical Patterns**: Analyze sentiment patterns over time
2. **Strategy Validation**: Test trading strategies with historical sentiment
3. **Model Training**: Train ML models on historical sentiment data
4. **Performance Analysis**: Compare strategy performance with and without sentiment

### **Iterative Learning Workflow**

```python
# 1. Collect historical data
historical_data = await apify_service.get_historical_reddit_data(
    symbol="AAPL",
    start_date="2023-01-01",
    end_date="2023-12-31"
)

# 2. Analyze sentiment patterns
sentiment_patterns = analyze_sentiment_patterns(historical_data)

# 3. Train/update models
model = train_sentiment_model(historical_data)

# 4. Test strategies
results = backtest_strategy_with_sentiment(model, historical_data)

# 5. Iterate and improve
improved_model = iterate_and_improve(model, results)
```

## 🚨 **Important Notes**

### **Rate Limiting and Costs**
- **Free Tier**: Limited API calls per month
- **Paid Plans**: Higher limits and priority support
- **Scraping Costs**: Pay per compute unit used
- **Data Storage**: Cached data reduces costs

### **Data Quality**
- **Historical Accuracy**: Data quality depends on Reddit's availability
- **Completeness**: Some historical data may be incomplete
- **Validation**: Always validate scraped data quality
- **Caching**: Use caching to avoid re-scraping

### **Privacy and Compliance**
- **Reddit Terms**: Respect Reddit's terms of service
- **Data Usage**: Use data responsibly and ethically
- **Storage**: Secure storage of scraped data
- **Compliance**: Follow data protection regulations

## 🐛 **Troubleshooting**

### **Common Issues**

1. **"Apify not configured"**:
   - Check your API token in `.env` file
   - Verify scraper ID is correct
   - Ensure Apify account is active

2. **"Failed to start Apify scraper"**:
   - Check API token permissions
   - Verify scraper ID exists
   - Check internet connection

3. **"No results from Apify scraper"**:
   - Check scraper configuration
   - Verify date range is valid
   - Check if symbol has Reddit activity

4. **"Scraper timed out"**:
   - Increase max wait time
   - Check scraper complexity
   - Consider using smaller date ranges

### **Debug Mode**

Enable debug logging to see detailed API interactions:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📈 **Performance Optimization**

### **Caching Strategy**
- **Local Cache**: 24-hour cache for historical data
- **Smart Caching**: Only re-scrape when needed
- **Cache Invalidation**: Automatic cache expiration
- **Storage Management**: Clean up old cache files

### **Batch Processing**
- **Multiple Symbols**: Process multiple symbols in parallel
- **Date Ranges**: Use optimal date ranges for efficiency
- **Resource Management**: Monitor API usage and costs

## 🎯 **Next Steps**

1. **Test with Different Symbols**: Try popular stocks like AAPL, TSLA, NVDA
2. **Experiment with Date Ranges**: Test different historical periods
3. **Integrate with Backtesting**: Use historical data in your backtesting framework
4. **Optimize Performance**: Fine-tune caching and batch processing
5. **Monitor Costs**: Track API usage and optimize for cost efficiency

## 📚 **Additional Resources**

- [Apify Documentation](https://docs.apify.com/)
- [Apify Store](https://apify.com/store)
- [Reddit Scraper Examples](https://apify.com/store?search=reddit)
- [Apify API Reference](https://docs.apify.com/api/v2)
- [Apify Pricing](https://apify.com/pricing)

---

**Happy Historical Analysis! 📊** 