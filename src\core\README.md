# 🏗️ Core Architecture Components

> **Modern foundation for the AI-Nvestor trading platform**

## 🌟 **Overview**

The `core/` module contains the fundamental architectural components that power the AI-Nvestor platform. These components implement modern software engineering patterns including dependency injection, centralized error handling, and a robust, async-first API client.

## 🔧 **Components**

### **🏭 `service_factory.py` - Dependency Injection**
> **Centralized service creation and lifecycle management.**

- **Service Container Pattern**: Manages dependencies between services cleanly.
- **Lazy Initialization**: Services are created only when needed.
- **Lifecycle Management**: Ensures proper cleanup of resources.
- **Configuration Sharing**: A single source of truth for configuration is passed to all services.

```python
# Initialize factory with configuration
factory = ServiceFactory(config_manager)

# Create service container with all dependencies
services = factory.create_container()

# Access services through container
market_data = services.market_data
ai_advisor = services.ai_advisor
```

---

### **🌐 `base_client.py` - Async API Client**
> **Base class for all API interactions with rate limiting and retry logic.**

- **Asynchronous by Default**: Built on `aiohttp` for non-blocking I/O.
- **Rate Limiting**: Uses an efficient `deque`-based rate limiter to prevent API bans.
- **Automatic Retries**: Implements exponential backoff for transient network errors.
- **Connection Pooling**: Reuses connections for better performance.

```python
class MyApiClient(AsyncBaseAPIClient):
    def __init__(self, config):
        super().__init__(
            base_url="https://api.example.com",
            rate_limit_requests=100,
            rate_limit_window=60
        )
```

---

### **🚨 `exceptions.py` - Exception Hierarchy**
> **Comprehensive custom exception classes for better error handling.**

- **TradingPlatformError**: The base for all custom exceptions.
- **Specific Errors**: `MarketDataError`, `ConfigurationError`, `APIError`, etc., allow for granular error handling.

```python
from src.core.exceptions import MarketDataError

try:
    data = fetch_market_data(symbol)
except MarketDataError as e:
    logger.error(f"Could not fetch market data: {e}")
    # Implement fallback logic
```

---

### **📝 `error_handler.py` - Centralized Error Management**
> **Global error handling with recovery strategies and comprehensive logging.**

- **Standardized Error Patterns**: Consistent error handling across the application.
- **Automatic Error Recovery**: Define strategies to recover from specific, known errors.
- **Decorator-Based**: A simple `@handle_trading_error` decorator can wrap any function.

```python
from src.core.error_handler import handle_trading_error

@handle_trading_error("symbol_analysis")
def analyze_symbol(symbol):
    # Your code here - any exceptions will be caught, logged,
    # and handled by the global error handler.
    ...
```
