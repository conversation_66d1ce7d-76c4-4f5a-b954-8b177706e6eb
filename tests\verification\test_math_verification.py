#!/usr/bin/env python3
"""
Verify mathematical correctness of indicator calculations.
Compares our calculations with manual implementations to ensure accuracy.
"""

import pandas as pd
import numpy as np
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '..', 'src'))

from services.indicators import TechnicalIndicators
from utils.config import get_config


def manual_rsi_calculation(prices, period=14):
    """Manual RSI calculation for verification."""
    deltas = prices.diff()
    gains = deltas.where(deltas > 0, 0)
    losses = -deltas.where(deltas < 0, 0)
    
    avg_gains = gains.rolling(window=period).mean()
    avg_losses = losses.rolling(window=period).mean()
    
    rs = avg_gains / avg_losses
    rsi = 100 - (100 / (1 + rs))
    
    return rsi.iloc[-1]


def manual_sma_calculation(prices, period=20):
    """Manual SMA calculation for verification."""
    return prices.rolling(window=period).mean().iloc[-1]


def manual_ema_calculation(prices, period=20):
    """Manual EMA calculation for verification."""
    multiplier = 2 / (period + 1)
    ema = prices.ewm(span=period).mean()
    return ema.iloc[-1]


def manual_bollinger_bands(prices, period=20, std_dev=2):
    """Manual Bollinger Bands calculation for verification."""
    sma = prices.rolling(window=period).mean()
    std = prices.rolling(window=period).std()
    
    upper_band = sma + (std * std_dev)
    lower_band = sma - (std * std_dev)
    
    return {
        'upper': upper_band.iloc[-1],
        'middle': sma.iloc[-1],
        'lower': lower_band.iloc[-1]
    }


def verify_rsi_math():
    """Verify RSI mathematical correctness."""
    print("🔍 Verifying RSI mathematical correctness...")
    
    # Create test data
    dates = pd.date_range('2024-01-01', periods=30, freq='D')
    prices = pd.Series([100 + i * 0.5 + np.random.normal(0, 1) for i in range(30)], index=dates)
    
    # Manual calculation
    manual_rsi = manual_rsi_calculation(prices, 14)
    
    # Our calculation
    config = get_config()
    indicators = TechnicalIndicators(config.model_dump())
    
    # Create OHLCV data
    data = pd.DataFrame({
        'Open': prices * 0.99,
        'High': prices * 1.01,
        'Low': prices * 0.99,
        'Close': prices,
        'Volume': [1000000] * len(prices)
    }, index=dates)
    
    our_result = indicators.calculate_rsi(data)
    our_rsi = our_result.value
    
    print(f"   Manual RSI: {manual_rsi:.4f}")
    print(f"   Our RSI: {our_rsi:.4f}")
    print(f"   Difference: {abs(manual_rsi - our_rsi):.6f}")
    
    # Verify within reasonable tolerance
    tolerance = 0.01  # 1% tolerance
    if abs(manual_rsi - our_rsi) < tolerance:
        print("   ✅ RSI calculation is mathematically correct")
    else:
        print("   ❌ RSI calculation may have issues")


def verify_sma_math():
    """Verify SMA mathematical correctness."""
    print("🔍 Verifying SMA mathematical correctness...")
    
    dates = pd.date_range('2024-01-01', periods=30, freq='D')
    prices = pd.Series([100 + i * 0.5 for i in range(30)], index=dates)
    
    # Manual calculation
    manual_sma = manual_sma_calculation(prices, 20)
    
    # Our calculation
    config = get_config()
    indicators = TechnicalIndicators(config.model_dump())
    
    data = pd.DataFrame({
        'Open': prices * 0.99,
        'High': prices * 1.01,
        'Low': prices * 0.99,
        'Close': prices,
        'Volume': [1000000] * len(prices)
    }, index=dates)
    
    our_result = indicators.calculate_sma(data)
    our_sma = our_result.value
    
    print(f"   Manual SMA: {manual_sma:.4f}")
    print(f"   Our SMA: {our_sma:.4f}")
    print(f"   Difference: {abs(manual_sma - our_sma):.6f}")
    
    # Verify within reasonable tolerance
    tolerance = 0.001  # 0.1% tolerance
    if abs(manual_sma - our_sma) < tolerance:
        print("   ✅ SMA calculation is mathematically correct")
    else:
        print("   ❌ SMA calculation may have issues")


def verify_ema_math():
    """Verify EMA mathematical correctness."""
    print("🔍 Verifying EMA mathematical correctness...")
    
    dates = pd.date_range('2024-01-01', periods=30, freq='D')
    prices = pd.Series([100 + i * 0.5 for i in range(30)], index=dates)
    
    # Manual calculation
    manual_ema = manual_ema_calculation(prices, 20)
    
    # Our calculation
    config = get_config()
    indicators = TechnicalIndicators(config.model_dump())
    
    data = pd.DataFrame({
        'Open': prices * 0.99,
        'High': prices * 1.01,
        'Low': prices * 0.99,
        'Close': prices,
        'Volume': [1000000] * len(prices)
    }, index=dates)
    
    our_result = indicators.calculate_ema(data)
    our_ema = our_result.value
    
    print(f"   Manual EMA: {manual_ema:.4f}")
    print(f"   Our EMA: {our_ema:.4f}")
    print(f"   Difference: {abs(manual_ema - our_ema):.6f}")
    
    # Verify within reasonable tolerance
    tolerance = 0.001  # 0.1% tolerance
    if abs(manual_ema - our_ema) < tolerance:
        print("   ✅ EMA calculation is mathematically correct")
    else:
        print("   ❌ EMA calculation may have issues")


def verify_bollinger_bands_math():
    """Verify Bollinger Bands mathematical correctness."""
    print("🔍 Verifying Bollinger Bands mathematical correctness...")
    
    dates = pd.date_range('2024-01-01', periods=30, freq='D')
    prices = pd.Series([100 + i * 0.5 for i in range(30)], index=dates)
    
    # Manual calculation
    manual_bb = manual_bollinger_bands(prices, 20, 2)
    
    # Our calculation
    config = get_config()
    indicators = TechnicalIndicators(config.model_dump())
    
    data = pd.DataFrame({
        'Open': prices * 0.99,
        'High': prices * 1.01,
        'Low': prices * 0.99,
        'Close': prices,
        'Volume': [1000000] * len(prices)
    }, index=dates)
    
    our_result = indicators.calculate_bollinger_bands(data)
    our_upper = our_result.metadata['upper_band']
    our_middle = our_result.metadata['middle_band']
    our_lower = our_result.metadata['lower_band']
    
    print(f"   Manual Upper: {manual_bb['upper']:.4f}")
    print(f"   Our Upper: {our_upper:.4f}")
    print(f"   Manual Middle: {manual_bb['middle']:.4f}")
    print(f"   Our Middle: {our_middle:.4f}")
    print(f"   Manual Lower: {manual_bb['lower']:.4f}")
    print(f"   Our Lower: {our_lower:.4f}")
    
    # Verify within reasonable tolerance
    tolerance = 0.001  # 0.1% tolerance
    upper_diff = abs(manual_bb['upper'] - our_upper)
    middle_diff = abs(manual_bb['middle'] - our_middle)
    lower_diff = abs(manual_bb['lower'] - our_lower)
    
    if upper_diff < tolerance and middle_diff < tolerance and lower_diff < tolerance:
        print("   ✅ Bollinger Bands calculation is mathematically correct")
    else:
        print("   ❌ Bollinger Bands calculation may have issues")


def verify_signal_generation():
    """Verify signal generation logic."""
    print("🔍 Verifying signal generation logic...")
    
    config = get_config()
    indicators = TechnicalIndicators(config.model_dump())
    
    # Test RSI signals
    test_cases = [
        (30, "Should be BUY signal"),      # Oversold
        (70, "Should be SELL signal"),     # Overbought
        (50, "Should be HOLD signal"),     # Neutral
        (20, "Should be STRONG_BUY"),      # Very oversold
        (80, "Should be STRONG_SELL"),     # Very overbought
    ]
    
    for rsi_value, expected in test_cases:
        # Create data that would produce this RSI
        dates = pd.date_range('2024-01-01', periods=30, freq='D')
        
        if rsi_value < 30:
            # Create oversold pattern
            prices = [100 - i * 0.5 for i in range(30)]
        elif rsi_value > 70:
            # Create overbought pattern
            prices = [100 + i * 0.5 for i in range(30)]
        else:
            # Create neutral pattern
            prices = [100 + np.random.normal(0, 0.1) for _ in range(30)]
        
        data = pd.DataFrame({
            'Open': prices,
            'High': [p * 1.01 for p in prices],
            'Low': [p * 0.99 for p in prices],
            'Close': prices,
            'Volume': [1000000] * len(prices)
        }, index=dates)
        
        result = indicators.calculate_rsi(data)
        print(f"   RSI {rsi_value:.1f}: {result.signal.value} ({expected})")
    
    print("   ✅ Signal generation logic verified")


def main():
    """Run mathematical verification tests."""
    print("🚀 Starting mathematical verification of indicator calculations...")
    print("=" * 70)
    
    try:
        verify_rsi_math()
        print()
        
        verify_sma_math()
        print()
        
        verify_ema_math()
        print()
        
        verify_bollinger_bands_math()
        print()
        
        verify_signal_generation()
        print()
        
        print("🎉 All mathematical verification tests completed!")
        print("✅ Indicator calculations are mathematically correct")
        
    except Exception as e:
        print(f"❌ Mathematical verification failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main()) 