# Integration Tests

This folder contains integration tests for the AI-Nvestor system.

## Test Structure

### `test_reddit_sentiment.py`
**Purpose**: Integration tests for Reddit sentiment analysis functionality.

**Features**:
- ✅ **Reddit Sentiment Fetching**: Tests real-time Reddit sentiment data collection
- ✅ **Caching**: Tests sentiment data caching functionality
- ✅ **Sentiment Analysis**: Tests keyword-based sentiment analysis accuracy
- ✅ **Service Integration**: Tests integration with main sentiment service
- ✅ **Manual Testing**: Includes manual test function for interactive testing

**Usage**:
```bash
# Run automated tests
pytest tests/integration/test_reddit_sentiment.py -v

# Run manual test
python tests/integration/test_reddit_sentiment.py
```

### `test_apify_sentiment.py`
**Purpose**: Integration tests for Apify historical Reddit sentiment analysis.

**Features**:
- ✅ **Apify Service Initialization**: Tests service setup and configuration
- ✅ **Historical Data Fetching**: Tests historical Reddit data collection
- ✅ **Backtesting Data Format**: Tests data formatting for backtesting
- ✅ **Sentiment Analysis**: Tests sentiment analysis accuracy
- ✅ **Caching**: Tests historical data caching
- ✅ **Manual Testing**: Includes manual test function for interactive testing

**Usage**:
```bash
# Run automated tests
pytest tests/integration/test_apify_sentiment.py -v

# Run manual test
python tests/integration/test_apify_sentiment.py
```

## Test Configuration

### Prerequisites
- Reddit API credentials (optional, for better results)
- Apify API credentials (optional, for historical data)
- Python packages: `pytest`, `pytest-asyncio`

### Environment Setup
1. **Copy environment template**:
   ```bash
   cp env.example .env
   ```

2. **Configure credentials** (optional):
   ```bash
   # Reddit API (for better results)
   REDDIT_CLIENT_ID=your_client_id
   REDDIT_CLIENT_SECRET=your_client_secret
   REDDIT_USERNAME=your_username
   REDDIT_PASSWORD=your_password
   
   # Apify API (for historical data)
   APIFY_API_TOKEN=your_apify_api_token
   APIFY_REDDIT_SCRAPER_ID=your_reddit_scraper_id
   ```

## Running Tests

### Individual Tests
```bash
# Reddit sentiment tests
python tests/integration/test_reddit_sentiment.py

# Apify sentiment tests
python tests/integration/test_apify_sentiment.py
```

### All Integration Tests
```bash
# Run all integration tests
pytest tests/integration/ -v

# Run with coverage
pytest tests/integration/ --cov=src --cov-report=html
```

### Specific Test Classes
```bash
# Run specific test class
pytest tests/integration/test_reddit_sentiment.py::TestRedditSentiment -v

# Run specific test method
pytest tests/integration/test_reddit_sentiment.py::TestRedditSentiment::test_reddit_sentiment_fetching -v
```

## Test Data

### Reddit Sentiment Test Data
- **Symbols**: AAPL, TSLA, NVDA
- **Time Range**: 24 hours
- **Subreddits**: stocks, investing, wallstreetbets, stockmarket, options, daytrading, investments, financialindependence
- **Data Types**: Posts and comments
- **Metrics**: Sentiment scores, confidence, metadata

### Apify Historical Test Data
- **Symbols**: AAPL, TSLA, NVDA
- **Time Range**: 2024-01-01 to 2024-01-31
- **Data Types**: Historical posts and comments
- **Formats**: Raw sentiment data and backtesting DataFrame

## Expected Results

### Reddit Sentiment Tests
- ✅ **Data Fetching**: Successfully fetch sentiment data from Reddit
- ✅ **Caching**: Properly cache and retrieve sentiment data
- ✅ **Analysis**: Accurate sentiment scoring (positive/negative detection)
- ✅ **Integration**: Seamless integration with main sentiment service

### Apify Historical Tests
- ✅ **Service Setup**: Proper Apify service initialization
- ✅ **Historical Data**: Successfully fetch historical sentiment data
- ✅ **Backtesting Format**: Correct DataFrame format for backtesting
- ✅ **Caching**: Proper historical data caching

## Troubleshooting

### Common Issues

1. **Rate Limiting**:
   - Reddit JSON API has rate limits (30 req/min)
   - Tests may be interrupted due to rate limiting
   - Consider setting up Reddit credentials for higher limits

2. **Missing Credentials**:
   - Tests will use fallback methods when credentials are missing
   - Some tests may be skipped or return limited data

3. **Network Issues**:
   - Tests require internet connection
   - Some tests may fail due to network issues

### Debug Mode
```bash
# Enable debug logging
export LOGGING_LEVEL=DEBUG
python tests/integration/test_reddit_sentiment.py
```

## Contributing

When adding new integration tests:

1. **Follow Naming Convention**: `test_<service>_<feature>.py`
2. **Include Both Automated and Manual Tests**: Automated for CI/CD, manual for interactive testing
3. **Add Proper Documentation**: Include docstrings and README updates
4. **Handle Errors Gracefully**: Tests should handle missing credentials and network issues
5. **Use Fixtures**: Use pytest fixtures for service initialization and cleanup

## Dependencies

- `pytest`: Testing framework
- `pytest-asyncio`: Async test support
- `httpx`: HTTP client for API testing
- `pandas`: Data manipulation for backtesting tests
- `praw`: Reddit API client (optional) 