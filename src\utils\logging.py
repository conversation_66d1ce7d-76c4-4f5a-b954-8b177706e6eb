"""
Advanced logging system for the AI-Nvestor trading platform.

This module provides comprehensive logging capabilities including:
- Structured logging with JSON formatting
- Performance metrics collection
- Error tracking and alerting
- Log rotation and archival
- Real-time log streaming
"""

import json
import sys
import traceback
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Optional, Union
from loguru import logger
import psutil
import os


class TradingLogger:
    """
    Professional logging system for algorithmic trading.
    
    Features:
    - Structured JSON logging
    - Performance monitoring
    - Error tracking with stack traces
    - Log rotation and compression
    - Real-time metrics collection
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the trading logger.
        
        Args:
            config: Logging configuration dictionary
        """
        self.config = config
        self.setup_logging()
        
    def setup_logging(self):
        """Configure the logging system with professional settings."""
        logger.remove()
        
        log_path = Path(self.config.get('file', 'logs/trading.log'))
        log_path.parent.mkdir(parents=True, exist_ok=True)

        # Use a simpler format if the output is not a TTY (e.g., redirected to a file)
        is_tty = sys.stdout.isatty()
        
        console_format = (
            "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
            "<level>{level: <8}</level> | "
            "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
            "<level>{message}</level>"
        ) if is_tty else (
            "{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}"
        )

        logger.add(
            sys.stdout,
            format=console_format,
            level=self.config.get('level', 'INFO'),
            colorize=is_tty  # Only colorize if it's a TTY
        )
        
        # File handler with JSON formatting
        logger.add(
            log_path,
            format="{message}",
            level=self.config.get('level', 'INFO'),
            rotation=self.config.get('max_size', '10MB'),
            retention=self.config.get('backup_count', 5),
            compression="zip",
            serialize=True
        )
        
        error_log_path = log_path.parent / 'errors.log'
        logger.add(
            error_log_path,
            format="{message}",
            level="ERROR",
            rotation="10MB",
            retention=10,
            compression="zip",
            serialize=True
        )
        
    def log_trade(self, symbol: str, action: str, quantity: float, price: float, 
                  timestamp: Optional[datetime] = None):
        """
        Log a trading action with structured data.
        
        Args:
            symbol: Stock symbol
            action: Buy/Sell action
            quantity: Number of shares
            price: Execution price
            timestamp: Trade timestamp
        """
        trade_data = {
            "event_type": "trade",
            "symbol": symbol,
            "action": action,
            "quantity": quantity,
            "price": price,
            "timestamp": timestamp or datetime.now(),
            "total_value": quantity * price
        }
        
        logger.info(f"Trade executed: {json.dumps(trade_data, default=str)}")
        
    def log_signal(self, symbol: str, signal_type: str, confidence: float, 
                   indicators: Dict[str, float]):
        """
        Log a trading signal with indicator values.
        
        Args:
            symbol: Stock symbol
            signal_type: Type of signal (BUY/SELL/HOLD)
            confidence: Signal confidence (0-1)
            indicators: Dictionary of indicator values
        """
        signal_data = {
            "event_type": "signal",
            "symbol": symbol,
            "signal_type": signal_type,
            "confidence": confidence,
            "indicators": indicators,
            "timestamp": datetime.now()
        }
        
        logger.info(f"Signal generated: {json.dumps(signal_data, default=str)}")
        
    def log_performance(self, metrics: Dict[str, float]):
        """
        Log performance metrics.
        
        Args:
            metrics: Dictionary of performance metrics
        """
        performance_data = {
            "event_type": "performance",
            "metrics": metrics,
            "timestamp": datetime.now(),
            "system_info": self._get_system_info()
        }
        
        logger.info(f"Performance update: {json.dumps(performance_data, default=str)}")
        
    def log_error(self, error: Exception, context: Optional[Dict[str, Any]] = None):
        """
        Log an error with full stack trace and context.
        
        Args:
            error: The exception that occurred
            context: Additional context information
        """
        error_data = {
            "event_type": "error",
            "error_type": type(error).__name__,
            "error_message": str(error),
            "stack_trace": traceback.format_exc(),
            "context": context or {},
            "timestamp": datetime.now(),
            "system_info": self._get_system_info()
        }
        
        logger.error(f"Error occurred: {json.dumps(error_data, default=str)}")
        
    def log_market_data(self, symbol: str, data: Dict[str, Any]):
        """
        Log market data updates.
        
        Args:
            symbol: Stock symbol
            data: Market data dictionary
        """
        market_data = {
            "event_type": "market_data",
            "symbol": symbol,
            "data": data,
            "timestamp": datetime.now()
        }
        
        logger.debug(f"Market data update: {json.dumps(market_data, default=str)}")
        
    def _get_system_info(self) -> Dict[str, Any]:
        """Get current system information for logging."""
        return {
            "cpu_percent": psutil.cpu_percent(),
            "memory_percent": psutil.virtual_memory().percent,
            "disk_percent": psutil.disk_usage('/').percent,
            "process_id": os.getpid()
        }
        
    def log_startup(self):
        """Log system startup information."""
        startup_data = {
            "event_type": "startup",
            "timestamp": datetime.now(),
            "system_info": self._get_system_info(),
            "config": self.config
        }
        
        logger.info(f"System startup: {json.dumps(startup_data, default=str)}")
        
    def log_shutdown(self):
        """Log system shutdown information."""
        shutdown_data = {
            "event_type": "shutdown",
            "timestamp": datetime.now(),
            "system_info": self._get_system_info()
        }
        
        logger.info(f"System shutdown: {json.dumps(shutdown_data, default=str)}")
    
    # Standard logging methods for compatibility
    def info(self, message: str):
        """Log an info message."""
        logger.info(message)
    
    def warning(self, message: str):
        """Log a warning message."""
        logger.warning(message)
    
    def error(self, message: str):
        """Log an error message."""
        logger.error(message)
    
    def debug(self, message: str):
        """Log a debug message."""
        logger.debug(message)
    
    def critical(self, message: str):
        """Log a critical message."""
        logger.critical(message)


# Global logger instance
trading_logger: Optional[TradingLogger] = None


def get_logger() -> TradingLogger:
    """Get the global trading logger instance."""
    global trading_logger
    if trading_logger is None:
        # Create a default logger if none exists
        default_config = {
            'level': 'INFO',
            'file': 'logs/trading.log',
            'max_size': '10MB',
            'backup_count': 5
        }
        trading_logger = TradingLogger(default_config)
    return trading_logger


def setup_logging(config: Dict[str, Any]) -> TradingLogger:
    """Setup the global trading logger."""
    global trading_logger
    trading_logger = TradingLogger(config)
    return trading_logger
