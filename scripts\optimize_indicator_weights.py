#!/usr/bin/env python3
"""
Indicator Weight Optimization Script

This script demonstrates how to iterate on and optimize financial indicator weights
and values using the IndicatorWeightOptimizer service.

Usage:
    python scripts/optimize_indicator_weights.py --symbols AAPL GOOGL MSFT --start-date 2024-01-01 --end-date 2024-12-31 --method hybrid
"""

import asyncio
import argparse
import json
from datetime import datetime, timedelta
from pathlib import Path
import sys

# Add src to path
sys.path.append(str(Path(__file__).parent.parent / "src"))

from utils.config import get_config
from utils.logging import get_logger
from services.indicator_weight_optimizer import IndicatorWeightOptimizer, OptimizationMethod


async def main():
    """Main function for indicator weight optimization."""
    parser = argparse.ArgumentParser(description="Optimize financial indicator weights")
    parser.add_argument("--symbols", nargs="+", default=["AAPL", "GOOGL", "MSFT", "NVDA"], 
                       help="List of symbols to optimize for")
    parser.add_argument("--start-date", default="2024-01-01", 
                       help="Start date for optimization (YYYY-MM-DD)")
    parser.add_argument("--end-date", default="2024-12-31", 
                       help="End date for optimization (YYYY-MM-DD)")
    parser.add_argument("--method", default="hybrid", 
                       choices=["machine_learning", "backtesting", "adaptive_learning", 
                               "genetic_algorithm", "bayesian_optimization", "ensemble", "hybrid"],
                       help="Optimization method to use")
    parser.add_argument("--config", default="config.json", 
                       help="Path to configuration file")
    parser.add_argument("--output-dir", default="outputs/optimization", 
                       help="Output directory for results")
    parser.add_argument("--compare-methods", action="store_true", 
                       help="Compare all optimization methods")
    
    args = parser.parse_args()
    
    # Setup logging
    logger = get_logger()
    logger.info("🚀 Starting Indicator Weight Optimization")
    
    # Load configuration
    try:
        config = get_config(args.config)
        logger.info(f"✅ Configuration loaded from {args.config}")
    except Exception as e:
        logger.error(f"❌ Failed to load configuration: {e}")
        return
    
    # Initialize optimizer
    try:
        optimizer = IndicatorWeightOptimizer(config)
        logger.info("✅ Indicator Weight Optimizer initialized")
    except Exception as e:
        logger.error(f"❌ Failed to initialize optimizer: {e}")
        return
    
    # Create output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Get current weights
    current_weights = optimizer.get_current_weights()
    logger.info("📊 Current indicator weights:")
    for indicator, weight in current_weights.items():
        logger.info(f"  {indicator}: {weight:.3f}")
    
    if args.compare_methods:
        # Compare all optimization methods
        await compare_optimization_methods(optimizer, args, output_dir)
    else:
        # Run single optimization method
        method = OptimizationMethod(args.method.upper())
        await run_single_optimization(optimizer, args, method, output_dir)
    
    # Cleanup
    await optimizer.close()
    logger.info("✅ Optimization completed")


async def compare_optimization_methods(optimizer: IndicatorWeightOptimizer, args, output_dir: Path):
    """Compare all optimization methods."""
    logger = get_logger()
    logger.info("🔍 Comparing all optimization methods...")
    
    methods = [
        OptimizationMethod.MACHINE_LEARNING,
        OptimizationMethod.BACKTESTING,
        OptimizationMethod.ADAPTIVE_LEARNING,
        OptimizationMethod.GENETIC_ALGORITHM,
        OptimizationMethod.BAYESIAN_OPTIMIZATION,
        OptimizationMethod.ENSEMBLE,
        OptimizationMethod.HYBRID
    ]
    
    results = {}
    
    for method in methods:
        logger.info(f"\n🔧 Testing {method.value} method...")
        try:
            result = await optimizer.optimize_weights(
                symbols=args.symbols,
                start_date=args.start_date,
                end_date=args.end_date,
                method=method
            )
            results[method] = result
            
            # Log results
            logger.info(f"✅ {method.value} completed:")
            logger.info(f"  - Optimization time: {result.optimization_time:.2f}s")
            logger.info(f"  - Iterations: {result.iterations}")
            logger.info(f"  - Convergence: {result.convergence}")
            
            if result.performance_metrics:
                for metric, value in result.performance_metrics.items():
                    logger.info(f"  - {metric}: {value:.4f}")
            
        except Exception as e:
            logger.error(f"❌ {method.value} failed: {e}")
            results[method] = None
    
    # Generate comparison report
    await generate_comparison_report(results, args, output_dir)


async def run_single_optimization(optimizer: IndicatorWeightOptimizer, args, method: OptimizationMethod, output_dir: Path):
    """Run a single optimization method."""
    logger = get_logger()
    logger.info(f"🔧 Running {method.value} optimization...")
    
    try:
        result = await optimizer.optimize_weights(
            symbols=args.symbols,
            start_date=args.start_date,
            end_date=args.end_date,
            method=method
        )
        
        if result.convergence:
            logger.info(f"✅ {method.value} optimization completed successfully!")
            
            # Log optimized weights
            logger.info("📊 Optimized indicator weights:")
            for indicator, weight in result.optimized_weights.items():
                logger.info(f"  {indicator}: {weight:.3f}")
            
            # Log performance metrics
            if result.performance_metrics:
                logger.info("📈 Performance metrics:")
                for metric, value in result.performance_metrics.items():
                    logger.info(f"  {metric}: {value:.4f}")
            
            # Save results
            await save_optimization_results(result, args, output_dir)
            
            # Update optimizer weights
            optimizer.update_weights(result.optimized_weights)
            logger.info("💾 Updated optimizer weights")
            
        else:
            logger.warning(f"⚠️ {method.value} optimization did not converge")
            
    except Exception as e:
        logger.error(f"❌ {method.value} optimization failed: {e}")


async def generate_comparison_report(results: dict, args, output_dir: Path):
    """Generate a comparison report for all optimization methods."""
    logger = get_logger()
    logger.info("📊 Generating comparison report...")
    
    # Filter out failed results
    valid_results = {k: v for k, v in results.items() if v is not None and v.convergence}
    
    if not valid_results:
        logger.warning("⚠️ No valid results to compare")
        return
    
    # Create comparison data
    comparison_data = {
        'timestamp': datetime.now().isoformat(),
        'symbols': args.symbols,
        'start_date': args.start_date,
        'end_date': args.end_date,
        'methods': {}
    }
    
    for method, result in valid_results.items():
        comparison_data['methods'][method.value] = {
            'optimization_time': result.optimization_time,
            'iterations': result.iterations,
            'performance_metrics': result.performance_metrics,
            'optimized_weights': result.optimized_weights,
            'feature_importance': result.feature_importance
        }
    
    # Save comparison report
    report_file = output_dir / f"optimization_comparison_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, 'w') as f:
        json.dump(comparison_data, f, indent=2, default=str)
    
    logger.info(f"💾 Comparison report saved: {report_file}")
    
    # Print summary
    print("\n" + "="*80)
    print("📊 OPTIMIZATION METHOD COMPARISON")
    print("="*80)
    
    for method, result in valid_results.items():
        print(f"\n🔧 {method.value.upper()}:")
        print(f"  Optimization time: {result.optimization_time:.2f}s")
        print(f"  Iterations: {result.iterations}")
        
        if result.performance_metrics:
            for metric, value in result.performance_metrics.items():
                print(f"  {metric}: {value:.4f}")
        
        # Show top 5 optimized weights
        if result.optimized_weights:
            sorted_weights = sorted(result.optimized_weights.items(), key=lambda x: x[1], reverse=True)
            print("  Top 5 optimized weights:")
            for indicator, weight in sorted_weights[:5]:
                print(f"    {indicator}: {weight:.3f}")


async def save_optimization_results(result, args, output_dir: Path):
    """Save optimization results to file."""
    logger = get_logger()
    
    # Create results data
    results_data = {
        'timestamp': datetime.now().isoformat(),
        'method': result.method.value,
        'symbols': args.symbols,
        'start_date': args.start_date,
        'end_date': args.end_date,
        'optimization_time': result.optimization_time,
        'iterations': result.iterations,
        'convergence': result.convergence,
        'optimized_weights': result.optimized_weights,
        'performance_metrics': result.performance_metrics,
        'feature_importance': result.feature_importance,
        'metadata': result.metadata
    }
    
    # Save to file
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"optimization_result_{result.method.value}_{timestamp}.json"
    filepath = output_dir / filename
    
    with open(filepath, 'w') as f:
        json.dump(results_data, f, indent=2, default=str)
    
    logger.info(f"💾 Results saved: {filepath}")


def print_usage_examples():
    """Print usage examples."""
    print("\n" + "="*80)
    print("📖 USAGE EXAMPLES")
    print("="*80)
    
    examples = [
        {
            "description": "Run hybrid optimization on default symbols",
            "command": "python scripts/optimize_indicator_weights.py"
        },
        {
            "description": "Run machine learning optimization on specific symbols",
            "command": "python scripts/optimize_indicator_weights.py --symbols AAPL GOOGL --method machine_learning"
        },
        {
            "description": "Compare all optimization methods",
            "command": "python scripts/optimize_indicator_weights.py --compare-methods --symbols AAPL GOOGL MSFT"
        },
        {
            "description": "Run backtesting optimization with custom date range",
            "command": "python scripts/optimize_indicator_weights.py --start-date 2023-01-01 --end-date 2023-12-31 --method backtesting"
        },
        {
            "description": "Run genetic algorithm optimization with custom output directory",
            "command": "python scripts/optimize_indicator_weights.py --method genetic_algorithm --output-dir outputs/genetic_opt"
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n{i}. {example['description']}")
        print(f"   {example['command']}")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⚠️ Optimization interrupted by user")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        print_usage_examples()


