#!/usr/bin/env python3
"""
AI-Nvestor Platform Demo

This script demonstrates the enhanced capabilities of the AI-Nvestor trading platform,
showcasing all the new features and improvements.
"""

import sys
import os
from datetime import datetime, timedelta
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def demo_basic_analysis():
    """Demonstrate basic market analysis."""
    print("\n" + "="*60)
    print("📊 BASIC MARKET ANALYSIS DEMO")
    print("="*60)
    
    try:
        from utils.config import get_config
        from services.market_data import MarketDataService
        from services.indicators import TechnicalIndicators
        
        config = get_config()
        market_data = MarketDataService(config.model_dump())
        indicators = TechnicalIndicators(config.model_dump())
        
        # Analyze a few symbols
        symbols = ['AAPL', 'GOOGL', 'MSFT']
        
        for symbol in symbols:
            print(f"\n🔍 Analyzing {symbol}...")
            
            # Get market data
            data = market_data.get_stock_data(symbol, interval='1d', days=30)
            
            if data is not None and not data.empty:
                # Calculate indicators
                results = indicators.calculate_all_indicators(data)
                
                # Generate consensus signal
                signal, confidence = indicators.generate_consensus_signal(results)
                
                current_price = data['Close'].iloc[-1]
                price_change = ((current_price - data['Close'].iloc[-2]) / data['Close'].iloc[-2]) * 100
                
                print(f"   💰 Current Price: ${current_price:.2f} ({price_change:+.2f}%)")
                print(f"   📈 Signal: {signal.value}")
                print(f"   🎯 Confidence: {confidence:.1%}")
                
                # Show key indicators
                key_indicators = ['rsi', 'macd', 'bollinger_bands']
                for indicator in key_indicators:
                    if indicator in results:
                        value = results[indicator].value
                        signal_type = results[indicator].signal.value
                        print(f"   📊 {indicator.upper()}: {value:.2f} ({signal_type})")
            else:
                print(f"   ❌ No data available for {symbol}")
        
        print("\n✅ Basic analysis completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Basic analysis error: {e}")
        return False

def demo_backtesting():
    """Demonstrate backtesting capabilities."""
    print("\n" + "="*60)
    print("🔄 BACKTESTING DEMO")
    print("="*60)
    
    try:
        from backtest import run_backtest
        from utils.config import get_config
        
        config = get_config()
        
        # Run backtest on multiple symbols
        symbols = ['AAPL', 'GOOGL', 'MSFT']
        start_date = (datetime.now() - timedelta(days=365)).strftime('%Y-%m-%d')
        end_date = datetime.now().strftime('%Y-%m-%d')
        
        print(f"📅 Backtesting period: {start_date} to {end_date}")
        print(f"📊 Symbols: {', '.join(symbols)}")
        
        results = run_backtest(symbols, start_date, end_date, config.model_dump())
        
        print(f"\n📈 BACKTEST RESULTS:")
        print(f"   💰 Total Return: {results.total_return:.2%}")
        print(f"   📊 Annualized Return: {results.annualized_return:.2%}")
        print(f"   ⚖️  Sharpe Ratio: {results.sharpe_ratio:.2f}")
        print(f"   📉 Max Drawdown: {results.max_drawdown:.2%}")
        print(f"   🎯 Win Rate: {results.win_rate:.2%}")
        print(f"   💸 Profit Factor: {results.profit_factor:.2f}")
        print(f"   📊 Total Trades: {results.total_trades}")
        print(f"   ✅ Winning Trades: {results.winning_trades}")
        print(f"   ❌ Losing Trades: {results.losing_trades}")
        print(f"   💰 Avg Win: ${results.avg_win:.2f}")
        print(f"   💸 Avg Loss: ${results.avg_loss:.2f}")
        
        # Show additional metrics
        if results.metrics:
            print(f"\n📊 ADDITIONAL METRICS:")
            for key, value in results.metrics.items():
                if isinstance(value, float):
                    print(f"   {key.replace('_', ' ').title()}: {value:.2f}")
                else:
                    print(f"   {key.replace('_', ' ').title()}: {value}")
        
        print("\n✅ Backtesting completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Backtesting error: {e}")
        return False

def demo_technical_indicators():
    """Demonstrate technical indicators."""
    print("\n" + "="*60)
    print("📈 TECHNICAL INDICATORS DEMO")
    print("="*60)
    
    try:
        from services.indicators import TechnicalIndicators
        from services.market_data import MarketDataService
        from utils.config import get_config
        
        config = get_config()
        indicators = TechnicalIndicators(config.model_dump())
        market_data = MarketDataService(config.model_dump())
        
        # Get data for analysis
        data = market_data.get_stock_data('AAPL', interval='1d', days=60)
        
        if data is not None and not data.empty:
            # Calculate all indicators
            results = indicators.calculate_all_indicators(data)
            
            print(f"📊 Technical Analysis for AAPL")
            print(f"📅 Data period: {data.index[0].strftime('%Y-%m-%d')} to {data.index[-1].strftime('%Y-%m-%d')}")
            print(f"📈 Data points: {len(data)}")
            
            print(f"\n🔍 INDICATOR RESULTS:")
            
            # Group indicators by type
            trend_indicators = ['sma', 'ema', 'macd']
            momentum_indicators = ['rsi', 'stochastic', 'williams_r', 'cci']
            volatility_indicators = ['bollinger_bands', 'atr', 'keltner_channels']
            volume_indicators = ['obv', 'vwap', 'money_flow_index']
            
            print(f"\n📈 TREND INDICATORS:")
            for indicator in trend_indicators:
                if indicator in results:
                    result = results[indicator]
                    print(f"   {indicator.upper()}: {result.value:.2f} ({result.signal.value})")
            
            print(f"\n⚡ MOMENTUM INDICATORS:")
            for indicator in momentum_indicators:
                if indicator in results:
                    result = results[indicator]
                    print(f"   {indicator.upper()}: {result.value:.2f} ({result.signal.value})")
            
            print(f"\n📊 VOLATILITY INDICATORS:")
            for indicator in volatility_indicators:
                if indicator in results:
                    result = results[indicator]
                    print(f"   {indicator.upper()}: {result.value:.2f} ({result.signal.value})")
            
            print(f"\n📊 VOLUME INDICATORS:")
            for indicator in volume_indicators:
                if indicator in results:
                    result = results[indicator]
                    print(f"   {indicator.upper()}: {result.value:.2f} ({result.signal.value})")
            
            # Show consensus signal
            signal, confidence = indicators.generate_consensus_signal(results)
            print(f"\n🎯 CONSENSUS SIGNAL:")
            print(f"   Signal: {signal.value}")
            print(f"   Confidence: {confidence:.1%}")
            
        else:
            print("❌ No data available for technical analysis")
            return False
        
        print("\n✅ Technical indicators demo completed!")
        return True
        
    except Exception as e:
        print(f"❌ Technical indicators error: {e}")
        return False

def demo_risk_management():
    """Demonstrate risk management features."""
    print("\n" + "="*60)
    print("🛡️ RISK MANAGEMENT DEMO")
    print("="*60)
    
    try:
        from utils.config import get_config
        
        config = get_config()
        risk_config = config.risk_management
        
        print(f"📊 RISK MANAGEMENT CONFIGURATION:")
        print(f"   💰 Max Position Size: {risk_config.max_position_size:.1%}")
        print(f"   🛑 Stop Loss: {risk_config.stop_loss_percentage:.1%}")
        print(f"   🎯 Take Profit: {risk_config.take_profit_percentage:.1%}")
        print(f"   📉 Max Daily Loss: {risk_config.max_daily_loss:.1%}")
        print(f"   ⚖️  Max Portfolio Risk: {risk_config.max_portfolio_risk:.1%}")
        print(f"   📊 Position Sizing: {risk_config.position_sizing_method}")
        
        # Demonstrate Kelly Criterion calculation
        print(f"\n🧮 POSITION SIZING EXAMPLE:")
        
        # Simulate different scenarios
        scenarios = [
            {'win_rate': 0.6, 'avg_win': 0.1, 'avg_loss': 0.05, 'name': 'Conservative'},
            {'win_rate': 0.7, 'avg_win': 0.15, 'avg_loss': 0.08, 'name': 'Moderate'},
            {'win_rate': 0.8, 'avg_win': 0.2, 'avg_loss': 0.1, 'name': 'Aggressive'}
        ]
        
        for scenario in scenarios:
            # Simplified Kelly calculation
            win_rate = scenario['win_rate']
            avg_win = scenario['avg_win']
            avg_loss = scenario['avg_loss']
            
            # Kelly Criterion: f = (bp - q) / b
            # where b = odds received, p = probability of win, q = probability of loss
            b = avg_win / avg_loss  # odds received
            p = win_rate
            q = 1 - win_rate
            
            kelly_fraction = (b * p - q) / b if b > 0 else 0
            kelly_fraction = max(0, min(kelly_fraction, 0.25))  # Cap at 25%
            
            print(f"   {scenario['name']} Strategy:")
            print(f"     Win Rate: {win_rate:.1%}")
            print(f"     Avg Win: {avg_win:.1%}")
            print(f"     Avg Loss: {avg_loss:.1%}")
            print(f"     Kelly Fraction: {kelly_fraction:.1%}")
            print()
        
        print("✅ Risk management demo completed!")
        return True
        
    except Exception as e:
        print(f"❌ Risk management error: {e}")
        return False

def demo_platform_features():
    """Demonstrate platform features."""
    print("\n" + "="*60)
    print("🚀 PLATFORM FEATURES DEMO")
    print("="*60)
    
    try:
        from utils.config import get_config
        from utils.logging import setup_logging
        from services.market_data import MarketDataService
        from services.signals import SignalGenerator
        from services.alerts import AlertSystem, AlertType, AlertPriority
        
        config = get_config()
        
        print(f"⚙️  CONFIGURATION:")
        print(f"   📊 Watchlist: {len(config.watchlist)} symbols")
        print(f"   ⏰ Timeframe: {config.timeframes.interval}")
        print(f"   📈 Data History: {config.timeframes.days_of_history} days")
        print(f"   🛡️  Paper Trading: {config.trading.paper_trading}")
        print(f"   🔔 Real-time Enabled: {config.trading.real_time_enabled}")
        
        print(f"\n📊 DATA SOURCES:")
        data_sources = config.data_sources
        print(f"   Primary: {data_sources.primary}")
        print(f"   Backup: {data_sources.backup}")
        print(f"   Real-time: {data_sources.real_time}")
        print(f"   Cache Duration: {data_sources.cache_duration}s")
        
        print(f"\n🔔 NOTIFICATIONS:")
        notifications = config.notifications
        print(f"   Email: {'✅' if notifications.email_enabled else '❌'}")
        print(f"   Slack: {'✅' if notifications.slack_enabled else '❌'}")
        print(f"   Discord: {'✅' if notifications.discord_enabled else '❌'}")
        
        print(f"\n🤖 AI ANALYSIS:")
        ai_config = config.ai_analysis
        print(f"   Enabled: {'✅' if ai_config.enabled else '❌'}")
        print(f"   Model Type: {ai_config.model_type}")
        print(f"   Confidence Threshold: {ai_config.confidence_threshold:.1%}")
        print(f"   Update Frequency: {ai_config.update_frequency}")
        
        print(f"\n📊 PERFORMANCE TARGETS:")
        performance = config.performance
        print(f"   Benchmark: {performance.benchmark}")
        print(f"   Risk-free Rate: {performance.risk_free_rate:.1%}")
        print(f"   Min Sharpe Ratio: {performance.min_sharpe_ratio}")
        print(f"   Max Drawdown: {performance.max_drawdown:.1%}")
        
        # Test market data service
        print(f"\n📊 MARKET DATA SERVICE:")
        market_data = MarketDataService(config.model_dump())
        data = market_data.get_stock_data('AAPL', interval='1d', days=5)
        if data is not None and not data.empty:
            print(f"   ✅ Data retrieval working")
            print(f"   📈 Latest AAPL price: ${data['Close'].iloc[-1]:.2f}")
        else:
            print(f"   ❌ Data retrieval failed")
        
        # Test signal generation
        print(f"\n📈 SIGNAL GENERATION:")
        signal_gen = SignalGenerator(config.model_dump())
        signals = signal_gen.generate_signals_for_watchlist(['AAPL'])
        if signals and 'AAPL' in signals and signals['AAPL'] is not None:
            signal = signals['AAPL']
            print(f"   ✅ Signal generation working")
            print(f"   📊 Signal: {signal.signal_type.value}")
            print(f"   🎯 Confidence: {signal.confidence:.1%}")
            print(f"   💪 Strength: {signal.strength.value}")
        else:
            print(f"   ❌ Signal generation failed")
        
        # Test alert system
        print(f"\n🔔 ALERT SYSTEM:")
        alert_system = AlertSystem(config.model_dump())
        success = alert_system.send_alert(
            AlertType.SYSTEM_ALERT,
            AlertPriority.MEDIUM,
            "Demo Alert",
            "This is a test alert from the AI-Nvestor platform demo.",
            metadata={'demo': True}
        )
        if success:
            print(f"   ✅ Alert system working")
        else:
            print(f"   ❌ Alert system failed")
        
        print("\n✅ Platform features demo completed!")
        return True
        
    except Exception as e:
        print(f"❌ Platform features error: {e}")
        return False

def main():
    """Run the comprehensive demo."""
    print("🚀 AI-NVESTOR PLATFORM DEMO")
    print("="*60)
    print("This demo showcases the enhanced capabilities of the AI-Nvestor platform.")
    print("="*60)
    
    demos = [
        ("Basic Analysis", demo_basic_analysis),
        ("Technical Indicators", demo_technical_indicators),
        ("Risk Management", demo_risk_management),
        ("Backtesting", demo_backtesting),
        ("Platform Features", demo_platform_features)
    ]
    
    passed = 0
    total = len(demos)
    
    for demo_name, demo_func in demos:
        try:
            if demo_func():
                passed += 1
            else:
                print(f"❌ {demo_name} failed")
        except Exception as e:
            print(f"❌ {demo_name} crashed: {e}")
    
    print("\n" + "="*60)
    print(f"📊 DEMO RESULTS: {passed}/{total} demos completed successfully")
    
    if passed == total:
        print("🎉 All demos completed successfully!")
        print("\n📋 Next steps:")
        print("   1. Run analysis: python src/main.py --mode analysis")
        print("   2. Run backtest: python src/main.py --mode backtest")
        print("   3. Start monitoring: python src/main.py --mode monitor")
        print("   4. Run tests: python test_platform.py")
        return 0
    else:
        print("⚠️ Some demos failed. Please check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 