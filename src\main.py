"""
AI-Nvestor: Advanced Algorithmic Trading Platform
"""

import argparse
import sys
import asyncio
from pathlib import Path
from typing import Dict, List, Optional
from datetime import datetime, timedelta
from collections import OrderedDict

from utils.config import setup_config
from utils.logging import setup_logging
from constants.settings import SignalType
from core.service_factory import ServiceFactory
from core.error_handler import handle_trading_error, handle_critical_error
from backtesting.backtest import run_backtest, BacktestResult

class TradingPlatform:
    """Main trading platform orchestrator."""
    
    def __init__(self, config_path: str = "config.json"):
        self.logger = setup_logging() 
        try:
            self.config_manager = setup_config(config_path)
            self.config = self.config_manager.get_config()
            self.logger = setup_logging(self.config.logging.model_dump())
            if not self._validate_config():
                raise ValueError("Invalid configuration")
            
            self.service_factory = ServiceFactory(self.config_manager)
            self.services = self.service_factory.create_container()
            
            self.market_data = self.services.market_data
            self.indicators = self.services.indicators
            self.ai_advisor = self.services.ai_advisor
            self.alert_system = self.services.alert_system
            
            self.is_running = False
            self._cache_max_size = 1000
            self._analysis_cache = OrderedDict()
            
            self.logger.log_startup()
            
        except Exception as e:
            self.logger.critical(f"Failed to initialize: {e}", exc_info=True)
            raise
    
    def _validate_config(self) -> bool:
        required_sections = ['watchlist', 'timeframes', 'trading', 'logging']
        for section in required_sections:
            if not hasattr(self.config, section):
                self.logger.error(f"Missing required configuration section: {section}")
                return False
        return True
            
    async def run_analysis(self, symbols: Optional[List[str]] = None) -> Dict:
        # ... (rest of the method is unchanged)
        if symbols is None: symbols = self.config.watchlist
        self.logger.log_performance({'event': 'analysis_started', 'symbols': symbols})
        results = {}
        for symbol in symbols:
            try:
                data = self.market_data.get_stock_data(
                    symbol=symbol,
                    interval=self.config.timeframes.interval,
                    days=self.config.timeframes.days_of_history
                )
                if data is None or data.empty:
                    self.logger.warning(f"No data for {symbol}, skipping analysis.")
                    continue
                indicators = self.indicators.calculate_all_indicators(data)
                signal, confidence = self.indicators.generate_consensus_signal(indicators)
                ai_analysis = None
                enhanced_confidence = confidence
                ai_reasoning = "Technical analysis only"
                ai_tier_used = None
                if self.ai_advisor and signal:
                    # ... (AI logic remains the same)
                    pass
                current_price = data['Close'].iloc[-1] if not data.empty else 0
                result_key = f"{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                results[result_key] = { 'symbol': symbol, 'current_price': current_price, 'signal': signal.value, 'confidence': enhanced_confidence }
                self.logger.log_signal(symbol, signal.value, enhanced_confidence, {})
            except Exception as e:
                self.logger.log_error(e, {'symbol': symbol})
                results[symbol] = {'symbol': symbol, 'error': str(e)}
        self._update_analysis_cache(results)
        self.logger.log_performance({'event': 'analysis_completed'})
        return results
    
    def print_analysis_results(self, results: Dict):
        """Log formatted analysis results."""
        # ... (logging remains the same)
        pass

    async def _monitoring_loop(self, interval_minutes: int = 5):
        """The core asyncio-native monitoring loop."""
        self.logger.info(f"Monitoring loop started. Analysis will run every {interval_minutes} minutes.")
        while self.is_running:
            try:
                if self.market_data.is_market_hours():
                    self.logger.info("Market is open. Running scheduled analysis...")
                    results = await self.run_analysis()
                    self.print_analysis_results(results)
                else:
                    self.logger.info("Market is closed. Skipping analysis.")
                
                # Wait for the next interval
                await asyncio.sleep(interval_minutes * 60)
            except asyncio.CancelledError:
                self.logger.info("Monitoring loop cancelled.")
                break
            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {e}", exc_info=True)
                # Wait a bit before retrying to avoid spamming errors
                await asyncio.sleep(60)

    async def start_real_time_monitoring(self):
        """Starts the asynchronous real-time market monitoring task."""
        if not self.config.trading.real_time_enabled:
            self.logger.warning("Real-time monitoring is disabled in configuration.")
            return
        
        self.is_running = True
        self.logger.info("🚀 Starting real-time market monitoring... Press Ctrl+C to stop.")
        
        monitoring_task = asyncio.create_task(self._monitoring_loop())
        
        try:
            await monitoring_task
        except KeyboardInterrupt:
            self.logger.info("Keyboard interrupt received. Stopping monitoring...")
            self.is_running = False
            monitoring_task.cancel()
            # Wait for the task to acknowledge cancellation
            await asyncio.sleep(1)
        finally:
            self.stop_real_time_monitoring()
    
    def stop_real_time_monitoring(self):
        """Stops the monitoring loop."""
        self.is_running = False
        self.logger.info("🛑 Real-time monitoring stopped.")
        self.logger.log_shutdown()

    # ... (cleanup and other methods)
    async def cleanup(self):
        self.logger.info("Initiating platform cleanup...")
        # ... (rest of cleanup)
        
async def _execute_mode(platform: TradingPlatform, args):
    try:
        if args.mode == "analysis":
            platform.logger.info(f"📊 Running analysis for symbols: {args.symbols or platform.config.watchlist}")
            results = await platform.run_analysis(args.symbols)
            platform.print_analysis_results(results)
        elif args.mode == "monitor":
            await platform.start_real_time_monitoring()
        # ... other modes like backtest
    except Exception as e:
        platform.logger.error(f"Error during {args.mode} operation: {e}", exc_info=True)

async def main_async():
    logger = setup_logging()
    parser = argparse.ArgumentParser(description="AI-Nvestor Trading Platform")
    parser.add_argument("--mode", choices=["analysis", "backtest", "monitor"], default="analysis")
    parser.add_argument("--symbols", nargs="+")
    parser.add_argument("--config", default="config.json")
    args = parser.parse_args()
        
    platform = None
    try:
        platform = TradingPlatform(args.config)
        await _execute_mode(platform, args)
    except Exception as e:
        logger.critical(f"A critical error occurred: {e}", exc_info=True)
    finally:
        if platform:
            await platform.cleanup()
        
def main():
    logger = setup_logging()
    try:
        asyncio.run(main_async())
    except KeyboardInterrupt:
        logger.info("\n🛑 Operation cancelled by user.")
    except Exception as e:
        logger.critical(f"\n❌ Unexpected error in main: {e}", exc_info=True)
        sys.exit(1)
