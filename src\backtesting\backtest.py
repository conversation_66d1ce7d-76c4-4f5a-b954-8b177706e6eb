"""
Advanced backtesting engine for AI-Nvestor.

This module provides:
- Historical strategy validation
- Performance metrics calculation
- Risk analysis and drawdown tracking
- Portfolio simulation
- Strategy comparison and optimization
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Union, Callable
from dataclasses import dataclass
from enum import Enum
import yfinance as yf
from pathlib import Path
import json

from utils.config import get_config
from utils.logging import get_logger
from services.indicators import TechnicalIndicators, SignalType, IndicatorResult


class TradeType(Enum):
    """Trade types for backtesting."""
    BUY = "BUY"
    SELL = "SELL"
    HOLD = "HOLD"


@dataclass
class Trade:
    """Trade record for backtesting."""
    symbol: str
    timestamp: datetime
    trade_type: TradeType
    quantity: float
    price: float
    value: float
    signal_strength: float
    indicators: Dict[str, float]


@dataclass
class Position:
    """Position tracking for backtesting."""
    symbol: str
    quantity: float
    avg_price: float
    current_value: float
    unrealized_pnl: float
    entry_date: datetime


@dataclass
class BacktestResult:
    """Results of a backtest run."""
    total_return: float
    annualized_return: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    profit_factor: float
    total_trades: int
    winning_trades: int
    losing_trades: int
    avg_win: float
    avg_loss: float
    trades: List[Trade]
    equity_curve: pd.Series
    positions: List[Position]
    metrics: Dict[str, float]


class BacktestEngine:
    """
    Professional backtesting engine for strategy validation.
    
    Features:
    - Historical data simulation
    - Realistic trading costs and slippage
    - Multiple strategy support
    - Comprehensive performance metrics
    - Risk analysis and reporting
    """
    
    def __init__(self, config: Dict, initial_capital: float = 100000):
        """
        Initialize the backtesting engine.
        
        Args:
            config: Configuration dictionary
            initial_capital: Starting capital for backtesting
        """
        self.config = config
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.positions: Dict[str, Position] = {}
        self.trades: List[Trade] = []
        self.equity_curve = []
        self.logger = get_logger()
        
        # Risk management parameters
        self.risk_config = config.get('risk_management', {})
        self.max_position_size = self.risk_config.get('max_position_size', 0.02)
        self.stop_loss = self.risk_config.get('stop_loss_percentage', 0.05)
        self.take_profit = self.risk_config.get('take_profit_percentage', 0.10)
        
        # Trading costs
        self.commission_rate = 0.005  # 0.5% commission
        self.slippage = 0.001  # 0.1% slippage
        
        # Technical indicators
        self.indicators = TechnicalIndicators(config)
        
    def run_backtest(self, symbols: List[str], start_date: str, end_date: str, 
                    strategy_func: Optional[Callable] = None) -> BacktestResult:
        """
        Run a complete backtest.
        
        Args:
            symbols: List of symbols to backtest
            start_date: Start date for backtest (YYYY-MM-DD)
            end_date: End date for backtest (YYYY-MM-DD)
            strategy_func: Custom strategy function
            
        Returns:
            Backtest results
        """
        self.logger.log_performance({
            'event': 'backtest_started',
            'symbols': symbols,
            'start_date': start_date,
            'end_date': end_date,
            'initial_capital': self.initial_capital
        })
        
        # Load historical data
        data = self._load_historical_data(symbols, start_date, end_date)
        
        # Run strategy
        if strategy_func:
            self._run_custom_strategy(data, strategy_func)
        else:
            self._run_momentum_strategy(data)
        
        # Calculate results
        results = self._calculate_results()
        
        self.logger.log_performance({
            'event': 'backtest_completed',
            'total_return': results.total_return,
            'sharpe_ratio': results.sharpe_ratio,
            'max_drawdown': results.max_drawdown
        })
        
        return results
    
    def _load_historical_data(self, symbols: List[str], start_date: str, end_date: str) -> Dict[str, pd.DataFrame]:
        """Load historical data for all symbols."""
        data = {}
        
        if not symbols:
            self.logger.log_error(
                Exception("No symbols provided for backtest"),
                {'operation': 'load_historical_data'}
            )
            return data
        
        for symbol in symbols:
            try:
                if not symbol or not isinstance(symbol, str):
                    self.logger.log_error(
                        Exception(f"Invalid symbol: {symbol}"),
                        {'symbol': symbol, 'operation': 'load_historical_data'}
                    )
                    continue
                
                ticker = yf.Ticker(symbol)
                df = ticker.history(start=start_date, end=end_date, interval='1d')
                
                if not df.empty and len(df) >= 10:  # Require minimum data points
                    # Validate data quality
                    if self._validate_backtest_data(df):
                        data[symbol] = df
                        self.logger.log_market_data(symbol, {
                            'rows': len(df),
                            'date_range': f"{df.index[0]} to {df.index[-1]}",
                            'columns': list(df.columns)
                        })
                    else:
                        self.logger.log_error(
                            Exception(f"Data validation failed for {symbol}"),
                            {'symbol': symbol, 'start_date': start_date, 'end_date': end_date}
                        )
                else:
                    self.logger.log_error(
                        Exception(f"Insufficient data for {symbol}: {len(df)} rows"),
                        {'symbol': symbol, 'start_date': start_date, 'end_date': end_date, 'rows': len(df)}
                    )
                    
            except Exception as e:
                self.logger.log_error(e, {'symbol': symbol, 'operation': 'load_historical_data'})
                
        return data
    
    def _validate_backtest_data(self, df: pd.DataFrame) -> bool:
        """Validate data quality for backtesting."""
        try:
            # Check for required columns
            required_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
            if not all(col in df.columns for col in required_columns):
                return False
            
            # Check for reasonable values
            if (df['High'] < df['Low']).any():
                return False
            
            if (df['Open'] < 0).any() or (df['Close'] < 0).any():
                return False
            
            # Check for excessive price changes (potential data errors)
            price_changes = df['Close'].pct_change().abs()
            if (price_changes > 0.5).any():  # 50% price change
                return False
            
            # Check for sufficient data points
            if len(df) < 10:
                return False
            
            return True
            
        except Exception as e:
            self.logger.log_error(e, {'operation': 'validate_backtest_data'})
            return False
    
    def _run_momentum_strategy(self, data: Dict[str, pd.DataFrame]):
        """Run the default momentum strategy."""
        # Get all unique dates
        all_dates = set()
        for symbol_data in data.values():
            all_dates.update(symbol_data.index)
        
        dates = sorted(list(all_dates))
        
        for date in dates:
            self._process_daily_signals(data, date)
            self._update_positions(data, date)
            self._record_equity(date)
    
    def _run_custom_strategy(self, data: Dict[str, pd.DataFrame], strategy_func: Callable):
        """Run a custom strategy function."""
        for symbol, symbol_data in data.items():
            for date, row in symbol_data.iterrows():
                signal = strategy_func(symbol, row, symbol_data.loc[:date])
                self._execute_signal(symbol, date, signal, row['Close'])
    
    def _process_daily_signals(self, data: Dict[str, pd.DataFrame], date: datetime):
        """Process trading signals for a specific date."""
        for symbol, symbol_data in data.items():
            if date in symbol_data.index:
                # Get data up to current date
                current_data = symbol_data.loc[:date]
                
                if len(current_data) < 50:  # Need enough data for indicators
                    continue
                
                # Calculate indicators
                indicators = self.indicators.calculate_all_indicators(current_data)
                
                # Generate consensus signal
                signal, confidence = self.indicators.generate_consensus_signal(indicators)
                
                # Execute signal
                current_price = current_data.iloc[-1]['Close']
                self._execute_signal(symbol, date, signal, current_price, confidence, indicators)
    
    def _execute_signal(self, symbol: str, date: datetime, signal: SignalType, 
                       price: float, confidence: Optional[float] = None,  # N/A instead of fake 0.5 
                       indicators: Optional[Dict[str, IndicatorResult]] = None):
        """Execute a trading signal."""
        if signal == SignalType.HOLD:
            return
        
        # Calculate position size based on Kelly Criterion
        position_size = self._calculate_position_size(confidence, price)
        
        if signal in [SignalType.BUY, SignalType.STRONG_BUY]:
            if symbol not in self.positions:
                # Open new position
                quantity = position_size / price
                cost = quantity * price * (1 + self.commission_rate + self.slippage)
                
                if cost <= self.current_capital:
                    self.positions[symbol] = Position(
                        symbol=symbol,
                        quantity=quantity,
                        avg_price=price,
                        current_value=quantity * price,
                        unrealized_pnl=0,
                        entry_date=date
                    )
                    
                    self.current_capital -= cost
                    
                    trade = Trade(
                        symbol=symbol,
                        timestamp=date,
                        trade_type=TradeType.BUY,
                        quantity=quantity,
                        price=price,
                        value=cost,
                        signal_strength=confidence,
                        indicators={name: result.value for name, result in (indicators or {}).items()}
                    )
                    self.trades.append(trade)
                    
                    self.logger.log_trade(symbol, "BUY", quantity, price, date)
        
        elif signal in [SignalType.SELL, SignalType.STRONG_SELL]:
            if symbol in self.positions:
                # Close position
                position = self.positions[symbol]
                quantity = position.quantity
                proceeds = quantity * price * (1 - self.commission_rate - self.slippage)
                
                self.current_capital += proceeds
                
                trade = Trade(
                    symbol=symbol,
                    timestamp=date,
                    trade_type=TradeType.SELL,
                    quantity=quantity,
                    price=price,
                    value=proceeds,
                    signal_strength=confidence,
                    indicators={name: result.value for name, result in (indicators or {}).items()}
                )
                self.trades.append(trade)
                
                del self.positions[symbol]
                
                self.logger.log_trade(symbol, "SELL", quantity, price, date)
    
    def _calculate_position_size(self, confidence: float, price: float) -> float:
        """Calculate position size using Kelly Criterion."""
        # Simplified Kelly Criterion
        win_rate = confidence
        avg_win = self.take_profit
        avg_loss = self.stop_loss
        
        kelly_fraction = (win_rate * avg_win - (1 - win_rate) * avg_loss) / avg_win
        
        # Limit position size
        max_position_value = self.current_capital * self.max_position_size
        kelly_position_value = self.current_capital * max(0, min(kelly_fraction, 0.25))
        
        return min(max_position_value, kelly_position_value)
    
    def _update_positions(self, data: Dict[str, pd.DataFrame], date: datetime):
        """Update position values and check stop losses."""
        positions_to_close = []
        
        for symbol, position in self.positions.items():
            if symbol in data and date in data[symbol].index:
                current_price = data[symbol].loc[date]['Close']
                position.current_value = position.quantity * current_price
                position.unrealized_pnl = position.current_value - (position.quantity * position.avg_price)
                
                # Check stop loss
                loss_pct = (position.avg_price - current_price) / position.avg_price
                if loss_pct >= self.stop_loss:
                    positions_to_close.append(symbol)
        
        # Close positions that hit stop loss
        for symbol in positions_to_close:
            position = self.positions[symbol]
            if symbol in data and date in data[symbol].index:
                current_price = data[symbol].loc[date]['Close']
                proceeds = position.quantity * current_price * (1 - self.commission_rate - self.slippage)
                self.current_capital += proceeds
                
                trade = Trade(
                    symbol=symbol,
                    timestamp=date,
                    trade_type=TradeType.SELL,
                    quantity=position.quantity,
                    price=current_price,
                    value=proceeds,
                    signal_strength=1.0,  # Stop loss
                    indicators={}
                )
                self.trades.append(trade)
                
                del self.positions[symbol]
                
                self.logger.log_trade(symbol, "STOP_LOSS", position.quantity, current_price, date)
    
    def _record_equity(self, date: datetime):
        """Record current equity for equity curve."""
        total_value = self.current_capital
        for position in self.positions.values():
            total_value += position.current_value
        
        self.equity_curve.append({
            'date': date,
            'equity': total_value
        })
    
    def _calculate_results(self) -> BacktestResult:
        """Calculate comprehensive backtest results."""
        if not self.equity_curve:
            raise ValueError("No equity curve data available")
        
        equity_df = pd.DataFrame(self.equity_curve)
        equity_df.set_index('date', inplace=True)
        
        # Calculate returns
        equity_df['returns'] = equity_df['equity'].pct_change()
        
        # Basic metrics
        total_return = (equity_df['equity'].iloc[-1] - self.initial_capital) / self.initial_capital
        annualized_return = self._calculate_annualized_return(equity_df)
        
        # Risk metrics
        sharpe_ratio = self._calculate_sharpe_ratio(equity_df)
        max_drawdown = self._calculate_max_drawdown(equity_df)
        
        # Trade metrics
        winning_trades = [t for t in self.trades if t.trade_type == TradeType.SELL and t.value > 0]
        losing_trades = [t for t in self.trades if t.trade_type == TradeType.SELL and t.value <= 0]
        
        win_rate = len(winning_trades) / len(self.trades) if self.trades else 0
        avg_win = np.mean([t.value for t in winning_trades]) if winning_trades else 0
        avg_loss = np.mean([t.value for t in losing_trades]) if losing_trades else 0
        
        profit_factor = abs(avg_win / avg_loss) if avg_loss != 0 else float('inf')
        
        return BacktestResult(
            total_return=total_return,
            annualized_return=annualized_return,
            sharpe_ratio=sharpe_ratio,
            max_drawdown=max_drawdown,
            win_rate=win_rate,
            profit_factor=profit_factor,
            total_trades=len(self.trades),
            winning_trades=len(winning_trades),
            losing_trades=len(losing_trades),
            avg_win=avg_win,
            avg_loss=avg_loss,
            trades=self.trades,
            equity_curve=equity_df['equity'],
            positions=list(self.positions.values()),
            metrics={
                'calmar_ratio': annualized_return / max_drawdown if max_drawdown > 0 else 0,
                'sortino_ratio': self._calculate_sortino_ratio(equity_df),
                'max_consecutive_losses': self._calculate_max_consecutive_losses(),
                'avg_trade_duration': self._calculate_avg_trade_duration()
            }
        )
    
    def _calculate_annualized_return(self, equity_df: pd.DataFrame) -> float:
        """Calculate annualized return."""
        try:
            if equity_df.empty or len(equity_df) < 2:
                return 0
            
            total_days = (equity_df.index[-1] - equity_df.index[0]).days
            if total_days <= 0:
                return 0
            
            final_equity = equity_df['equity'].iloc[-1]
            if final_equity <= 0 or self.initial_capital <= 0:
                return 0
            
            total_return = (final_equity - self.initial_capital) / self.initial_capital
            
            # Handle edge cases
            if total_return <= -1:  # Complete loss
                return -1.0
            
            annualized_return = ((1 + total_return) ** (365 / total_days)) - 1
            
            # Handle extreme values
            if np.isnan(annualized_return) or np.isinf(annualized_return):
                return 0
            
            return annualized_return
            
        except Exception as e:
            self.logger.log_error(e, {'operation': 'calculate_annualized_return'})
            return 0
    
    def _calculate_sharpe_ratio(self, equity_df: pd.DataFrame) -> float:
        """Calculate Sharpe ratio."""
        try:
            if equity_df.empty or 'returns' not in equity_df.columns:
                return 0
            
            returns = equity_df['returns'].dropna()
            if len(returns) < 2:  # Need at least 2 data points
                return 0
            
            risk_free_rate = self.config.get('performance', {}).get('risk_free_rate', 0.02)
            excess_returns = returns - risk_free_rate / 252  # Daily risk-free rate
            
            # Handle edge cases
            if len(excess_returns) < 2:
                return 0
            
            std_dev = excess_returns.std()
            if std_dev == 0 or np.isnan(std_dev) or np.isinf(std_dev):
                return 0
            
            mean_return = excess_returns.mean()
            if np.isnan(mean_return) or np.isinf(mean_return):
                return 0
            
            sharpe = mean_return / std_dev * np.sqrt(252)
            
            # Handle extreme values
            if np.isnan(sharpe) or np.isinf(sharpe):
                return 0
            
            # Cap extreme values
            if sharpe > 10 or sharpe < -10:
                return 0
            
            return sharpe
            
        except Exception as e:
            self.logger.log_error(e, {'operation': 'calculate_sharpe_ratio'})
            return 0
    
    def _calculate_sortino_ratio(self, equity_df: pd.DataFrame) -> float:
        """Calculate Sortino ratio."""
        try:
            if equity_df.empty or 'returns' not in equity_df.columns:
                return 0
            
            returns = equity_df['returns'].dropna()
            if len(returns) < 2:
                return 0
            
            risk_free_rate = self.config.get('performance', {}).get('risk_free_rate', 0.02)
            excess_returns = returns - risk_free_rate / 252
            
            downside_returns = excess_returns[excess_returns < 0]
            if len(downside_returns) < 2:  # Need at least 2 downside returns
                return 0
            
            std_dev = downside_returns.std()
            if std_dev == 0 or np.isnan(std_dev) or np.isinf(std_dev):
                return 0
            
            mean_return = excess_returns.mean()
            if np.isnan(mean_return) or np.isinf(mean_return):
                return 0
            
            sortino = mean_return / std_dev * np.sqrt(252)
            
            # Handle extreme values
            if np.isnan(sortino) or np.isinf(sortino):
                return 0
            
            # Cap extreme values
            if sortino > 10 or sortino < -10:
                return 0
            
            return sortino
            
        except Exception as e:
            self.logger.log_error(e, {'operation': 'calculate_sortino_ratio'})
            return 0
    
    def _calculate_max_drawdown(self, equity_df: pd.DataFrame) -> float:
        """Calculate maximum drawdown."""
        try:
            if equity_df.empty or 'equity' not in equity_df.columns:
                return 0
            
            equity_series = equity_df['equity']
            if len(equity_series) < 2:
                return 0
            
            # Handle zero or negative equity values
            if (equity_series <= 0).any():
                return 1.0  # 100% drawdown if equity goes to zero or negative
            
            peak = equity_series.expanding().max()
            
            # Avoid division by zero
            drawdown = (equity_series - peak) / peak
            drawdown = drawdown.fillna(0)  # Handle any NaN values
            
            max_drawdown = abs(drawdown.min())
            
            # Handle extreme values
            if np.isnan(max_drawdown) or np.isinf(max_drawdown):
                return 0
            
            # Cap at 100%
            return min(max_drawdown, 1.0)
            
        except Exception as e:
            self.logger.log_error(e, {'operation': 'calculate_max_drawdown'})
            return 0
    
    def _calculate_max_consecutive_losses(self) -> int:
        """Calculate maximum consecutive losing trades."""
        max_consecutive = 0
        current_consecutive = 0
        
        for trade in self.trades:
            if trade.trade_type == TradeType.SELL:
                if trade.value <= 0:  # Losing trade
                    current_consecutive += 1
                    max_consecutive = max(max_consecutive, current_consecutive)
                else:  # Winning trade
                    current_consecutive = 0
        
        return max_consecutive
    
    def _calculate_avg_trade_duration(self) -> float:
        """Calculate average trade duration in days."""
        if len(self.trades) < 2:
            return 0
        
        durations = []
        for i in range(1, len(self.trades), 2):  # Pair buy/sell trades
            if i < len(self.trades):
                duration = (self.trades[i].timestamp - self.trades[i-1].timestamp).days
                durations.append(duration)
        
        return np.mean(durations) if durations else 0
    
    def generate_report(self, results: BacktestResult, output_path: str = "backtest_report.html"):
        """Generate a comprehensive HTML backtest report."""
        # This would generate a detailed HTML report with charts and metrics
        # Implementation would include plotly charts and detailed analysis
        pass


def run_backtest(symbols: List[str], start_date: str, end_date: str, 
                config: Optional[Dict] = None) -> BacktestResult:
    """
    Convenience function to run a backtest.
    
    Args:
        symbols: List of symbols to backtest
        start_date: Start date (YYYY-MM-DD)
        end_date: End date (YYYY-MM-DD)
        config: Configuration dictionary
        
    Returns:
        Backtest results
    """
    if config is None:
        config = get_config().dict()
    
    engine = BacktestEngine(config)
    return engine.run_backtest(symbols, start_date, end_date)


if __name__ == "__main__":
    # Example usage
    symbols = ["AAPL", "GOOGL", "MSFT", "NVDA"]
    start_date = "2023-01-01"
    end_date = "2023-12-31"
    
    results = run_backtest(symbols, start_date, end_date)
    
    print(f"Total Return: {results.total_return:.2%}")
    print(f"Annualized Return: {results.annualized_return:.2%}")
    print(f"Sharpe Ratio: {results.sharpe_ratio:.2f}")
    print(f"Max Drawdown: {results.max_drawdown:.2%}")
    print(f"Win Rate: {results.win_rate:.2%}")
    print(f"Total Trades: {results.total_trades}") 