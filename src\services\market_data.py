"""
Market data service for AI-Nvestor.

This module provides:
- Unified access to multiple data sources
- Real-time and historical data retrieval
- Data validation and quality checks
- Caching and performance optimization
- Fallback mechanisms for reliability
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Union, Tuple
from datetime import datetime, timedelta
import yfinance as yf
import asyncio
import aiohttp
from pathlib import Path
import json
import pickle
import hashlib

from core.base_client import BaseAPIClient, AsyncBaseAPIClient, APIResponse
from core.exceptions import MarketDataError, DataError, get_error_code
from utils.logging import get_logger


class MarketDataService:
    """
    Comprehensive market data service.
    
    Features:
    - Multi-source data aggregation
    - Real-time and historical data
    - Data validation and cleaning
    - Intelligent caching
    - Fallback mechanisms
    """
    
    def __init__(self, config: Dict):
        """
        Initialize the market data service.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.logger = get_logger()
        self.cache_dir = Path("data/cache")
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # Data sources configuration
        self.primary_source = config.get('data_sources', {}).get('primary', 'yfinance')
        self.backup_source = config.get('data_sources', {}).get('backup', 'alpha_vantage')
        self.cache_duration = config.get('data_sources', {}).get('cache_duration', 300)
        
        # Trading hours configuration
        self.market_hours = config.get('market_hours', {})
        self.real_time_enabled = config.get('trading', {}).get('real_time_enabled', True)
        
        # Initialize data sources
        self.sources = {
            'yfinance': YFinanceDataSource(),
            'alpha_vantage': AlphaVantageDataSource(config),
            'polygon': PolygonDataSource(config)
        }
    
    def get_stock_data(self, 
                       symbol: str, 
                       interval: str = '5m',
                       days: int = 30,
                       use_cache: bool = True,
                       force_real_time: bool = False) -> pd.DataFrame:
        """
        Get stock data with fallback mechanisms and trading hours optimization.
        
        Args:
            symbol: Stock symbol
            interval: Data interval
            days: Number of days of history
            use_cache: Whether to use cached data
            force_real_time: Force real-time data even outside market hours
            
        Returns:
            DataFrame with OHLCV data
        """
        try:
            # Check if we should use cached data based on market hours
            should_use_cache = use_cache and not force_real_time
            
            # Try cache first (if appropriate)
            if should_use_cache:
                cached_data = self._get_cached_data(symbol, interval, days)
                if cached_data is not None and self.validate_data(cached_data):
                    # If outside market hours, prefer cached data
                    if not self.should_fetch_real_time():
                        self.logger.info(f"Using cached data for {symbol} (outside market hours)")
                        return cached_data
                    # If cache is recent enough, use it
                    elif self._is_cache_fresh(symbol, interval, days):
                        return cached_data
            
            # Only fetch real-time data during market hours (unless forced)
            if not force_real_time and not self.should_fetch_real_time():
                self.logger.info(f"Outside market hours - using cached data for {symbol}")
                cached_data = self._get_cached_data(symbol, interval, days)
                if cached_data is not None and self.validate_data(cached_data):
                    return cached_data
                else:
                    self.logger.warning(f"No valid cached data available for {symbol} outside market hours")
            
            # Try primary source
            data = self._get_from_source(self.primary_source, symbol, interval, days)
            if data is not None and not data.empty and self.validate_data(data):
                self._cache_data(symbol, interval, days, data)
                return data
            
            # Try backup source
            if self.backup_source != self.primary_source:
                data = self._get_from_source(self.backup_source, symbol, interval, days)
                if data is not None and not data.empty and self.validate_data(data):
                    self._cache_data(symbol, interval, days, data)
                    return data
            
            raise MarketDataError(
                f"No data available for {symbol} from any source",
                error_code=get_error_code('NO_DATA_AVAILABLE'),
                context={'symbol': symbol, 'interval': interval, 'days': days}
            )
            
        except Exception as e:
            self.logger.log_error(e, {'symbol': symbol, 'operation': 'get_stock_data'})
            raise
    
    def get_multiple_symbols(self, 
                           symbols: List[str], 
                           interval: str = '5m',
                           days: int = 30) -> Dict[str, pd.DataFrame]:
        """
        Get data for multiple symbols efficiently.
        
        Args:
            symbols: List of stock symbols
            interval: Data interval
            days: Number of days of history
            
        Returns:
            Dictionary mapping symbols to DataFrames
        """
        results = {}
        
        for symbol in symbols:
            try:
                data = self.get_stock_data(symbol, interval, days)
                results[symbol] = data
            except Exception as e:
                self.logger.log_error(e, {'symbol': symbol})
                results[symbol] = None
        
        return results
    
    def is_market_hours(self) -> bool:
        """
        Check if current time is within market hours.
        
        Returns:
            True if within market hours (including extended hours)
        """
        try:
            from datetime import datetime
            import pytz
            
            # Get current time in market timezone
            market_tz = pytz.timezone(self.market_hours.get('timezone', 'America/New_York'))
            now = datetime.now(market_tz)
            
            # Parse market hours
            market_start = datetime.strptime(self.market_hours.get('start', '09:30'), '%H:%M').time()
            market_end = datetime.strptime(self.market_hours.get('end', '16:00'), '%H:%M').time()
            pre_market_start = datetime.strptime(self.market_hours.get('pre_market_start', '06:30'), '%H:%M').time()
            after_hours_end = datetime.strptime(self.market_hours.get('after_hours_end', '20:00'), '%H:%M').time()
            
            current_time = now.time()
            
            # Check if within extended trading hours
            is_market_open = (market_start <= current_time <= market_end)
            is_pre_market = (pre_market_start <= current_time < market_start)
            is_after_hours = (market_end < current_time <= after_hours_end)
            
            return is_market_open or is_pre_market or is_after_hours
            
        except Exception as e:
            self.logger.log_error(e, {'operation': 'check_market_hours'})
            # Default to True if there's an error
            return True
    
    def should_fetch_real_time(self) -> bool:
        """
        Determine if real-time data should be fetched based on market hours.
        
        Returns:
            True if real-time data should be fetched
        """
        if not self.real_time_enabled:
            return False
        
        return self.is_market_hours()
    
    def get_real_time_price(self, symbol: str) -> Dict[str, float]:
        """
        Get real-time price data.
        
        Args:
            symbol: Stock symbol
            
        Returns:
            Dictionary with price information
        """
        try:
            # Try real-time source first
            real_time_source = self.config.get('data_sources', {}).get('real_time', 'polygon')
            if real_time_source in self.sources:
                return self.sources[real_time_source].get_real_time_price(symbol)
            
            # Fallback to yfinance
            return self.sources['yfinance'].get_real_time_price(symbol)
            
        except Exception as e:
            self.logger.log_error(e, {'symbol': symbol, 'operation': 'get_real_time_price'})
            raise
    
    def validate_data(self, data: pd.DataFrame) -> bool:
        """
        Validate data quality.
        
        Args:
            data: DataFrame to validate
            
        Returns:
            True if data is valid
        """
        if data is None or data.empty:
            self.logger.warning("Data validation failed: data is None or empty")
            return False
        
        # Check for required columns
        required_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
        missing_columns = [col for col in required_columns if col not in data.columns]
        if missing_columns:
            self.logger.warning(f"Data validation failed: missing columns {missing_columns}")
            return False
        
        # Check for reasonable values
        if (data['High'] < data['Low']).any():
            self.logger.warning("Data validation failed: High values less than Low values")
            return False
        
        if (data['Open'] < 0).any() or (data['Close'] < 0).any():
            self.logger.warning("Data validation failed: negative price values")
            return False
        
        # Check for excessive gaps (potential data issues)
        price_changes = data['Close'].pct_change().abs()
        if (price_changes > 0.5).any():  # 50% price change
            self.logger.warning("Data validation failed: excessive price changes detected")
            return False
        
        return True
    
    def _get_from_source(self, source_name: str, symbol: str, interval: str, days: int) -> Optional[pd.DataFrame]:
        """Get data from a specific source."""
        if source_name not in self.sources:
            return None
        
        try:
            return self.sources[source_name].get_data(symbol, interval, days)
        except Exception as e:
            self.logger.log_error(e, {
                'source': source_name, 
                'symbol': symbol, 
                'operation': 'get_from_source'
            })
            return None
    
    def _get_cached_data(self, symbol: str, interval: str, days: int) -> Optional[pd.DataFrame]:
        """Get data from cache if available and fresh."""
        cache_key = self._generate_cache_key(symbol, interval, days)
        cache_file = self.cache_dir / f"{cache_key}.pkl"
        
        if not cache_file.exists():
            return None
        
        try:
            # Check if cache is fresh
            cache_age = datetime.now().timestamp() - cache_file.stat().st_mtime
            if cache_age > self.cache_duration:
                return None
            
            with open(cache_file, 'rb') as f:
                return pickle.load(f)
        except Exception as e:
            self.logger.log_error(e, {'operation': 'get_cached_data'})
            return None
    
    def _cache_data(self, symbol: str, interval: str, days: int, data: pd.DataFrame):
        """Cache data for future use."""
        try:
            cache_key = self._generate_cache_key(symbol, interval, days)
            cache_file = self.cache_dir / f"{cache_key}.pkl"
            
            with open(cache_file, 'wb') as f:
                pickle.dump(data, f)
        except Exception as e:
            self.logger.log_error(e, {'operation': 'cache_data'})
    
    def _generate_cache_key(self, symbol: str, interval: str, days: int) -> str:
        """Generate cache key for data."""
        key_string = f"{symbol}_{interval}_{days}"
        return hashlib.md5(key_string.encode()).hexdigest()
    
    def _is_cache_fresh(self, symbol: str, interval: str, days: int) -> bool:
        """
        Check if cached data is fresh enough to use.
        
        Args:
            symbol: Stock symbol
            interval: Data interval
            days: Number of days
            
        Returns:
            True if cache is fresh enough
        """
        try:
            cache_file = self.cache_dir / f"{self._generate_cache_key(symbol, interval, days)}.pkl"
            if not cache_file.exists():
                return False
            
            # Check file modification time
            import time
            file_age = time.time() - cache_file.stat().st_mtime
            
            # Cache is fresh if less than cache_duration seconds old
            return file_age < self.cache_duration
            
        except Exception as e:
            self.logger.log_error(e, {'operation': 'check_cache_freshness'})
            return False


class YFinanceDataSource:
    """Yahoo Finance data source."""
    
    def __init__(self):
        self.logger = get_logger()
    
    def get_data(self, symbol: str, interval: str = '5m', days: int = 30) -> pd.DataFrame:
        """Get data from Yahoo Finance."""
        try:
            ticker = yf.Ticker(symbol)
            end = datetime.now()
            start = end - timedelta(days=days)
            
            data = ticker.history(start=start, end=end, interval=interval)
            
            if data.empty:
                raise MarketDataError(
                    f"No data available for {symbol}",
                    error_code=get_error_code('NO_DATA_AVAILABLE'),
                    context={'symbol': symbol}
                )
            
            return data
            
        except Exception as e:
            self.logger.log_error(e, {'symbol': symbol, 'source': 'yfinance'})
            raise
    
    def get_real_time_price(self, symbol: str) -> Dict[str, float]:
        """Get real-time price from Yahoo Finance."""
        try:
            ticker = yf.Ticker(symbol)
            info = ticker.info
            
            return {
                'price': info.get('regularMarketPrice', 0),
                'change': info.get('regularMarketChange', 0),
                'change_percent': info.get('regularMarketChangePercent', 0),
                'volume': info.get('volume', 0),
                'market_cap': info.get('marketCap', 0)
            }
        except Exception as e:
            self.logger.log_error(e, {'symbol': symbol, 'source': 'yfinance'})
            raise


class AlphaVantageDataSource(BaseAPIClient):
    """Alpha Vantage data source."""
    
    def __init__(self, config: Dict):
        api_key = config.get('api_keys', {}).get('alpha_vantage')
        super().__init__(
            base_url="https://www.alphavantage.co/query",
            api_key=api_key,
            rate_limit_requests=5,  # Alpha Vantage free tier limit
            rate_limit_window=60
        )
        self.logger = get_logger()
    
    def get_data(self, symbol: str, interval: str = '5m', days: int = 30) -> pd.DataFrame:
        """Get data from Alpha Vantage."""
        try:
            # Convert interval to Alpha Vantage format
            av_interval = self._convert_interval(interval)
            
            response = self.get("", params={
                'function': 'TIME_SERIES_INTRADAY',
                'symbol': symbol,
                'interval': av_interval,
                'apikey': self.api_key,
                'outputsize': 'full'
            })
            
            if not response.success:
                raise MarketDataError(
                    f"Alpha Vantage API error: {response.error}",
                    error_code=get_error_code('API_RATE_LIMIT'),
                    context={'symbol': symbol, 'response': response.data}
                )
            
            # Parse response
            data = self._parse_alpha_vantage_response(response.data, symbol)
            return data
            
        except Exception as e:
            self.logger.log_error(e, {'symbol': symbol, 'source': 'alpha_vantage'})
            raise
    
    def get_real_time_price(self, symbol: str) -> Dict[str, float]:
        """Get real-time price from Alpha Vantage."""
        try:
            response = self.get("", params={
                'function': 'GLOBAL_QUOTE',
                'symbol': symbol,
                'apikey': self.api_key
            })
            
            if not response.success:
                raise MarketDataError(
                    f"Alpha Vantage API error: {response.error}",
                    error_code=get_error_code('API_RATE_LIMIT')
                )
            
            quote = response.data.get('Global Quote', {})
            
            return {
                'price': float(quote.get('05. price', 0)),
                'change': float(quote.get('09. change', 0)),
                'change_percent': float(quote.get('10. change percent', '0').rstrip('%')),
                'volume': int(quote.get('06. volume', 0)),
                'market_cap': 0  # Not provided by Alpha Vantage
            }
        except Exception as e:
            self.logger.log_error(e, {'symbol': symbol, 'source': 'alpha_vantage'})
            raise
    
    def _convert_interval(self, interval: str) -> str:
        """Convert interval to Alpha Vantage format."""
        mapping = {
            '1m': '1min',
            '5m': '5min',
            '15m': '15min',
            '30m': '30min',
            '60m': '60min',
            '1d': 'daily'
        }
        return mapping.get(interval, '5min')
    
    def _parse_alpha_vantage_response(self, response_data: Dict, symbol: str) -> pd.DataFrame:
        """Parse Alpha Vantage response into DataFrame."""
        time_series_key = None
        for key in response_data.keys():
            if 'Time Series' in key:
                time_series_key = key
                break
        
        if not time_series_key:
            raise MarketDataError(
                "Invalid Alpha Vantage response format",
                error_code=get_error_code('DATA_RETRIEVAL_FAILED')
            )
        
        time_series = response_data[time_series_key]
        
        data = []
        for timestamp, values in time_series.items():
            data.append({
                'Open': float(values['1. open']),
                'High': float(values['2. high']),
                'Low': float(values['3. low']),
                'Close': float(values['4. close']),
                'Volume': int(values['5. volume'])
            })
        
        df = pd.DataFrame(data, index=pd.to_datetime(list(time_series.keys())))
        df.index.name = 'Date'
        return df.sort_index()
    
    def validate_response(self, response: APIResponse) -> bool:
        """Validate Alpha Vantage response."""
        if not response.success:
            return False
        
        # Check for API error messages
        if 'Error Message' in response.data:
            return False
        
        # Check for rate limit message
        if 'Note' in response.data and 'API call frequency' in response.data['Note']:
            return False
        
        return True


class PolygonDataSource(BaseAPIClient):
    """Polygon.io data source."""
    
    def __init__(self, config: Dict):
        api_key = config.get('api_keys', {}).get('polygon')
        super().__init__(
            base_url="https://api.polygon.io",
            api_key=api_key,
            rate_limit_requests=5,  # Polygon free tier limit
            rate_limit_window=60
        )
        self.logger = get_logger()
    
    def get_data(self, symbol: str, interval: str = '5m', days: int = 30) -> pd.DataFrame:
        """Get data from Polygon.io."""
        try:
            # Convert interval to Polygon format
            polygon_interval = self._convert_interval(interval)
            
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            response = self.get(f"/v2/aggs/ticker/{symbol}/range/{polygon_interval}/{start_date.strftime('%Y-%m-%d')}/{end_date.strftime('%Y-%m-%d')}")
            
            if not response.success:
                raise MarketDataError(
                    f"Polygon API error: {response.error}",
                    error_code=get_error_code('API_RATE_LIMIT'),
                    context={'symbol': symbol}
                )
            
            # Parse response
            data = self._parse_polygon_response(response.data, symbol)
            return data
            
        except Exception as e:
            self.logger.log_error(e, {'symbol': symbol, 'source': 'polygon'})
            raise
    
    def get_real_time_price(self, symbol: str) -> Dict[str, float]:
        """Get real-time price from Polygon.io."""
        try:
            response = self.get(f"/v2/snapshot/locale/us/markets/stocks/tickers/{symbol}")
            
            if not response.success:
                raise MarketDataError(
                    f"Polygon API error: {response.error}",
                    error_code=get_error_code('API_RATE_LIMIT')
                )
            
            ticker_data = response.data.get('results', {})
            
            return {
                'price': ticker_data.get('c', 0),  # Current price
                'change': ticker_data.get('d', 0),  # Change
                'change_percent': ticker_data.get('dp', 0),  # Change percent
                'volume': ticker_data.get('v', 0),  # Volume
                'market_cap': ticker_data.get('market_cap', 0)
            }
        except Exception as e:
            self.logger.log_error(e, {'symbol': symbol, 'source': 'polygon'})
            raise
    
    def _convert_interval(self, interval: str) -> str:
        """Convert interval to Polygon format."""
        mapping = {
            '1m': '1',
            '5m': '5',
            '15m': '15',
            '30m': '30',
            '60m': '60',
            '1d': '1'
        }
        return mapping.get(interval, '5')
    
    def _parse_polygon_response(self, response_data: Dict, symbol: str) -> pd.DataFrame:
        """Parse Polygon response into DataFrame."""
        results = response_data.get('results', [])
        
        if not results:
            raise MarketDataError(
                "No data in Polygon response",
                error_code=get_error_code('NO_DATA_AVAILABLE')
            )
        
        data = []
        for bar in results:
            data.append({
                'Open': bar['o'],
                'High': bar['h'],
                'Low': bar['l'],
                'Close': bar['c'],
                'Volume': bar['v']
            })
        
        df = pd.DataFrame(data, index=pd.to_datetime([bar['t'] for bar in results], unit='ms'))
        df.index.name = 'Date'
        return df.sort_index()
    
    def validate_response(self, response: APIResponse) -> bool:
        """Validate Polygon response."""
        if not response.success:
            return False
        
        # Check for API error messages
        if 'error' in response.data:
            return False
        
        return True
