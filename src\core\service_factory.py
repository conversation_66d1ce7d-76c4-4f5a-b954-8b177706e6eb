"""
Service factory for dependency injection and configuration management.

This module provides:
- Centralized service creation
- Dependency injection
- Configuration sharing
- Service lifecycle management
"""

from typing import Dict, Any, Optional, Type, TypeVar
from dataclasses import dataclass

from utils.config import ConfigManager
from utils.logging import get_logger
from services.market_data import MarketDataService
from services.indicators import TechnicalIndicators
from services.ai_advisor import AIAdvisor
from services.alerts import AlertSystem
from core.error_handler import global_error_handler

T = TypeVar('T')


@dataclass
class ServiceContainer:
    """Container for all initialized services."""
    config_manager: ConfigManager
    market_data: MarketDataService
    indicators: TechnicalIndicators
    ai_advisor: Optional[AIAdvisor]
    alert_system: Optional[AlertSystem]
    logger: Any


class ServiceFactory:
    """Factory for creating and managing services with dependency injection."""
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.config = config_manager.get_config()
        self.logger = get_logger()
        self._services: Dict[str, Any] = {}
    
    def create_container(self) -> ServiceContainer:
        """Create a service container with all initialized services."""
        return ServiceContainer(
            config_manager=self.config_manager,
            market_data=self.get_market_data_service(),
            indicators=self.get_indicators_service(),
            ai_advisor=self.get_ai_advisor_service(),
            alert_system=self.get_alert_system_service(),
            logger=self.logger
        )
    
    def get_market_data_service(self) -> MarketDataService:
        """Get or create market data service."""
        if 'market_data' not in self._services:
            try:
                # Services expect dict config from model_dump()
                config_dict = self.config.model_dump()
                self._services['market_data'] = MarketDataService(config_dict)
                self.logger.info("Market data service initialized successfully")
            except Exception as e:
                global_error_handler.handle_error(
                    e, 
                    {'service': 'market_data'}, 
                    'service_initialization',
                    recoverable=False
                )
                raise RuntimeError(f"Failed to initialize market data service: {e}")
        
        return self._services['market_data']
    
    def get_indicators_service(self) -> TechnicalIndicators:
        """Get or create technical indicators service."""
        if 'indicators' not in self._services:
            try:
                # Services expect dict config from model_dump()
                config_dict = self.config.model_dump()
                self._services['indicators'] = TechnicalIndicators(config_dict)
                self.logger.info("Technical indicators service initialized successfully")
            except Exception as e:
                global_error_handler.handle_error(
                    e, 
                    {'service': 'indicators'}, 
                    'service_initialization',
                    recoverable=False
                )
                raise RuntimeError(f"Failed to initialize indicators service: {e}")
        
        return self._services['indicators']
    
    def get_ai_advisor_service(self) -> Optional[AIAdvisor]:
        """Get or create AI advisor service (optional)."""
        if 'ai_advisor' not in self._services:
            try:
                # Services expect dict config from model_dump()
                config_dict = self.config.model_dump()
                self._services['ai_advisor'] = AIAdvisor(config_dict)
                self.logger.info("AI advisor service initialized successfully")
            except Exception as e:
                global_error_handler.handle_error(
                    e, 
                    {'service': 'ai_advisor'}, 
                    'service_initialization',
                    recoverable=True
                )
                self.logger.warning(f"AI advisor initialization failed: {e}")
                self._services['ai_advisor'] = None
        
        return self._services['ai_advisor']
    
    def get_alert_system_service(self) -> Optional[AlertSystem]:
        """Get or create alert system service (optional)."""
        if 'alert_system' not in self._services:
            try:
                # Services expect dict config from model_dump()
                config_dict = self.config.model_dump()
                self._services['alert_system'] = AlertSystem(config_dict)
                self.logger.info("Alert system service initialized successfully")
            except Exception as e:
                global_error_handler.handle_error(
                    e, 
                    {'service': 'alert_system'}, 
                    'service_initialization',
                    recoverable=True
                )
                self.logger.warning(f"Alert system initialization failed: {e}")
                self._services['alert_system'] = None
        
        return self._services['alert_system']
    
    def cleanup(self) -> None:
        """Clean up services and resources."""
        for service_name, service in self._services.items():
            try:
                if hasattr(service, 'cleanup'):
                    service.cleanup()
                elif hasattr(service, 'close'):
                    service.close()
            except Exception as e:
                self.logger.warning(f"Error cleaning up {service_name}: {e}")
        
        self._services.clear()
        self.logger.info("Service factory cleanup completed")