"""
Sentiment data service for AI-Nvestor.

This module provides:
- Reddit sentiment analysis from relevant subreddits
- News headline aggregation from multiple sources
- Social media sentiment analysis
- Sentiment scoring and normalization
- Caching and rate limiting
- Integration with AI analysis
"""

import pandas as pd
import numpy as np
import httpx
import asyncio
import json
from typing import Dict, List, Optional, Any, Tu<PERSON>
from datetime import datetime, timed<PERSON>ta
from dataclasses import dataclass
from enum import Enum
import time
import hashlib
from pathlib import Path
import re
from urllib.parse import quote

from core.base_client import AsyncBaseAPIClient
from core.exceptions import DataError
from utils.logging import get_logger


class SentimentSource(Enum):
    """Sentiment data sources."""
    REDDIT = "reddit"
    NEWS_API = "news_api"
    ALPHA_VANTAGE = "alpha_vantage"
    POLYGON = "polygon"
    TWITTER = "twitter"
    CUSTOM = "custom"


@dataclass
class SentimentData:
    """Sentiment data structure."""
    symbol: str
    source: SentimentSource
    sentiment_score: float  # -1.0 to 1.0
    confidence: float  # 0.0 to 1.0
    timestamp: datetime
    text: str
    metadata: Dict[str, Any]


class RedditSentimentService:
    """
    Reddit-specific sentiment analysis service using praw with fallback to JSON API.
    
    Features:
    - Fetches posts and comments from relevant subreddits using official Reddit API
    - Falls back to JSON API when praw is not available
    - Analyzes sentiment using keyword-based scoring
    - Handles rate limiting and Reddit API restrictions
    - Caches results for performance
    - Includes company names in addition to ticker symbols for comprehensive search
    """
    
    def __init__(self, config: Dict):
        self.config = config
        self.logger = get_logger()
        
        # Reddit configuration
        self.reddit_config = config.get('sentiment_data', {}).get('reddit', {})
        self.subreddits = self.reddit_config.get('subreddits', [
            'stocks', 'investing', 'wallstreetbets', 'stockmarket', 
            'options', 'daytrading', 'investments', 'financialindependence'
        ])
        self.max_posts_per_subreddit = self.reddit_config.get('max_posts_per_subreddit', 50)
        self.max_comments_per_post = self.reddit_config.get('max_comments_per_post', 20)
        self.min_score_threshold = self.reddit_config.get('min_score_threshold', 5)
        
        # HTTP client for JSON API fallback
        self.client = httpx.AsyncClient(timeout=30.0)
        self.requests_per_minute = 30  # Reddit's actual limit for unauthenticated requests
        self.last_request_time = 0
        
        # Company name mappings for comprehensive search
        self.company_names = {
            'AAPL': ['Apple', 'Apple Inc', 'iPhone', 'iPad', 'Mac', 'iPod'],
            'MSFT': ['Microsoft', 'Microsoft Corporation', 'Windows', 'Office', 'Azure'],
            'GOOGL': ['Google', 'Alphabet', 'Alphabet Inc', 'YouTube', 'Android'],
            'GOOG': ['Google', 'Alphabet', 'Alphabet Inc', 'YouTube', 'Android'],
            'AMZN': ['Amazon', 'Amazon.com', 'AWS', 'Prime'],
            'TSLA': ['Tesla', 'Tesla Inc', 'Elon Musk', 'Model S', 'Model 3', 'Model X', 'Model Y'],
            'NVDA': ['NVIDIA', 'Nvidia Corporation', 'GPU', 'Graphics'],
            'META': ['Meta', 'Facebook', 'Instagram', 'WhatsApp', 'Oculus'],
            'CRM': ['Salesforce', 'Salesforce.com', 'CRM software'],
            'NFLX': ['Netflix', 'Netflix Inc', 'Streaming'],
            'DIS': ['Disney', 'Walt Disney', 'Disney+', 'Marvel', 'Star Wars'],
            'PYPL': ['PayPal', 'PayPal Holdings', 'Digital payments'],
            'INTC': ['Intel', 'Intel Corporation', 'CPU', 'Processor'],
            'AMD': ['AMD', 'Advanced Micro Devices', 'Ryzen', 'EPYC'],
            'UBER': ['Uber', 'Uber Technologies', 'Ride sharing'],
            'LYFT': ['Lyft', 'Lyft Inc', 'Ride sharing'],
            'SPOT': ['Spotify', 'Spotify Technology', 'Music streaming'],
            'SQ': ['Square', 'Square Inc', 'Block', 'Cash App'],
            'ZM': ['Zoom', 'Zoom Video Communications', 'Video conferencing'],
            'U': ['Unity', 'Unity Software', 'Game engine', 'Unity Technologies'],
            'XYZ': ['XYZ Corp', 'XYZ Corporation', 'XYZ Company'],  # Placeholder
            'COIN': ['Coinbase', 'Coinbase Global', 'Cryptocurrency'],
            'PLTR': ['Palantir', 'Palantir Technologies', 'Data analytics'],
            'RBLX': ['Roblox', 'Roblox Corporation', 'Gaming platform'],
            'SNAP': ['Snap', 'Snap Inc', 'Snapchat'],
            'TWTR': ['Twitter', 'Twitter Inc', 'Social media'],
            'SHOP': ['Shopify', 'Shopify Inc', 'E-commerce'],
            'ABNB': ['Airbnb', 'Airbnb Inc', 'Vacation rentals'],
            'DASH': ['DoorDash', 'DoorDash Inc', 'Food delivery'],
            'ROKU': ['Roku', 'Roku Inc', 'Streaming devices'],
            'PINS': ['Pinterest', 'Pinterest Inc', 'Social media'],
            'ETSY': ['Etsy', 'Etsy Inc', 'Online marketplace'],
            'WISH': ['Wish', 'ContextLogic Inc', 'Online marketplace'],
            'HOOD': ['Robinhood', 'Robinhood Markets', 'Trading platform'],
            'COUR': ['Coursera', 'Coursera Inc', 'Online education'],
            'PLTR': ['Palantir', 'Palantir Technologies', 'Data analytics'],
            'RIVN': ['Rivian', 'Rivian Automotive', 'Electric vehicles'],
            'LCID': ['Lucid', 'Lucid Group', 'Electric vehicles'],
            'NIO': ['NIO', 'NIO Inc', 'Electric vehicles'],
            'XPEV': ['XPeng', 'XPeng Inc', 'Electric vehicles'],
            'LI': ['Li Auto', 'Li Auto Inc', 'Electric vehicles'],
            'BABA': ['Alibaba', 'Alibaba Group', 'E-commerce'],
            'JD': ['JD.com', 'JD.com Inc', 'E-commerce'],
            'PDD': ['Pinduoduo', 'Pinduoduo Inc', 'E-commerce'],
            'TME': ['Tencent Music', 'Tencent Music Entertainment', 'Music streaming'],
            'BIDU': ['Baidu', 'Baidu Inc', 'Search engine'],
            'NTES': ['NetEase', 'NetEase Inc', 'Gaming'],
            'TCEHY': ['Tencent', 'Tencent Holdings', 'Gaming and social media']
        }
        
        # Initialize praw Reddit client
        self.reddit = None
        try:
            import praw
            client_id = self.reddit_config.get('client_id', '')
            client_secret = self.reddit_config.get('client_secret', '')
            username = self.reddit_config.get('username', '')
            password = self.reddit_config.get('password', '')
            
            if client_id and client_secret and username and password:
                self.reddit = praw.Reddit(
                    client_id=client_id,
                    client_secret=client_secret,
                    user_agent=self.reddit_config.get('user_agent', 'AI-Nvestor/1.0 (Sentiment Analysis)'),
                    username=username,
                    password=password
                )
                self.logger.info("✅ Reddit API client initialized successfully (praw)")
            else:
                self.logger.info("⚠️ Reddit credentials not configured, using JSON API fallback")
        except ImportError:
            self.logger.info("⚠️ praw package not found, using JSON API fallback")
        except Exception as e:
            self.logger.warning(f"⚠️ Failed to initialize Reddit API client (praw): {e}, using JSON API fallback")
        
        # Sentiment keywords
        self.positive_keywords = {
            'bullish', 'moon', 'rocket', 'pump', 'buy', 'long', 'hold', 'diamond', 'hands',
            'tendies', 'gains', 'profit', 'win', 'success', 'growth', 'up', 'rise', 'surge',
            'breakout', 'rally', 'soar', 'jump', 'climb', 'gain', 'positive', 'good', 'great',
            'amazing', 'incredible', 'fantastic', 'excellent', 'outstanding', 'perfect'
        }
        
        self.negative_keywords = {
            'bearish', 'dump', 'sell', 'short', 'crash', 'drop', 'fall', 'decline', 'loss',
            'paper', 'hands', 'bagholder', 'rekt', 'fomo', 'panic', 'fear', 'down', 'fall',
            'plunge', 'tank', 'crash', 'dump', 'sell', 'negative', 'bad', 'terrible', 'awful',
            'horrible', 'disaster', 'catastrophe', 'failure', 'lose', 'losing', 'lost'
        }
        
        # Cache
        self.cache_dir = Path("data/cache/sentiment/reddit")
        self.cache_dir.mkdir(parents=True, exist_ok=True)
    
    def _get_search_terms(self, symbol: str) -> List[str]:
        """
        Get comprehensive search terms for a symbol including company names.
        
        Args:
            symbol: Stock symbol (e.g., 'AAPL', 'MSFT')
            
        Returns:
            List of search terms including symbol and company names
        """
        search_terms = [symbol]
        
        # Add company names if available
        if symbol in self.company_names:
            company_names = self.company_names[symbol]
            search_terms.extend(company_names)
            self.logger.info(f"🔍 Added company names for {symbol}: {', '.join(company_names)}")
        
        return search_terms
    
    async def get_reddit_sentiment(self, symbol: str, hours: int = 24) -> List[SentimentData]:
        """
        Get Reddit sentiment data for a symbol using praw or JSON API fallback.
        
        Args:
            symbol: Stock symbol
            hours: Hours of historical data to fetch
            
        Returns:
            List of sentiment data points
        """
        try:
            # Check cache first
            cached_data = self._get_cached_reddit_sentiment(symbol, hours)
            if cached_data:
                self.logger.info(f"📊 Using cached Reddit sentiment data for {symbol}")
                return cached_data
            
            self.logger.info(f"🔍 Fetching Reddit sentiment for {symbol}...")
            
            # Use praw if available, otherwise use JSON API
            if self.reddit:
                all_sentiment_data = await self._fetch_with_praw(symbol, hours)
            else:
                all_sentiment_data = await self._fetch_with_json_api(symbol, hours)
            
            # Cache the results
            if all_sentiment_data:
                self._cache_reddit_sentiment_data(symbol, all_sentiment_data)
            
            self.logger.info(f"✅ Found {len(all_sentiment_data)} Reddit sentiment data points for {symbol}")
            return all_sentiment_data
            
        except Exception as e:
            self.logger.error(f"❌ Error fetching Reddit sentiment for {symbol}: {e}")
            return []
    
    async def _fetch_with_praw(self, symbol: str, hours: int) -> List[SentimentData]:
        """Fetch Reddit data using praw."""
        all_sentiment_data = []
        
        # Get comprehensive search terms including company names
        search_terms = self._get_search_terms(symbol)
        
        for subreddit_name in self.subreddits:
            try:
                # Get subreddit
                subreddit = self.reddit.subreddit(subreddit_name)
                
                # Search for posts mentioning the symbol and company names
                for search_term in search_terms:
                    search_query = f"{search_term}"
                    posts = subreddit.search(search_query, sort='new', limit=self.max_posts_per_subreddit)
                    
                    post_count = 0
                    for post in posts:
                        if post_count >= self.max_posts_per_subreddit:
                            break
                        
                        # Check if post meets score threshold and is recent
                        if post.score >= self.min_score_threshold:
                            # Analyze post sentiment
                            post_text = f"{post.title} {post.selftext}"
                            post_sentiment = self._analyze_text_sentiment(post_text)
                            
                            if post_sentiment['score'] != 0:  # Only include if sentiment detected
                                all_sentiment_data.append(SentimentData(
                                    symbol=symbol,
                                    source=SentimentSource.REDDIT,
                                    sentiment_score=post_sentiment['score'],
                                    confidence=post_sentiment['confidence'],
                                    timestamp=datetime.fromtimestamp(post.created_utc),
                                    text=post_text[:200],
                                    metadata={
                                        'type': 'post',
                                        'subreddit': subreddit_name,
                                        'post_id': post.id,
                                        'score': post.score,
                                        'upvote_ratio': getattr(post, 'upvote_ratio', 0.0),
                                        'num_comments': post.num_comments,
                                        'url': f"https://reddit.com{post.permalink}",
                                        'search_term': search_term
                                    }
                                ))
                            
                            # Get comments for this post
                            comments = await self._fetch_post_comments(post, symbol)
                            all_sentiment_data.extend(comments)
                            
                            post_count += 1
                    
                    self.logger.info(f"📝 Found {post_count} posts from r/{subreddit_name} for {symbol} (search term: {search_term})")
                
            except Exception as e:
                self.logger.warning(f"⚠️ Error fetching from r/{subreddit_name}: {e}")
                continue
        
        return all_sentiment_data
    
    async def _fetch_with_json_api(self, symbol: str, hours: int) -> List[SentimentData]:
        """Fetch Reddit data using JSON API fallback."""
        all_sentiment_data = []
        
        # Get comprehensive search terms including company names
        search_terms = self._get_search_terms(symbol)
        
        for subreddit_name in self.subreddits:
            try:
                # Rate limiting
                await self._rate_limit()
                
                # Search for posts mentioning the symbol and company names
                for search_term in search_terms:
                    # Fetch posts from subreddit
                    url = f"https://www.reddit.com/r/{subreddit_name}/search.json"
                    params = {
                        'q': search_term,
                        'sort': 'new',
                        't': 'day',
                        'limit': min(self.max_posts_per_subreddit, 25)  # Reddit JSON API limit
                    }
                    headers = {
                        'User-Agent': 'AI-Nvestor/1.0 (Sentiment Analysis)'
                    }
                    
                    response = await self.client.get(url, params=params, headers=headers)
                    response.raise_for_status()
                    
                    data = response.json()
                    posts = data.get('data', {}).get('children', [])
                    
                    post_count = 0
                    for post_data in posts:
                        if post_count >= self.max_posts_per_subreddit:
                            break
                        
                        post = post_data.get('data', {})
                        
                        # Check if post meets score threshold
                        if post.get('score', 0) >= self.min_score_threshold:
                            # Analyze post sentiment
                            post_text = f"{post.get('title', '')} {post.get('selftext', '')}"
                            post_sentiment = self._analyze_text_sentiment(post_text)
                            
                            if post_sentiment['score'] != 0:  # Only include if sentiment detected
                                all_sentiment_data.append(SentimentData(
                                    symbol=symbol,
                                    source=SentimentSource.REDDIT,
                                    sentiment_score=post_sentiment['score'],
                                    confidence=post_sentiment['confidence'],
                                    timestamp=datetime.fromtimestamp(post.get('created_utc', 0)),
                                    text=post_text[:200],
                                    metadata={
                                        'type': 'post',
                                        'subreddit': subreddit_name,
                                        'post_id': post.get('id', ''),
                                        'score': post.get('score', 0),
                                        'upvote_ratio': post.get('upvote_ratio', 0.0),
                                        'num_comments': post.get('num_comments', 0),
                                        'url': f"https://reddit.com{post.get('permalink', '')}",
                                        'search_term': search_term
                                    }
                                ))
                            
                            # Get comments for this post
                            comments = await self._fetch_post_comments_json(post.get('id', ''), symbol, subreddit_name)
                            all_sentiment_data.extend(comments)
                            
                            post_count += 1
                    
                    self.logger.info(f"📝 Found {post_count} posts from r/{subreddit_name} for {symbol} (search term: {search_term})")
                
            except Exception as e:
                self.logger.warning(f"⚠️ Error fetching from r/{subreddit_name}: {e}")
                continue
        
        return all_sentiment_data
    
    async def _fetch_post_comments_json(self, post_id: str, symbol: str, subreddit: str) -> List[SentimentData]:
        """Fetch comments using JSON API."""
        try:
            # Rate limiting
            await self._rate_limit()
            
            # Fetch comments
            url = f"https://www.reddit.com/comments/{post_id}.json"
            headers = {
                'User-Agent': 'AI-Nvestor/1.0 (Sentiment Analysis)'
            }
            
            response = await self.client.get(url, headers=headers)
            response.raise_for_status()
            
            data = response.json()
            comments_data = []
            comment_count = 0
            
            # Get comprehensive search terms for this symbol
            search_terms = self._get_search_terms(symbol)
            
            # Parse comments from the response
            if len(data) > 1:  # Comments are in the second element
                comments = data[1].get('data', {}).get('children', [])
                
                for comment_data in comments:
                    if comment_count >= self.max_comments_per_post:
                        break
                    
                    comment = comment_data.get('data', {})
                    
                    # Check if comment mentions the symbol or company names and has content
                    comment_text = comment.get('body', '')
                    comment_text_lower = comment_text.lower()
                    
                    # Check if any search term is mentioned in the comment
                    mentions_symbol = any(search_term.lower() in comment_text_lower for search_term in search_terms)
                    
                    if mentions_symbol and len(comment_text.strip()) > 10:
                        # Analyze comment sentiment
                        comment_sentiment = self._analyze_text_sentiment(comment_text)
                        
                        if comment_sentiment['score'] != 0:  # Only include if sentiment detected
                            comments_data.append(SentimentData(
                                symbol=symbol,
                                source=SentimentSource.REDDIT,
                                sentiment_score=comment_sentiment['score'],
                                confidence=comment_sentiment['confidence'],
                                timestamp=datetime.fromtimestamp(comment.get('created_utc', 0)),
                                text=comment_text[:200],
                                metadata={
                                    'type': 'comment',
                                    'subreddit': subreddit,
                                    'post_id': post_id,
                                    'comment_id': comment.get('id', ''),
                                    'score': comment.get('score', 0),
                                    'url': f"https://reddit.com{comment.get('permalink', '')}"
                                }
                            ))
                            
                            comment_count += 1
            
            return comments_data
            
        except Exception as e:
            self.logger.warning(f"⚠️ Error fetching comments for post {post_id}: {e}")
            return []
    
    async def _fetch_post_comments(self, post, symbol: str) -> List[SentimentData]:
        """
        Fetch comments from a post that mention the symbol (praw version).
        
        Args:
            post: praw Submission object
            symbol: Stock symbol to search for
            
        Returns:
            List of sentiment data points
        """
        try:
            comments_data = []
            comment_count = 0
            
            # Get comprehensive search terms for this symbol
            search_terms = self._get_search_terms(symbol)
            
            # Replace more=True to get all comments
            post.comments.replace_more(limit=0)  # Don't expand "more comments" links
            
            for comment in post.comments.list():
                if comment_count >= self.max_comments_per_post:
                    break
                
                # Check if comment mentions the symbol or company names and has content
                comment_text = comment.body
                comment_text_lower = comment_text.lower()
                
                # Check if any search term is mentioned in the comment
                mentions_symbol = any(search_term.lower() in comment_text_lower for search_term in search_terms)
                
                if mentions_symbol and len(comment_text.strip()) > 10:
                    # Analyze comment sentiment
                    comment_sentiment = self._analyze_text_sentiment(comment_text)
                    
                    if comment_sentiment['score'] != 0:  # Only include if sentiment detected
                        comments_data.append(SentimentData(
                            symbol=symbol,
                            source=SentimentSource.REDDIT,
                            sentiment_score=comment_sentiment['score'],
                            confidence=comment_sentiment['confidence'],
                            timestamp=datetime.fromtimestamp(comment.created_utc),
                            text=comment_text[:200],
                            metadata={
                                'type': 'comment',
                                'subreddit': post.subreddit.display_name,
                                'post_id': post.id,
                                'comment_id': comment.id,
                                'score': comment.score,
                                'url': f"https://reddit.com{comment.permalink}"
                            }
                        ))
                        
                        comment_count += 1
            
            return comments_data
            
        except Exception as e:
            self.logger.error(f"❌ Error fetching comments for post {post.id}: {e}")
            return []
    
    async def _rate_limit(self):
        """Implement rate limiting for JSON API."""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < (60.0 / self.requests_per_minute):
            sleep_time = (60.0 / self.requests_per_minute) - time_since_last
            await asyncio.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    async def _handle_rate_limit_error(self, retry_count: int = 0):
        """Handle 429 rate limit errors with exponential backoff."""
        if retry_count >= 3:
            self.logger.warning("⚠️ Max retries reached for rate limit, skipping...")
            return False
        
        # Exponential backoff: 2^retry_count * 30 seconds
        backoff_time = min(2 ** retry_count * 30, 300)  # Max 5 minutes
        self.logger.info(f"⏳ Rate limit hit, waiting {backoff_time} seconds before retry...")
        await asyncio.sleep(backoff_time)
        return True
    
    def _analyze_text_sentiment(self, text: str) -> Dict[str, float]:
        """
        Analyze sentiment of text using keyword-based scoring.
        
        Args:
            text: Text to analyze
            
        Returns:
            Dictionary with sentiment score and confidence
        """
        if not text or len(text.strip()) < 10:
            return {'score': 0.0, 'confidence': 0.0}
        
        # Convert to lowercase for analysis
        text_lower = text.lower()
        
        # Count positive and negative keywords
        positive_count = sum(1 for keyword in self.positive_keywords if keyword in text_lower)
        negative_count = sum(1 for keyword in self.negative_keywords if keyword in text_lower)
        
        # Calculate sentiment score (-1.0 to 1.0)
        total_keywords = positive_count + negative_count
        if total_keywords == 0:
            return {'score': 0.0, 'confidence': 0.0}
        
        # Normalize score
        sentiment_score = (positive_count - negative_count) / total_keywords
        
        # Calculate confidence based on keyword density and text length
        keyword_density = total_keywords / len(text_lower.split())
        confidence = min(1.0, keyword_density * 50)  # Scale by density
        
        # Boost confidence for longer texts with more keywords
        if len(text_lower.split()) > 20 and total_keywords > 2:
            confidence = min(1.0, confidence * 1.5)
        
        return {
            'score': sentiment_score,
            'confidence': confidence
        }
    
    def _get_cached_reddit_sentiment(self, symbol: str, hours: int) -> Optional[List[SentimentData]]:
        """Get cached Reddit sentiment data."""
        try:
            cache_file = self.cache_dir / f"reddit_{symbol}_{hours}h.json"
            
            if cache_file.exists():
                # Check if cache is still valid (1 hour)
                cache_age = time.time() - cache_file.stat().st_mtime
                if cache_age < (60 * 60):  # 1 hour (60 minutes * 60 seconds)
                    with open(cache_file, 'r', encoding='utf-8') as f:
                        cached_data = json.load(f)
                    
                    # Convert back to SentimentData objects
                    sentiment_data = []
                    for item in cached_data:
                        sentiment_data.append(SentimentData(
                            symbol=item['symbol'],
                            source=SentimentSource.REDDIT,
                            sentiment_score=item['sentiment_score'],
                            confidence=item['confidence'],
                            timestamp=datetime.fromisoformat(item['timestamp']),
                            text=item['text'],
                            metadata=item['metadata']
                        ))
                    
                    return sentiment_data
            
            return None
            
        except Exception as e:
            self.logger.error(f"❌ Error reading cached Reddit sentiment: {e}")
            return None
    
    def _cache_reddit_sentiment_data(self, symbol: str, sentiment_data: List[SentimentData]):
        """Cache Reddit sentiment data."""
        try:
            cache_file = self.cache_dir / f"reddit_{symbol}_{24}h.json"
            
            # Convert to JSON-serializable format
            cache_data = []
            for item in sentiment_data:
                cache_data.append({
                    'symbol': item.symbol,
                    'source': item.source.value,
                    'sentiment_score': item.sentiment_score,
                    'confidence': item.confidence,
                    'timestamp': item.timestamp.isoformat(),
                    'text': item.text,
                    'metadata': item.metadata
                })
            
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"💾 Cached Reddit sentiment data for {symbol}")
            
        except Exception as e:
            self.logger.error(f"❌ Error caching Reddit sentiment data: {e}")
    
    async def close(self):
        """Close the Reddit client and HTTP client."""
        if self.reddit:
            try:
                self.reddit.close()
            except:
                pass
        
        if hasattr(self, 'client'):
            await self.client.aclose()


class SentimentDataService:
    """
    Comprehensive sentiment data service.
    
    Features:
    - Reddit sentiment analysis (primary focus)
    - Multi-source sentiment aggregation
    - Real-time and historical sentiment
    - Sentiment scoring and normalization
    - Intelligent caching
    - Rate limiting and fallbacks
    """
    
    def __init__(self, config: Dict):
        self.config = config
        self.logger = get_logger()
        self.client = httpx.AsyncClient(timeout=30.0)
        
        # Convert Pydantic model to dict if needed
        if hasattr(config, 'dict'):
            config_dict = config.dict()
        else:
            config_dict = config
        
        # Initialize Reddit service
        self.reddit_service = RedditSentimentService(config_dict)
        
        # API keys - handle both Pydantic model and dict
        if hasattr(config, 'api_keys'):
            # Pydantic model
            api_keys = config.api_keys
            if hasattr(api_keys, 'dict'):
                api_keys = api_keys.dict()
            else:
                api_keys = {}
        else:
            # Dictionary
            api_keys = config_dict.get('api_keys', {})
        
        self.news_api_key = api_keys.get('news_api', '')
        self.alpha_vantage_key = api_keys.get('alpha_vantage', '')
        self.polygon_key = api_keys.get('polygon', '')
        
        # Configuration - handle both Pydantic model and dict
        if hasattr(config, 'data_sources'):
            # Pydantic model
            data_sources = config.data_sources
            if hasattr(data_sources, 'dict'):
                data_sources = data_sources.dict()
            else:
                data_sources = {}
        else:
            # Dictionary
            data_sources = config_dict.get('data_sources', {})
        
        self.cache_duration = data_sources.get('cache_duration', 300)
        self.max_requests_per_minute = 60
        self.last_request_time = 0
        
        # Cache
        self.cache_dir = Path("data/cache/sentiment")
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
    async def get_sentiment_data(self, symbol: str, hours: int = 24) -> List[SentimentData]:
        """
        Get sentiment data for a symbol (primarily Reddit for now).
        
        Args:
            symbol: Stock symbol
            hours: Hours of historical data to fetch
            
        Returns:
            List of sentiment data points
        """
        try:
            # Check cache first
            cached_data = self._get_cached_sentiment(symbol, hours)
            if cached_data:
                self.logger.info(f"📊 Using cached sentiment data for {symbol}")
                return cached_data
            
            self.logger.info(f"🔍 Fetching sentiment data for {symbol}...")
            
            # Get Reddit sentiment (primary source)
            reddit_sentiment = await self.reddit_service.get_reddit_sentiment(symbol, hours)
            
            # TODO: Add other sources (news, Twitter, etc.) here
            all_sentiment_data = reddit_sentiment
            
            # Cache the results
            if all_sentiment_data:
                self._cache_sentiment_data(symbol, all_sentiment_data)
            
            self.logger.info(f"✅ Found {len(all_sentiment_data)} sentiment data points for {symbol}")
            return all_sentiment_data
            
        except Exception as e:
            self.logger.error(f"❌ Error fetching sentiment data for {symbol}: {e}")
            return []
    
    async def _fetch_news_sentiment(self, symbol: str, hours: int) -> List[SentimentData]:
        """Fetch news sentiment from multiple sources."""
        sentiment_data = []
        
        # NewsAPI.org
        if self.news_api_key:
            try:
                news_data = await self._fetch_newsapi_news(symbol, hours)
                sentiment_data.extend(news_data)
            except Exception as e:
                self.logger.warning(f"⚠️ Error fetching NewsAPI data: {e}")
        
        # Alpha Vantage News
        if self.alpha_vantage_key:
            try:
                news_data = await self._fetch_alphavantage_news(symbol, hours)
                sentiment_data.extend(news_data)
            except Exception as e:
                self.logger.warning(f"⚠️ Error fetching Alpha Vantage news: {e}")
        
        return sentiment_data
    
    async def _fetch_newsapi_news(self, symbol: str, hours: int) -> List[SentimentData]:
        """Fetch news from NewsAPI.org."""
        try:
            # Calculate date range
            end_date = datetime.now()
            start_date = end_date - timedelta(hours=hours)
            
            url = "https://newsapi.org/v2/everything"
            params = {
                'q': f'"{symbol}" OR "{symbol} stock" OR "{symbol} shares"',
                'from': start_date.strftime('%Y-%m-%d'),
                'to': end_date.strftime('%Y-%m-%d'),
                'sortBy': 'publishedAt',
                'language': 'en',
                'pageSize': 50,
                'apiKey': self.news_api_key
            }
            
            await self._rate_limit()
            
            response = await self.client.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            sentiment_data = []
            
            if 'articles' in data:
                for article in data['articles']:
                    # Calculate simple sentiment score
                    sentiment_score = self._calculate_simple_sentiment(
                        article.get('title', '') + ' ' + article.get('description', '')
                    )
                    
                    if sentiment_score != 0:  # Only include if sentiment detected
                        sentiment_data.append(SentimentData(
                            symbol=symbol,
                            source=SentimentSource.NEWS_API,
                            sentiment_score=sentiment_score,
                            confidence=0.5,  # Default confidence for news
                            timestamp=datetime.fromisoformat(article['publishedAt'].replace('Z', '+00:00')),
                            text=article.get('title', '') + ' ' + article.get('description', ''),
                            metadata={
                                'source': article.get('source', {}).get('name', ''),
                                'url': article.get('url', ''),
                                'author': article.get('author', '')
                            }
                        ))
            
            return sentiment_data
            
        except Exception as e:
            self.logger.error(f"❌ Error fetching NewsAPI data: {e}")
            return []
    
    async def _fetch_alphavantage_news(self, symbol: str, hours: int) -> List[SentimentData]:
        """Fetch news from Alpha Vantage."""
        try:
            url = "https://www.alphavantage.co/query"
            params = {
                'function': 'NEWS_SENTIMENT',
                'tickers': symbol,
                'apikey': self.alpha_vantage_key,
                'limit': 50
            }
            
            await self._rate_limit()
            
            response = await self.client.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            sentiment_data = []
            
            if 'feed' in data:
                for article in data['feed']:
                    # Get sentiment score from Alpha Vantage
                    sentiment_score = float(article.get('overall_sentiment_score', 0))
                    
                    if sentiment_score != 0:  # Only include if sentiment detected
                        sentiment_data.append(SentimentData(
                            symbol=symbol,
                            source=SentimentSource.ALPHA_VANTAGE,
                            sentiment_score=sentiment_score,
                            confidence=float(article.get('overall_sentiment_label_confidence', 0.5)),
                            timestamp=datetime.fromisoformat(article['time_published']),
                            text=article.get('title', '') + ' ' + article.get('summary', ''),
                            metadata={
                                'source': article.get('source', ''),
                                'url': article.get('url', ''),
                                'sentiment_label': article.get('overall_sentiment_label', '')
                            }
                        ))
            
            return sentiment_data
            
        except Exception as e:
            self.logger.error(f"❌ Error fetching Alpha Vantage news: {e}")
            return []
    
    async def _fetch_social_sentiment(self, symbol: str, hours: int) -> List[SentimentData]:
        """Fetch social media sentiment (placeholder for future implementation)."""
        # TODO: Implement Twitter, Reddit sentiment fetching
        return []
    
    def _calculate_simple_sentiment(self, text: str) -> float:
        """
        Calculate simple sentiment score using keyword analysis.
        
        Args:
            text: Text to analyze
            
        Returns:
            Sentiment score (-1.0 to 1.0)
        """
        if not text:
            return 0.0
        
        # Simple keyword-based sentiment analysis
        positive_words = {
            'bullish', 'positive', 'growth', 'profit', 'gain', 'up', 'rise', 'surge',
            'breakout', 'rally', 'soar', 'jump', 'climb', 'success', 'win', 'good',
            'great', 'amazing', 'incredible', 'fantastic', 'excellent', 'outstanding'
        }
        
        negative_words = {
            'bearish', 'negative', 'loss', 'drop', 'fall', 'decline', 'crash', 'down',
            'plunge', 'tank', 'dump', 'sell', 'bad', 'terrible', 'awful', 'horrible',
            'disaster', 'catastrophe', 'failure', 'lose', 'losing', 'lost'
        }
        
        text_lower = text.lower()
        positive_count = sum(1 for word in positive_words if word in text_lower)
        negative_count = sum(1 for word in negative_words if word in text_lower)
        
        total_words = positive_count + negative_count
        if total_words == 0:
            return 0.0
        
        return (positive_count - negative_count) / total_words
    
    async def _rate_limit(self):
        """Implement rate limiting."""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < (60.0 / self.max_requests_per_minute):
            sleep_time = (60.0 / self.max_requests_per_minute) - time_since_last
            await asyncio.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def _get_cached_sentiment(self, symbol: str, hours: int) -> Optional[List[SentimentData]]:
        """Get cached sentiment data."""
        try:
            cache_file = self.cache_dir / f"sentiment_{symbol}_{hours}h.json"
            
            if cache_file.exists():
                # Check if cache is still valid - use 1 hour cache duration for backtesting
                cache_age = time.time() - cache_file.stat().st_mtime
                # Use 1 hour cache duration for more realistic backtesting
                cache_duration_minutes = min(self.cache_duration, 60)  # Max 1 hour (60 minutes) for backtesting
                cache_duration_seconds = cache_duration_minutes * 60  # Convert to seconds
                if cache_age < cache_duration_seconds:
                    with open(cache_file, 'r', encoding='utf-8') as f:
                        cached_data = json.load(f)
                    
                    # Convert back to SentimentData objects
                    sentiment_data = []
                    for item in cached_data:
                        sentiment_data.append(SentimentData(
                            symbol=item['symbol'],
                            source=SentimentSource(item['source']),
                            sentiment_score=item['sentiment_score'],
                            confidence=item['confidence'],
                            timestamp=datetime.fromisoformat(item['timestamp']),
                            text=item['text'],
                            metadata=item['metadata']
                        ))
                    
                    return sentiment_data
            
            return None
            
        except Exception as e:
            self.logger.error(f"❌ Error reading cached sentiment: {e}")
            return None
    
    def _cache_sentiment_data(self, symbol: str, sentiment_data: List[SentimentData]):
        """Cache sentiment data."""
        try:
            cache_file = self.cache_dir / f"sentiment_{symbol}_{24}h.json"
            
            # Convert to JSON-serializable format
            cache_data = []
            for item in sentiment_data:
                cache_data.append({
                    'symbol': item.symbol,
                    'source': item.source.value,
                    'sentiment_score': item.sentiment_score,
                    'confidence': item.confidence,
                    'timestamp': item.timestamp.isoformat(),
                    'text': item.text,
                    'metadata': item.metadata
                })
            
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"💾 Cached sentiment data for {symbol}")
            
        except Exception as e:
            self.logger.error(f"❌ Error caching sentiment data: {e}")
    
    def get_aggregated_sentiment(self, sentiment_data: List[SentimentData]) -> Dict[str, float]:
        """
        Aggregate sentiment data into summary metrics.
        
        Args:
            sentiment_data: List of sentiment data points
            
        Returns:
            Dictionary with aggregated sentiment metrics
        """
        if not sentiment_data:
            return {
                'overall_sentiment': 0.0,
                'confidence': 0.0,
                'volume': 0,
                'positive_ratio': 0.0,
                'negative_ratio': 0.0,
                'neutral_ratio': 0.0
            }
        
        # Calculate weighted average sentiment
        total_weight = 0
        weighted_sentiment = 0
        
        positive_count = 0
        negative_count = 0
        neutral_count = 0
        
        for item in sentiment_data:
            weight = item.confidence
            total_weight += weight
            weighted_sentiment += item.sentiment_score * weight
            
            # Count sentiment categories
            if item.sentiment_score > 0.1:
                positive_count += 1
            elif item.sentiment_score < -0.1:
                negative_count += 1
            else:
                neutral_count += 1
        
        overall_sentiment = weighted_sentiment / total_weight if total_weight > 0 else 0.0
        
        # Calculate ratios
        total_items = len(sentiment_data)
        positive_ratio = positive_count / total_items if total_items > 0 else 0.0
        negative_ratio = negative_count / total_items if total_items > 0 else 0.0
        neutral_ratio = neutral_count / total_items if total_items > 0 else 0.0
        
        # Calculate overall confidence
        avg_confidence = sum(item.confidence for item in sentiment_data) / total_items if total_items > 0 else 0.0
        
        return {
            'overall_sentiment': overall_sentiment,
            'confidence': avg_confidence,
            'volume': total_items,
            'positive_ratio': positive_ratio,
            'negative_ratio': negative_ratio,
            'neutral_ratio': neutral_ratio
        }
    
    async def close(self):
        """Close the HTTP client and Reddit service."""
        await self.client.aclose()
        await self.reddit_service.close() 