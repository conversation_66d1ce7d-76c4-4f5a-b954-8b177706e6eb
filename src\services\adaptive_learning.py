#!/usr/bin/env python3
"""
Adaptive Learning Service for AI-Nvestor.

This module provides:
- Machine learning-based variable weight optimization
- Iterative intelligence for sentiment data significance
- Historical performance tracking and analysis
- Dynamic weight adjustment based on market conditions
- Feature importance analysis
- Continuous learning and adaptation
"""

import pandas as pd
import numpy as np
import json
import pickle
import asyncio
from typing import Dict, List, Optional, Any, Tuple, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
import time
import hashlib
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression, Ridge
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.feature_selection import SelectKBest, f_regression
import warnings
warnings.filterwarnings('ignore')

from utils.logging import get_logger
from services.sentiment_data import SentimentData, SentimentSource
from services.indicators import IndicatorResult, SignalType


class LearningMode(Enum):
    """Learning modes for adaptive weights."""
    SENTIMENT_ONLY = "sentiment_only"
    TECHNICAL_ONLY = "technical_only"
    HYBRID = "hybrid"
    FULL_SYSTEM = "full_system"


class WeightCategory(Enum):
    """Categories of weights for learning."""
    SENTIMENT = "sentiment"
    TECHNICAL = "technical"
    MARKET_CONDITION = "market_condition"
    TIME_BASED = "time_based"
    VOLATILITY = "volatility"


@dataclass
class VariableWeight:
    """Represents a variable weight with learning capabilities."""
    name: str
    category: WeightCategory
    current_weight: float
    base_weight: float
    min_weight: float = 0.0
    max_weight: float = 1.0
    learning_rate: float = 0.01
    performance_history: List[float] = field(default_factory=list)
    last_updated: datetime = field(default_factory=datetime.now)
    confidence: float = 0.5
    significance_score: float = 0.5
    
    def update_weight(self, new_weight: float, performance: float = None):
        """Update weight with learning."""
        # Store performance if provided
        if performance is not None:
            self.performance_history.append(performance)
            # Keep only last 100 performance records
            if len(self.performance_history) > 100:
                self.performance_history = self.performance_history[-100:]
        
        # Apply learning rate
        weight_change = (new_weight - self.current_weight) * self.learning_rate
        self.current_weight = np.clip(
            self.current_weight + weight_change,
            self.min_weight,
            self.max_weight
        )
        
        # Update confidence based on performance consistency
        if len(self.performance_history) > 10:
            recent_performance = self.performance_history[-10:]
            self.confidence = np.std(recent_performance)  # Lower std = higher confidence
        
        self.last_updated = datetime.now()


@dataclass
class LearningResult:
    """Result of learning process."""
    symbol: str
    timestamp: datetime
    weights: Dict[str, VariableWeight]
    performance_metrics: Dict[str, float]
    feature_importance: Dict[str, float]
    model_accuracy: float
    learning_mode: LearningMode
    metadata: Dict[str, Any]


class AdaptiveLearningService:
    """
    Adaptive learning service for variable weights and significance.
    
    Features:
    - Machine learning-based weight optimization
    - Sentiment data significance analysis
    - Historical performance tracking
    - Dynamic weight adjustment
    - Feature importance analysis
    - Continuous learning and adaptation
    """
    
    def __init__(self, config: Dict):
        self.config = config
        self.logger = get_logger()
        
        # Learning configuration
        self.learning_config = config.get('adaptive_learning', {})
        self.enabled = self.learning_config.get('enabled', True)
        self.learning_rate = self.learning_config.get('learning_rate', 0.01)
        self.min_data_points = self.learning_config.get('min_data_points', 50)
        self.retrain_frequency = self.learning_config.get('retrain_frequency', 24)  # hours
        
        # Data storage
        self.data_dir = Path("data/adaptive_learning")
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # Model storage
        self.models_dir = self.data_dir / "models"
        self.models_dir.mkdir(exist_ok=True)
        
        # Initialize weights
        self.variable_weights = self._initialize_weights()
        
        # Performance tracking
        self.performance_history = {}
        self.feature_importance_history = {}
        
        # Learning models
        self.models = {}
        self.scalers = {}
        self.last_training = {}
        
        # Market condition tracking
        self.market_conditions = {}
        
        self.logger.info("🚀 Adaptive Learning Service initialized")
    
    def _initialize_weights(self) -> Dict[str, VariableWeight]:
        """Initialize variable weights."""
        weights = {}
        
        # Sentiment weights
        sentiment_weights = {
            'reddit_sentiment': VariableWeight(
                name='reddit_sentiment',
                category=WeightCategory.SENTIMENT,
                current_weight=0.3,
                base_weight=0.3,
                learning_rate=self.learning_rate
            ),
            'news_sentiment': VariableWeight(
                name='news_sentiment',
                category=WeightCategory.SENTIMENT,
                current_weight=0.2,
                base_weight=0.2,
                learning_rate=self.learning_rate
            ),
            'social_sentiment': VariableWeight(
                name='social_sentiment',
                category=WeightCategory.SENTIMENT,
                current_weight=0.1,
                base_weight=0.1,
                learning_rate=self.learning_rate
            ),
            'sentiment_volume': VariableWeight(
                name='sentiment_volume',
                category=WeightCategory.SENTIMENT,
                current_weight=0.15,
                base_weight=0.15,
                learning_rate=self.learning_rate
            ),
            'sentiment_confidence': VariableWeight(
                name='sentiment_confidence',
                category=WeightCategory.SENTIMENT,
                current_weight=0.25,
                base_weight=0.25,
                learning_rate=self.learning_rate
            )
        }
        
        # Technical weights
        technical_weights = {
            'trend_indicators': VariableWeight(
                name='trend_indicators',
                category=WeightCategory.TECHNICAL,
                current_weight=0.4,
                base_weight=0.4,
                learning_rate=self.learning_rate
            ),
            'momentum_indicators': VariableWeight(
                name='momentum_indicators',
                category=WeightCategory.TECHNICAL,
                current_weight=0.3,
                base_weight=0.3,
                learning_rate=self.learning_rate
            ),
            'volatility_indicators': VariableWeight(
                name='volatility_indicators',
                category=WeightCategory.TECHNICAL,
                current_weight=0.2,
                base_weight=0.2,
                learning_rate=self.learning_rate
            ),
            'volume_indicators': VariableWeight(
                name='volume_indicators',
                category=WeightCategory.TECHNICAL,
                current_weight=0.1,
                base_weight=0.1,
                learning_rate=self.learning_rate
            )
        }
        
        # Market condition weights
        market_weights = {
            'market_volatility': VariableWeight(
                name='market_volatility',
                category=WeightCategory.MARKET_CONDITION,
                current_weight=0.2,
                base_weight=0.2,
                learning_rate=self.learning_rate
            ),
            'market_trend': VariableWeight(
                name='market_trend',
                category=WeightCategory.MARKET_CONDITION,
                current_weight=0.3,
                base_weight=0.3,
                learning_rate=self.learning_rate
            ),
            'market_sentiment': VariableWeight(
                name='market_sentiment',
                category=WeightCategory.MARKET_CONDITION,
                current_weight=0.25,
                base_weight=0.25,
                learning_rate=self.learning_rate
            ),
            'market_volume': VariableWeight(
                name='market_volume',
                category=WeightCategory.MARKET_CONDITION,
                current_weight=0.25,
                base_weight=0.25,
                learning_rate=self.learning_rate
            )
        }
        
        weights.update(sentiment_weights)
        weights.update(technical_weights)
        weights.update(market_weights)
        
        return weights
    
    async def learn_from_data(self, symbol: str, data: pd.DataFrame, 
                             sentiment_data: List[SentimentData] = None,
                             technical_indicators: Dict[str, IndicatorResult] = None,
                             actual_outcome: float = None) -> LearningResult:
        """
        Learn from new data and update weights.
        
        Args:
            symbol: Stock symbol
            data: OHLCV data
            sentiment_data: Sentiment data points
            technical_indicators: Technical indicator results
            actual_outcome: Actual price movement (for supervised learning)
            
        Returns:
            LearningResult with updated weights and metrics
        """
        if not self.enabled:
            return self._get_default_result(symbol)
        
        try:
            self.logger.info(f"🧠 Starting adaptive learning for {symbol}")
            
            # Prepare features
            features = await self._prepare_features(symbol, data, sentiment_data, technical_indicators)
            
            if features is None or len(features) < self.min_data_points:
                self.logger.warning(f"⚠️ Insufficient data for learning: {len(features) if features is not None else 0} points")
                return self._get_default_result(symbol)
            
            # Create target variable if actual_outcome provided
            target = None
            if actual_outcome is not None:
                target = self._create_target_variable(data, actual_outcome)
            
            # Train models and update weights
            learning_result = await self._train_models_and_update_weights(
                symbol, features, target, sentiment_data, technical_indicators
            )
            
            # Store learning result
            await self._store_learning_result(symbol, learning_result)
            
            self.logger.info(f"✅ Adaptive learning completed for {symbol}")
            return learning_result
            
        except Exception as e:
            self.logger.error(f"❌ Error in adaptive learning for {symbol}: {e}")
            return self._get_default_result(symbol)
    
    async def _prepare_features(self, symbol: str, data: pd.DataFrame,
                               sentiment_data: List[SentimentData] = None,
                               technical_indicators: Dict[str, IndicatorResult] = None) -> Optional[pd.DataFrame]:
        """Prepare features for learning."""
        try:
            features = []
            
            # Technical features
            if technical_indicators:
                tech_features = self._extract_technical_features(technical_indicators)
                if not tech_features.empty:
                    features.append(tech_features)
            
            # Sentiment features
            if sentiment_data:
                sentiment_features = self._extract_sentiment_features(sentiment_data)
                if not sentiment_features.empty:
                    features.append(sentiment_features)
            
            # Market condition features
            market_features = self._extract_market_features(data)
            if not market_features.empty:
                features.append(market_features)
            
            # Time-based features
            time_features = self._extract_time_features(data)
            if not time_features.empty:
                features.append(time_features)
            
            # Combine all features
            if features:
                combined_features = pd.concat(features, axis=1)
                combined_features = combined_features.fillna(0)
                return combined_features
            
            return None
            
        except Exception as e:
            self.logger.error(f"❌ Error preparing features: {e}")
            return None
    
    def _extract_technical_features(self, indicators: Dict[str, IndicatorResult]) -> pd.DataFrame:
        """Extract technical indicator features."""
        features = {}
        
        for name, result in indicators.items():
            if hasattr(result, 'value') and result.value is not None:
                features[f'tech_{name}_value'] = [result.value]
                features[f'tech_{name}_confidence'] = [result.confidence if result.confidence else 0.0]
                
                # Signal encoding
                if hasattr(result, 'signal') and result.signal:
                    signal_value = self._encode_signal(result.signal)
                    features[f'tech_{name}_signal'] = [signal_value]
        
        return pd.DataFrame(features)
    
    def _extract_sentiment_features(self, sentiment_data: List[SentimentData]) -> pd.DataFrame:
        """Extract sentiment features."""
        if not sentiment_data:
            return pd.DataFrame()
        
        features = {}
        
        # Aggregate sentiment metrics
        sentiment_scores = [item.sentiment_score for item in sentiment_data]
        confidence_scores = [item.confidence for item in sentiment_data]
        
        features['sentiment_mean'] = [np.mean(sentiment_scores)]
        features['sentiment_std'] = [np.std(sentiment_scores)]
        features['sentiment_volume'] = [len(sentiment_data)]
        features['sentiment_confidence_mean'] = [np.mean(confidence_scores)]
        
        # Source-specific features
        sources = [item.source.value for item in sentiment_data]
        for source in set(sources):
            source_data = [item for item in sentiment_data if item.source.value == source]
            if source_data:
                source_scores = [item.sentiment_score for item in source_data]
                features[f'sentiment_{source}_mean'] = [np.mean(source_scores)]
                features[f'sentiment_{source}_volume'] = [len(source_data)]
        
        # Sentiment distribution
        positive_count = sum(1 for score in sentiment_scores if score > 0.1)
        negative_count = sum(1 for score in sentiment_scores if score < -0.1)
        neutral_count = len(sentiment_scores) - positive_count - negative_count
        
        features['sentiment_positive_ratio'] = [positive_count / len(sentiment_scores) if sentiment_scores else 0]
        features['sentiment_negative_ratio'] = [negative_count / len(sentiment_scores) if sentiment_scores else 0]
        features['sentiment_neutral_ratio'] = [neutral_count / len(sentiment_scores) if sentiment_scores else 0]
        
        return pd.DataFrame(features)
    
    def _extract_market_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Extract market condition features."""
        features = {}
        
        if len(data) < 20:
            return pd.DataFrame()
        
        # Price-based features
        features['price_change'] = [(data['Close'].iloc[-1] - data['Close'].iloc[-2]) / data['Close'].iloc[-2]]
        features['price_volatility'] = [data['Close'].pct_change().std()]
        features['price_trend'] = [(data['Close'].iloc[-1] - data['Close'].iloc[-20]) / data['Close'].iloc[-20]]
        
        # Volume-based features
        features['volume_change'] = [(data['Volume'].iloc[-1] - data['Volume'].iloc[-2]) / data['Volume'].iloc[-2] if data['Volume'].iloc[-2] > 0 else 0]
        features['volume_trend'] = [(data['Volume'].iloc[-1] - data['Volume'].iloc[-20]) / data['Volume'].iloc[-20] if data['Volume'].iloc[-20] > 0 else 0]
        
        # Market condition indicators
        features['market_volatility'] = [data['Close'].pct_change().rolling(20).std().iloc[-1]]
        features['market_trend_strength'] = [abs(features['price_trend'][0])]
        
        return pd.DataFrame(features)
    
    def _extract_time_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Extract time-based features."""
        features = {}
        
        # Time-based features
        current_time = datetime.now()
        features['hour_of_day'] = [current_time.hour]
        features['day_of_week'] = [current_time.weekday()]
        features['month'] = [current_time.month]
        
        # Market session features
        features['is_market_hours'] = [1 if 9 <= current_time.hour <= 16 else 0]
        features['is_weekend'] = [1 if current_time.weekday() >= 5 else 0]
        
        return pd.DataFrame(features)
    
    def _encode_signal(self, signal: SignalType) -> float:
        """Encode signal type to numerical value."""
        signal_encoding = {
            SignalType.STRONG_SELL: -1.0,
            SignalType.SELL: -0.5,
            SignalType.HOLD: 0.0,
            SignalType.BUY: 0.5,
            SignalType.STRONG_BUY: 1.0
        }
        return signal_encoding.get(signal, 0.0)
    
    def _create_target_variable(self, data: pd.DataFrame, actual_outcome: float) -> pd.Series:
        """Create target variable for supervised learning."""
        # Use actual_outcome if provided, otherwise use future price movement
        if actual_outcome is not None:
            return pd.Series([actual_outcome])
        else:
            # Calculate future price movement (next day)
            if len(data) > 1:
                future_return = (data['Close'].iloc[-1] - data['Close'].iloc[-2]) / data['Close'].iloc[-2]
                return pd.Series([future_return])
            else:
                return pd.Series([0.0])
    
    async def _train_models_and_update_weights(self, symbol: str, features: pd.DataFrame,
                                               target: pd.Series = None,
                                               sentiment_data: List[SentimentData] = None,
                                               technical_indicators: Dict[str, IndicatorResult] = None) -> LearningResult:
        """Train models and update weights."""
        try:
            # Initialize models if not exists
            if symbol not in self.models:
                self.models[symbol] = {
                    'sentiment_model': RandomForestRegressor(n_estimators=100, random_state=42),
                    'technical_model': GradientBoostingRegressor(n_estimators=100, random_state=42),
                    'hybrid_model': Ridge(alpha=1.0)
                }
                self.scalers[symbol] = StandardScaler()
            
            # Prepare data
            X = features.fillna(0)
            if target is not None and len(target) > 0:
                y = target
            else:
                # Create synthetic target for unsupervised learning
                y = self._create_synthetic_target(X)
            
            # Scale features
            X_scaled = self.scalers[symbol].fit_transform(X)
            
            # Train models
            model_performances = {}
            feature_importance = {}
            
            for model_name, model in self.models[symbol].items():
                try:
                    # Train model
                    model.fit(X_scaled, y)
                    
                    # Calculate performance
                    if len(y) > 5:
                        predictions = model.predict(X_scaled)
                        mse = mean_squared_error(y, predictions)
                        r2 = r2_score(y, predictions)
                        model_performances[model_name] = {
                            'mse': mse,
                            'r2': r2,
                            'mae': mean_absolute_error(y, predictions)
                        }
                        
                        # Feature importance
                        if hasattr(model, 'feature_importances_'):
                            importance = model.feature_importances_
                            feature_importance[model_name] = dict(zip(X.columns, importance))
                    
                except Exception as e:
                    self.logger.warning(f"⚠️ Error training {model_name}: {e}")
            
            # Update weights based on feature importance and performance
            updated_weights = self._update_weights_from_models(
                symbol, feature_importance, model_performances, sentiment_data, technical_indicators
            )
            
            # Create learning result
            learning_result = LearningResult(
                symbol=symbol,
                timestamp=datetime.now(),
                weights=updated_weights,
                performance_metrics=model_performances,
                feature_importance=feature_importance,
                model_accuracy=np.mean([perf.get('r2', 0) for perf in model_performances.values()]),
                learning_mode=LearningMode.HYBRID,
                metadata={
                    'features_count': len(X.columns),
                    'data_points': len(X),
                    'sentiment_data_count': len(sentiment_data) if sentiment_data else 0,
                    'technical_indicators_count': len(technical_indicators) if technical_indicators else 0
                }
            )
            
            return learning_result
            
        except Exception as e:
            self.logger.error(f"❌ Error training models: {e}")
            return self._get_default_result(symbol)
    
    def _create_synthetic_target(self, features: pd.DataFrame) -> pd.Series:
        """Create synthetic target for unsupervised learning."""
        # Use a combination of features to create a synthetic target
        if 'sentiment_mean' in features.columns:
            sentiment_component = features['sentiment_mean'] * 0.3
        else:
            sentiment_component = pd.Series([0.0] * len(features))
        
        if 'price_change' in features.columns:
            price_component = features['price_change'] * 0.4
        else:
            price_component = pd.Series([0.0] * len(features))
        
        if 'volume_change' in features.columns:
            volume_component = features['volume_change'] * 0.3
        else:
            volume_component = pd.Series([0.0] * len(features))
        
        synthetic_target = sentiment_component + price_component + volume_component
        return synthetic_target
    
    def _update_weights_from_models(self, symbol: str, feature_importance: Dict[str, Dict[str, float]],
                                   model_performances: Dict[str, Dict[str, float]],
                                   sentiment_data: List[SentimentData] = None,
                                   technical_indicators: Dict[str, IndicatorResult] = None) -> Dict[str, VariableWeight]:
        """Update weights based on model performance and feature importance."""
        updated_weights = self.variable_weights.copy()
        
        try:
            # Calculate overall feature importance
            overall_importance = {}
            for model_name, importance_dict in feature_importance.items():
                for feature, importance in importance_dict.items():
                    if feature not in overall_importance:
                        overall_importance[feature] = []
                    overall_importance[feature].append(importance)
            
            # Average importance across models
            for feature, importances in overall_importance.items():
                overall_importance[feature] = np.mean(importances)
            
            # Update sentiment weights
            if sentiment_data:
                sentiment_importance = self._calculate_sentiment_importance(overall_importance)
                for weight_name, importance in sentiment_importance.items():
                    if weight_name in updated_weights:
                        # Update weight based on importance
                        new_weight = updated_weights[weight_name].base_weight * (1 + importance)
                        updated_weights[weight_name].update_weight(new_weight, importance)
            
            # Update technical weights
            if technical_indicators:
                technical_importance = self._calculate_technical_importance(overall_importance)
                for weight_name, importance in technical_importance.items():
                    if weight_name in updated_weights:
                        new_weight = updated_weights[weight_name].base_weight * (1 + importance)
                        updated_weights[weight_name].update_weight(new_weight, importance)
            
            # Update market condition weights
            market_importance = self._calculate_market_importance(overall_importance)
            for weight_name, importance in market_importance.items():
                if weight_name in updated_weights:
                    new_weight = updated_weights[weight_name].base_weight * (1 + importance)
                    updated_weights[weight_name].update_weight(new_weight, importance)
            
        except Exception as e:
            self.logger.error(f"❌ Error updating weights: {e}")
        
        return updated_weights
    
    def _calculate_sentiment_importance(self, overall_importance: Dict[str, float]) -> Dict[str, float]:
        """Calculate sentiment feature importance."""
        sentiment_importance = {}
        
        # Map sentiment features to weights
        sentiment_mapping = {
            'sentiment_mean': 'reddit_sentiment',
            'sentiment_volume': 'sentiment_volume',
            'sentiment_confidence_mean': 'sentiment_confidence',
            'sentiment_reddit_mean': 'reddit_sentiment',
            'sentiment_news_mean': 'news_sentiment',
            'sentiment_social_mean': 'social_sentiment'
        }
        
        for feature, importance in overall_importance.items():
            if feature in sentiment_mapping:
                weight_name = sentiment_mapping[feature]
                sentiment_importance[weight_name] = importance
        
        return sentiment_importance
    
    def _calculate_technical_importance(self, overall_importance: Dict[str, float]) -> Dict[str, float]:
        """Calculate technical feature importance."""
        technical_importance = {}
        
        # Map technical features to weights
        technical_mapping = {
            'tech_sma_value': 'trend_indicators',
            'tech_ema_value': 'trend_indicators',
            'tech_macd_value': 'trend_indicators',
            'tech_rsi_value': 'momentum_indicators',
            'tech_stochastic_value': 'momentum_indicators',
            'tech_bollinger_value': 'volatility_indicators',
            'tech_volume_value': 'volume_indicators'
        }
        
        for feature, importance in overall_importance.items():
            if feature in technical_mapping:
                weight_name = technical_mapping[feature]
                if weight_name not in technical_importance:
                    technical_importance[weight_name] = []
                technical_importance[weight_name].append(importance)
        
        # Average importance for each weight category
        for weight_name, importances in technical_importance.items():
            technical_importance[weight_name] = np.mean(importances)
        
        return technical_importance
    
    def _calculate_market_importance(self, overall_importance: Dict[str, float]) -> Dict[str, float]:
        """Calculate market condition feature importance."""
        market_importance = {}
        
        # Map market features to weights
        market_mapping = {
            'market_volatility': 'market_volatility',
            'market_trend_strength': 'market_trend',
            'sentiment_mean': 'market_sentiment',
            'volume_change': 'market_volume'
        }
        
        for feature, importance in overall_importance.items():
            if feature in market_mapping:
                weight_name = market_mapping[feature]
                market_importance[weight_name] = importance
        
        return market_importance
    
    async def _store_learning_result(self, symbol: str, learning_result: LearningResult):
        """Store learning result for future reference."""
        try:
            # Save to file
            result_file = self.data_dir / f"{symbol}_learning_result.json"
            
            # Convert to JSON-serializable format
            result_data = {
                'symbol': learning_result.symbol,
                'timestamp': learning_result.timestamp.isoformat(),
                'weights': {
                    name: {
                        'current_weight': weight.current_weight,
                        'base_weight': weight.base_weight,
                        'confidence': weight.confidence,
                        'significance_score': weight.significance_score,
                        'last_updated': weight.last_updated.isoformat()
                    }
                    for name, weight in learning_result.weights.items()
                },
                'performance_metrics': learning_result.performance_metrics,
                'feature_importance': learning_result.feature_importance,
                'model_accuracy': learning_result.model_accuracy,
                'learning_mode': learning_result.learning_mode.value,
                'metadata': learning_result.metadata
            }
            
            with open(result_file, 'w') as f:
                json.dump(result_data, f, indent=2, default=str)
            
            # Update performance history
            self.performance_history[symbol] = learning_result.performance_metrics
            self.feature_importance_history[symbol] = learning_result.feature_importance
            
        except Exception as e:
            self.logger.error(f"❌ Error storing learning result: {e}")
    
    def get_optimized_weights(self, symbol: str, category: WeightCategory = None) -> Dict[str, float]:
        """Get optimized weights for a symbol."""
        if symbol not in self.variable_weights:
            return {}
        
        weights = {}
        for name, weight in self.variable_weights.items():
            if category is None or weight.category == category:
                weights[name] = weight.current_weight
        
        return weights
    
    def get_feature_importance(self, symbol: str) -> Dict[str, float]:
        """Get feature importance for a symbol."""
        return self.feature_importance_history.get(symbol, {})
    
    def get_performance_metrics(self, symbol: str) -> Dict[str, float]:
        """Get performance metrics for a symbol."""
        return self.performance_history.get(symbol, {})
    
    def _get_default_result(self, symbol: str) -> LearningResult:
        """Get default learning result when learning is disabled or fails."""
        return LearningResult(
            symbol=symbol,
            timestamp=datetime.now(),
            weights=self.variable_weights,
            performance_metrics={},
            feature_importance={},
            model_accuracy=0.0,
            learning_mode=LearningMode.HYBRID,
            metadata={'error': 'Learning disabled or failed'}
        )
    
    async def close(self):
        """Close the adaptive learning service."""
        try:
            # Save current state
            state_file = self.data_dir / "adaptive_learning_state.json"
            state_data = {
                'variable_weights': {
                    name: {
                        'current_weight': weight.current_weight,
                        'base_weight': weight.base_weight,
                        'confidence': weight.confidence,
                        'significance_score': weight.significance_score,
                        'last_updated': weight.last_updated.isoformat()
                    }
                    for name, weight in self.variable_weights.items()
                },
                'performance_history': self.performance_history,
                'feature_importance_history': self.feature_importance_history,
                'last_training': self.last_training
            }
            
            with open(state_file, 'w') as f:
                json.dump(state_data, f, indent=2, default=str)
            
            self.logger.info("✅ Adaptive Learning Service closed")
            
        except Exception as e:
            self.logger.error(f"❌ Error closing adaptive learning service: {e}") 