# 📊 Constants and Settings

> **Centralized application constants, enums, and default configurations**

## 🌟 **Overview**

The `constants/` module provides a centralized location for all application constants, enums, default values, and system limits. This ensures consistency across the application and makes configuration management easier.

## 📁 **Components**

### **⚙️ `settings.py` - Application Constants**
> **Comprehensive constants, enums, and default configurations**

## 🏷️ **Enums and Types**

### **Trading Enums**

#### **TradingMode**
```python
class TradingMode(Enum):
    PAPER = "paper"     # Paper trading (simulation)
    LIVE = "live"       # Live trading with real money
    DEMO = "demo"       # Demo mode for testing
```

#### **SignalType** (Centralized)
```python
class SignalType(Enum):
    BUY = "BUY"                    # Moderate bullish signal
    SELL = "SELL"                  # Moderate bearish signal  
    HOLD = "HOLD"                  # Neutral/no action
    STRONG_BUY = "STRONG_BUY"      # Strong bullish signal
    STRONG_SELL = "STRONG_SELL"    # Strong bearish signal
```

#### **DataSource**
```python
class DataSource(Enum):
    YFINANCE = "yfinance"           # Yahoo Finance (primary)
    ALPHA_VANTAGE = "alpha_vantage" # Alpha Vantage (backup)
    POLYGON = "polygon"             # Polygon.io (real-time)
    QUANDL = "quandl"              # Quandl (historical)
```

#### **NotificationChannel**
```python
class NotificationChannel(Enum):
    EMAIL = "email"        # Email notifications
    SLACK = "slack"        # Slack messaging
    DISCORD = "discord"    # Discord webhooks
    SMS = "sms"           # SMS via Twilio
    WEBHOOK = "webhook"    # Custom webhooks
```

---

## 🛠️ **Default Configuration**

### **Complete Default Configuration**
```python
DEFAULT_CONFIG = {
    "watchlist": ["AAPL", "GOOGL", "MSFT", "NVDA"],
    
    "timeframes": {
        "interval": "5m",
        "days_of_history": 30,
        "backtest_days": 365
    },
    
    "indicators": {
        "rsi_period": 14,
        "rsi_overbought": 70,
        "rsi_oversold": 30,
        "macd": {
            "fast_period": 12,
            "slow_period": 26,
            "signal_period": 9
        },
        "bollinger_bands": {
            "period": 20,
            "std_dev": 2
        },
        "stochastic": {
            "k_period": 14,
            "d_period": 3
        },
        "atr_period": 14
    },
    
    "risk_management": {
        "max_position_size": 0.02,          # 2% per position
        "stop_loss_percentage": 0.05,       # 5% stop loss
        "take_profit_percentage": 0.10,     # 10% take profit
        "max_daily_loss": 0.03,             # 3% daily limit
        "max_portfolio_risk": 0.15,         # 15% portfolio risk
        "position_sizing_method": "kelly_criterion"
    },
    
    "trading": {
        "paper_trading": True,
        "real_time_enabled": True,
        "auto_trading": False,
        "max_concurrent_positions": 5,
        "min_volume_threshold": 1000000
    },
    
    "market_hours": {
        "start": "09:30",
        "end": "16:00",
        "pre_market_start": "06:30",
        "after_hours_end": "20:00",
        "timezone": "America/New_York"
    },
    
    "data_sources": {
        "primary": "yfinance",
        "backup": "alpha_vantage",
        "real_time": "polygon",
        "cache_duration": 300  # 5 minutes
    },
    
    "logging": {
        "level": "INFO",
        "file": "logs/trading.log",
        "max_size": "10MB",
        "backup_count": 5
    },
    
    "notifications": {
        "email_enabled": False,
        "slack_enabled": False,
        "discord_enabled": False,
        "sms_enabled": True,
        "webhook_url": ""
    },
    
    "performance": {
        "benchmark": "SPY",
        "risk_free_rate": 0.02,
        "min_sharpe_ratio": 1.0,
        "max_drawdown": 0.20
    },
    
    "ai_analysis": {
        "enabled": True,
        "model_type": "tiered",
        "confidence_threshold": 0.6,
        "update_frequency": "1h"
    },
    
    "database": {
        "type": "sqlite",
        "path": "data/trading.db",
        "backup_frequency": "daily"
    }
}
```

---

## 📏 **System Limits**

### **Performance and Safety Limits**
```python
# Position and Portfolio Limits
MAX_CONCURRENT_POSITIONS = 20      # Maximum open positions
MAX_WATCHLIST_SIZE = 100           # Maximum symbols in watchlist
MAX_POSITION_SIZE = 0.1            # 10% maximum position size

# Data and Processing Limits  
MAX_BACKTEST_DAYS = 3650           # 10 years maximum backtest
MIN_DATA_POINTS = 30               # Minimum data points for analysis
MAX_API_RETRIES = 3                # Maximum API retry attempts
CACHE_DURATION = 300               # 5 minutes cache duration

# Performance Limits
MAX_ANALYSIS_SYMBOLS = 200         # Maximum symbols per analysis batch
MAX_CONCURRENT_REQUESTS = 20       # Maximum parallel API requests
MAX_MEMORY_CACHE_SIZE = 1000       # Maximum cached analysis results
```

### **File and Directory Paths**
```python
# Standard Directory Structure
LOG_DIR = "logs"                   # Application logs
DATA_DIR = "data"                  # Cached data and databases  
CACHE_DIR = "data/cache"           # Market data cache
BACKTEST_DIR = "backtests"         # Backtest results
REPORTS_DIR = "reports"            # Generated reports
CONFIG_DIR = "config"              # Configuration files
```

---

## 🔄 **API Rate Limits**

### **External API Constraints**
```python
RATE_LIMITS = {
    "yfinance": {
        "requests_per_minute": 100,
        "requests_per_hour": 1000,
        "daily_limit": None,
        "cost_per_request": 0.0        # Free
    },
    
    "alpha_vantage": {
        "requests_per_minute": 5,
        "requests_per_hour": 500,
        "daily_limit": 25,             # Free tier
        "cost_per_request": 0.0
    },
    
    "polygon": {
        "requests_per_minute": 5,      # Free tier
        "requests_per_hour": 1000,     # Paid tier
        "daily_limit": None,
        "cost_per_request": 0.002      # $0.002 per request
    },
    
    # Tier 1 AI Providers (Free)
    "google_ai": {
        "requests_per_minute": 15,     # Gemini Flash
        "requests_per_day": 1000000,   # Very generous
        "cost_per_request": 0.0
    },
    
    "groq": {
        "requests_per_minute": 30,
        "requests_per_day": 14400,
        "cost_per_request": 0.0
    },
    
    "huggingface": {
        "requests_per_hour": 1000,
        "daily_limit": None,
        "cost_per_request": 0.0
    },
    
    "cerebras": {
        "requests_per_minute": 60,     # Generous free tier
        "cost_per_request": 0.0
    },
    
    "openrouter": {
        "requests_per_day": 200,       # Free models only
        "cost_per_request": 0.0
    },
    
    # Tier 2 AI Providers (Paid)
    "openai": {
        "requests_per_minute": 500,    # GPT-4o mini
        "tokens_per_minute": 10000000,
        "cost_per_1k_tokens": 0.0006   # $0.15 per 1M input tokens
    },
    
    "anthropic": {
        "requests_per_minute": 1000,
        "tokens_per_minute": 1000000,
        "cost_per_1k_tokens": 0.003    # $3.00 per 1M input tokens
    },
    
    # Communication APIs
    "twilio": {
        "requests_per_second": 1,
        "cost_per_sms": 0.0075         # $0.0075 per SMS
    }
}
```

---

## 💬 **Message Templates**

### **Error Messages**
```python
ERROR_MESSAGES = {
    "DATA_RETRIEVAL_FAILED": "Failed to retrieve market data for {symbol}",
    "INVALID_SYMBOL": "Invalid stock symbol provided: {symbol}",
    "NO_DATA_AVAILABLE": "No data available for {symbol} in the specified period",
    "CONFIG_INVALID": "Invalid configuration: {error}",
    "INSUFFICIENT_FUNDS": "Insufficient funds for trade: {symbol} ({amount})",
    "POSITION_LIMIT_EXCEEDED": "Position limit exceeded: max {max_positions}",
    "RISK_LIMIT_EXCEEDED": "Risk limit exceeded: {risk_percent}% > {max_risk}%",
    "SIGNAL_GENERATION_FAILED": "Failed to generate trading signal for {symbol}",
    "NETWORK_TIMEOUT": "Network timeout occurred while fetching {url}",
    "API_RATE_LIMIT": "API rate limit exceeded for {provider}: {retry_after}s",
    "AI_PROVIDER_UNAVAILABLE": "AI provider {provider} is unavailable: {reason}",
    "INVALID_API_KEY": "Invalid API key for {provider}: please check configuration"
}
```

### **Success Messages**
```python
SUCCESS_MESSAGES = {
    "DATA_RETRIEVED": "Market data retrieved successfully for {symbol}",
    "SIGNAL_GENERATED": "Trading signal generated: {signal} for {symbol} ({confidence}%)",
    "TRADE_EXECUTED": "Trade executed: {action} {shares} shares of {symbol} at ${price}",
    "BACKTEST_COMPLETED": "Backtest completed: {duration} with {trades} trades",
    "CONFIG_LOADED": "Configuration loaded successfully from {path}",
    "AI_ANALYSIS_COMPLETED": "AI analysis completed using {provider}: {recommendation}",
    "ALERT_SENT": "Alert sent via {channel}: {message}",
    "CACHE_HIT": "Cache hit for {symbol}: data age {age}s"
}
```

---

## 📊 **Trading Constants**

### **Signal Confidence Ranges**
```python
SIGNAL_CONFIDENCE_RANGES = {
    "STRONG_BUY": (0.8, 1.0),     # 80-100% confidence
    "BUY": (0.6, 0.8),            # 60-80% confidence  
    "HOLD": (0.4, 0.6),           # 40-60% confidence
    "SELL": (0.2, 0.4),           # 20-40% confidence
    "STRONG_SELL": (0.0, 0.2)     # 0-20% confidence
}
```

### **AI Tier Thresholds**
```python
AI_TIER_THRESHOLDS = {
    "TIER_1_ACTIVATION": 0.5,     # 50% technical confidence triggers free AI
    "TIER_2_ACTIVATION": 0.75,    # 75% enhanced confidence triggers paid AI
    "ALERT_THRESHOLD": 0.8,       # 80% confidence required for SMS alerts
    "VERY_STRONG_THRESHOLD": 0.9  # 90% confidence for high-priority alerts
}
```

### **Risk Management Constants**
```python
RISK_CONSTANTS = {
    "DEFAULT_STOP_LOSS": 0.05,       # 5% default stop loss
    "DEFAULT_TAKE_PROFIT": 0.10,     # 10% default take profit
    "MAX_PORTFOLIO_RISK": 0.15,      # 15% maximum portfolio risk
    "MAX_POSITION_SIZE": 0.02,       # 2% maximum position size
    "KELLY_MULTIPLIER": 0.25,        # 25% of Kelly criterion
    "MIN_RISK_REWARD_RATIO": 2.0     # 2:1 minimum risk/reward
}
```

---

## 🔧 **Usage Examples**

### **Using Enums**
```python
from src.constants.settings import SignalType, TradingMode, DataSource

# Type-safe signal handling
def process_signal(signal_type: SignalType):
    if signal_type == SignalType.STRONG_BUY:
        execute_buy_order()
    elif signal_type == SignalType.STRONG_SELL:
        execute_sell_order()

# Configuration with enums
config = {
    "trading_mode": TradingMode.PAPER,
    "primary_data_source": DataSource.YFINANCE,
    "backup_data_source": DataSource.ALPHA_VANTAGE
}
```

### **Using Constants**
```python
from src.constants.settings import MAX_CONCURRENT_POSITIONS, RATE_LIMITS

# Validate position limits
if current_positions >= MAX_CONCURRENT_POSITIONS:
    raise PositionLimitError(f"Maximum {MAX_CONCURRENT_POSITIONS} positions allowed")

# Check rate limits
provider_limits = RATE_LIMITS["openai"]
if requests_per_minute > provider_limits["requests_per_minute"]:
    sleep(60)  # Wait for rate limit reset
```

### **Using Default Configuration**
```python
from src.constants.settings import DEFAULT_CONFIG

def get_default_watchlist():
    return DEFAULT_CONFIG["watchlist"]

def get_default_risk_settings():
    return DEFAULT_CONFIG["risk_management"]
```

### **Using Message Templates**
```python
from src.constants.settings import ERROR_MESSAGES, SUCCESS_MESSAGES

# Error handling with templated messages
try:
    data = fetch_market_data(symbol)
except Exception as e:
    error_msg = ERROR_MESSAGES["DATA_RETRIEVAL_FAILED"].format(symbol=symbol)
    logger.error(error_msg)

# Success messages
success_msg = SUCCESS_MESSAGES["SIGNAL_GENERATED"].format(
    signal="STRONG_BUY", 
    symbol="AAPL", 
    confidence=85
)
logger.info(success_msg)
```

---

## 🧪 **Testing with Constants**

### **Test Configuration**
```python
def test_signal_types():
    # Ensure all signal types are valid
    assert SignalType.STRONG_BUY.value == "STRONG_BUY"
    assert len(SignalType) == 5

def test_rate_limits():
    # Validate rate limit structure
    for provider, limits in RATE_LIMITS.items():
        assert "requests_per_minute" in limits
        assert limits["requests_per_minute"] > 0
```

### **Mock Constants for Testing**
```python
# Test with modified constants
TEST_RATE_LIMITS = {
    "test_provider": {
        "requests_per_minute": 1000,  # Higher limits for testing
        "cost_per_request": 0.0
    }
}
```

---

## 📚 **Best Practices**

### **Enum Usage**
1. **Use enums for fixed sets** of values (signal types, trading modes)
2. **Prefer enums over strings** for type safety
3. **Document enum values** with comments
4. **Keep enum names descriptive** and consistent

### **Constants Management**
1. **Group related constants** in logical sections
2. **Use descriptive names** that explain purpose
3. **Document units** for numeric constants (seconds, percentages)
4. **Avoid magic numbers** in code - use named constants

### **Configuration Defaults**
1. **Provide sensible defaults** for all configuration options
2. **Use conservative values** for risk and rate limits
3. **Document default rationale** for complex settings
4. **Test defaults** in various scenarios

---

**📊 Centralized constants ensuring consistency across the trading platform** ⚙️