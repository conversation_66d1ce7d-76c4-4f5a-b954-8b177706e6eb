{"watchlist": ["AAPL", "ABNB", "AMBA", "AMD", "AMZN", "ASAN", "AVAV", "BABA", "BBAI", "BKSY", "CAT", "COIN", "COUR", "CRM", "CROX", "CRSR", "DASH", "DIS", "DOMO", "DRSHF", "EBAY", "ETSY", "GOOG", "GOOGL", "HIMS", "INTC", "INTU", "KTOS", "LCID", "LMT", "LPSN", "LULU", "LYFT", "MDB", "META", "MSFT", "MU", "NFLX", "NKE", "NOW", "NVDA", "ORCL", "PINS", "PL", "PLTR", "PYPL", "QBTS", "QQQ", "RBLX", "RDW", "RDDT", "REKR", "RIVN", "RKLB", "RXRX", "SEI", "SHOP", "SOUN", "TGT", "TSLA", "TWLO", "U", "UBER", "UPST", "VERI", "XYZ"], "timeframes": {"interval": "5m", "days_of_history": 30, "backtest_days": 365}, "indicators": {"rsi_period": 14, "rsi_overbought": 70, "rsi_oversold": 30, "macd": {"fast_period": 12, "slow_period": 26, "signal_period": 9}, "bollinger_bands": {"period": 20, "std_dev": 2}, "stochastic": {"k_period": 14, "d_period": 3}, "atr_period": 14}, "risk_management": {"max_position_size": 0.02, "stop_loss_percentage": 0.05, "take_profit_percentage": 0.1, "max_daily_loss": 0.03, "max_portfolio_risk": 0.15, "position_sizing_method": "kelly_criterion"}, "trading": {"paper_trading": true, "real_time_enabled": true, "auto_trading": false, "max_concurrent_positions": 5, "min_volume_threshold": 1000000}, "market_hours": {"start": "09:30", "end": "16:00", "pre_market_start": "04:00", "after_hours_end": "20:00", "timezone": "America/New_York"}, "data_sources": {"primary": "yfinance", "backup": "alpha_vantage", "real_time": "polygon", "cache_duration": 300}, "api_keys": {"alpha_vantage": "${ALPHA_VANTAGE_API_KEY}", "polygon": "${POLYGON_API_KEY}", "quandl": "${QUANDL_API_KEY}", "openai": "${OPENAI_API_KEY}", "anthropic": "${ANTHROPIC_API_KEY}", "google_ai": "${GOOGLE_AI_API_KEY}", "cohere": "${COHERE_API_KEY}", "huggingface": "${HUGGINGFACE_API_KEY}", "twilio_account_sid": "${TWILIO_ACCOUNT_SID}", "twilio_auth_token": "${TWILIO_AUTH_TOKEN}", "groq": "${GROQ_API_KEY}", "openrouter": "${OPEN_ROUTER_API_KEY}", "cerebras": "${CEREBRAS_API_KEY}", "news_api": "${NEWS_API_KEY}"}, "logging": {"level": "INFO", "file": "logs/trading.log", "max_size": "10MB", "backup_count": 5}, "notifications": {"email_enabled": false, "slack_enabled": false, "discord_enabled": false, "sms_enabled": true, "email": {"enabled": false, "smtp_server": "smtp.gmail.com", "smtp_port": 587, "username": "${EMAIL_USERNAME}", "password": "${EMAIL_PASSWORD}", "from_email": "${EMAIL_FROM}", "to_email": "${EMAIL_TO}"}, "slack": {"enabled": false, "webhook_url": "${SLACK_WEBHOOK_URL}"}, "discord": {"enabled": false, "webhook_url": "${DISCORD_WEBHOOK_URL}"}, "sms": {"enabled": true, "twilio_account_sid": "${TWILIO_ACCOUNT_SID}", "twilio_auth_token": "${TWILIO_AUTH_TOKEN}", "from_number": "${TWILIO_FROM_NUMBER}", "to_number": "${TWILIO_TO_NUMBER}"}}, "performance": {"benchmark": "SPY", "risk_free_rate": 0.02, "min_sharpe_ratio": 1.0, "max_drawdown": 0.2}, "ai_analysis": {"enabled": true, "model_type": "tiered", "confidence_threshold": 0.6, "update_frequency": "1h", "tier_1_threshold": 0.5, "tier_2_threshold": 0.75, "stop_at_tier_1": true, "tier_1_providers": {"google_ai": {"enabled": true, "model": "gemini-1.5-flash", "max_tokens": 500, "temperature": 0.3, "cost_tier": "free", "max_retries": 3, "retry_delay": 1.0, "max_retry_delay": 10.0, "rate_limit_delay": 0.5}, "groq": {"enabled": true, "model": "llama3-8b-8192", "max_tokens": 500, "temperature": 0.3, "cost_tier": "free", "max_retries": 3, "retry_delay": 1.0, "max_retry_delay": 10.0, "rate_limit_delay": 0.5}, "huggingface": {"enabled": false, "model": "openai-community/gpt2", "max_tokens": 500, "temperature": 0.3, "cost_tier": "free", "max_retries": 3, "retry_delay": 1.0, "max_retry_delay": 10.0, "rate_limit_delay": 0.5}, "cerebras": {"enabled": false, "model": "llama3.1-8b-instruct", "max_tokens": 500, "temperature": 0.3, "cost_tier": "free", "max_retries": 3, "retry_delay": 1.0, "max_retry_delay": 10.0, "rate_limit_delay": 0.5}, "openrouter": {"enabled": true, "model": "anthropic/claude-3-haiku", "max_tokens": 500, "temperature": 0.3, "cost_tier": "free", "max_retries": 3, "retry_delay": 1.0, "max_retry_delay": 10.0, "rate_limit_delay": 0.5}}, "tier_2_providers": {"openai": {"enabled": true, "model": "gpt-4o-mini", "max_tokens": 1000, "temperature": 0.3, "cost_tier": "paid"}, "anthropic": {"enabled": true, "model": "claude-3-sonnet-20240229", "max_tokens": 1000, "temperature": 0.3, "cost_tier": "paid"}}, "analysis_types": {"sentiment": true, "pattern_recognition": true, "price_prediction": true, "risk_assessment": true, "portfolio_optimization": true}}, "sentiment_data": {"enabled": true, "cache_duration": 300, "reddit": {"enabled": true, "client_id": "${REDDIT_CLIENT_ID}", "client_secret": "${REDDIT_CLIENT_SECRET}", "username": "${REDDIT_USERNAME}", "password": "${REDDIT_PASSWORD}", "user_agent": "AI-Nvestor/1.0 (Sentiment Analysis)", "subreddits": ["stocks", "investing", "wallstreetbets", "stockmarket", "options", "daytrading", "investments", "financialindependence"], "max_posts_per_subreddit": 50, "max_comments_per_post": 20, "min_score_threshold": 5, "rate_limit_per_minute": 60}, "apify": {"enabled": true, "api_token": "${APIFY_API_TOKEN}", "reddit_scraper_id": "${APIFY_REDDIT_SCRAPER_ID}", "max_posts": 1000, "max_comments": 100, "cache_duration": 86400}, "news": {"enabled": false, "sources": ["news_api", "alpha_vantage"], "max_articles_per_source": 50}, "social_media": {"enabled": false, "sources": ["twitter"], "max_posts_per_source": 100}}, "adaptive_learning": {"enabled": true, "learning_rate": 0.01, "min_data_points": 50, "retrain_frequency": 24, "models": {"sentiment_model": "RandomForestRegressor", "technical_model": "GradientBoostingRegressor", "hybrid_model": "Ridge"}, "weight_categories": {"sentiment": {"reddit_sentiment": 0.3, "news_sentiment": 0.2, "social_sentiment": 0.1, "sentiment_volume": 0.15, "sentiment_confidence": 0.25}, "technical": {"trend_indicators": 0.4, "momentum_indicators": 0.3, "volatility_indicators": 0.2, "volume_indicators": 0.1}, "market_condition": {"market_volatility": 0.2, "market_trend": 0.3, "market_sentiment": 0.25, "market_volume": 0.25}}, "feature_selection": {"max_features": 50, "importance_threshold": 0.01}, "performance_tracking": {"history_length": 100, "evaluation_metrics": ["mse", "r2", "mae"]}}, "database": {"type": "sqlite", "path": "data/trading.db", "backup_frequency": "daily"}}