"""
Signal generation service for AI-Nvestor.

This module provides:
- Multi-indicator signal generation
- Signal validation and filtering
- Confidence scoring
- Market condition analysis
- Signal persistence and tracking
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Union
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import json
from pathlib import Path

from services.indicators import TechnicalIndicators, SignalType, IndicatorResult
from services.market_data import MarketDataService
from core.exceptions import SignalError, get_error_code
from utils.logging import get_logger


class SignalStrength(Enum):
    """Signal strength levels."""
    WEAK = 1
    MODERATE = 2
    STRONG = 3
    VERY_STRONG = 4


class MarketCondition(Enum):
    """Market condition types."""
    BULLISH = "BULLISH"
    BEARISH = "BEARISH"
    SIDEWAYS = "SIDEWAYS"
    VOLATILE = "VOLATILE"
    TRENDING = "TRENDING"


@dataclass
class Signal:
    """Trading signal with metadata."""
    symbol: str
    signal_type: SignalType
    strength: SignalStrength
    confidence: Optional[float]  # Can be None for undefined confidence
    price: Optional[float]  # Can be None for undefined price
    timestamp: datetime
    indicators: Dict[str, float]
    market_condition: MarketCondition
    volume_support: bool
    trend_alignment: bool
    metadata: Dict[str, Union[str, float, bool]]


class SignalGenerator:
    """
    Advanced signal generation system.
    
    Features:
    - Multi-indicator consensus
    - Market condition analysis
    - Volume confirmation
    - Trend alignment
    - Signal persistence
    """
    
    def __init__(self, config: Dict):
        """
        Initialize the signal generator.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.logger = get_logger()
        self.indicators = TechnicalIndicators(config)
        self.market_data = MarketDataService(config)
        
        # Signal tracking
        self.signal_history = []
        self.signal_file = Path("data/signals.json")
        self.signal_file.parent.mkdir(parents=True, exist_ok=True)
        
        # Load existing signals
        self._load_signal_history()
    
    def generate_signal(self, symbol: str, data: pd.DataFrame) -> Signal:
        """
        Generate a comprehensive trading signal with improved logic.
        
        Args:
            symbol: Stock symbol
            data: OHLCV data
            
        Returns:
            Trading signal with metadata
        """
        try:
            # Validate input data
            if not self._validate_signal_input(symbol, data):
                error_msg = f"Insufficient data for {symbol} (need 30+ points, got {len(data)})"
                self.logger.log_error(Exception(error_msg), {'symbol': symbol, 'operation': 'validate_input'})
                raise SignalError(error_msg)
            
            # Calculate all indicators
            indicator_results = self.indicators.calculate_all_indicators(data)
            
            # Check if indicators calculation failed
            if not indicator_results:
                error_msg = f"Technical indicators failed for {symbol} (need 30+ data points)"
                self.logger.log_error(Exception(error_msg), {'symbol': symbol, 'operation': 'calculate_indicators', 'data_length': len(data)})
                raise SignalError(error_msg)
            
            # Generate consensus signal
            consensus_signal, confidence = self.indicators.generate_consensus_signal(indicator_results)
            
            # Validate consensus signal
            if consensus_signal is None:
                error_msg = f"Consensus signal calculation failed for {symbol}"
                self.logger.log_error(Exception(error_msg), {'symbol': symbol, 'operation': 'consensus_signal', 'indicator_count': len(indicator_results)})
                raise SignalError(error_msg)
            
            # Analyze market conditions
            market_condition = self._analyze_market_condition(data, indicator_results)
            
            # Check volume support
            volume_support = self._check_volume_support(data, consensus_signal)
            
            # Check trend alignment
            trend_alignment = self._check_trend_alignment(data, consensus_signal)
            
            # Check multi-timeframe alignment for STRONG signals (ChatGPT's suggestion)
            multi_timeframe_alignment = self._check_multi_timeframe_alignment(data, consensus_signal)
            
            # Apply ChatGPT's grading system for STRONG signals
            if consensus_signal in [SignalType.STRONG_BUY, SignalType.STRONG_SELL]:
                # Require multi-timeframe alignment for STRONG signals
                if not multi_timeframe_alignment:
                    # Downgrade STRONG signals to regular signals
                    if consensus_signal == SignalType.STRONG_BUY:
                        consensus_signal = SignalType.BUY
                    elif consensus_signal == SignalType.STRONG_SELL:
                        consensus_signal = SignalType.SELL
                    self.logger.info(f"⚠️ Downgraded {symbol} signal from {consensus_signal} due to multi-timeframe misalignment")
                
                # Additional checks for STRONG_SELL (ChatGPT's suggestion)
                if consensus_signal == SignalType.STRONG_SELL:
                    # Check if trend is actually down
                    if trend_alignment:  # If trend is up, downgrade to SELL
                        consensus_signal = SignalType.SELL
                        self.logger.info(f"⚠️ Downgraded {symbol} from STRONG_SELL to SELL due to bullish trend")
                    
                    # Check if we have sufficient momentum sell signals
                    momentum_sell_count = 0
                    for name, result in indicator_results.items():
                        if name in ['rsi', 'stochastic', 'williams_r', 'cci'] and result.signal in [SignalType.SELL, SignalType.STRONG_SELL]:
                            momentum_sell_count += 1
                    
                    if momentum_sell_count < 3:
                        consensus_signal = SignalType.SELL
                        self.logger.info(f"⚠️ Downgraded {symbol} from STRONG_SELL to SELL due to insufficient momentum signals ({momentum_sell_count})")
            
            # Determine signal strength
            strength = self._calculate_signal_strength(
                consensus_signal, confidence, volume_support, trend_alignment
            )
            
            # Get current price with validation
            current_price = self._get_current_price(data)
            
            # Create signal
            signal = Signal(
                symbol=symbol,
                signal_type=consensus_signal,
                strength=strength,
                confidence=confidence,
                price=current_price,
                timestamp=datetime.now(),
                indicators={name: result.value for name, result in indicator_results.items()},
                market_condition=market_condition,
                volume_support=volume_support,
                trend_alignment=trend_alignment,
                metadata=self._generate_metadata(data, indicator_results)
            )
            
            # Store signal
            self._store_signal(signal)
            
            return signal
            
        except SignalError as e:
            # Re-raise signal errors as they are already logged
            raise e
        except Exception as e:
            error_msg = f"Unexpected error during signal generation for {symbol}: {str(e)}"
            self.logger.log_error(e, {'symbol': symbol, 'operation': 'generate_signal'})
            raise SignalError(error_msg)
    
    def generate_signals_for_watchlist(self, symbols: List[str]) -> Dict[str, Signal]:
        """
        Generate signals for multiple symbols.
        
        Args:
            symbols: List of stock symbols
            
        Returns:
            Dictionary mapping symbols to signals
        """
        signals = {}
        
        for symbol in symbols:
            try:
                # Get market data
                data = self.market_data.get_stock_data(
                    symbol, 
                    interval=self.config.get('timeframes', {}).get('interval', '5m'),
                    days=self.config.get('timeframes', {}).get('days_of_history', 30)
                )
                
                if data is not None and not data.empty:
                    signal = self.generate_signal(symbol, data)
                    signals[symbol] = signal
                else:
                    self.logger.log_error(
                        Exception(f"No market data available for {symbol}"),
                        {'symbol': symbol, 'operation': 'get_market_data'}
                    )
                    # Don't add failed symbols to results - let caller handle missing data
                    
            except SignalError as e:
                # Signal generation failed - already logged in generate_signal
                pass
            except Exception as e:
                # Unexpected error
                self.logger.log_error(e, {'symbol': symbol, 'operation': 'watchlist_generation'})
        
        return signals
    
    def _validate_signal_input(self, symbol: str, data: pd.DataFrame) -> bool:
        """Validate input data for signal generation."""
        try:
            if not symbol or not isinstance(symbol, str):
                return False
            
            if data is None or data.empty:
                return False
            
            # Check for required columns
            required_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
            if not all(col in data.columns for col in required_columns):
                return False
            
            # Check for sufficient data points
            if len(data) < 50:  # Need enough data for indicators
                return False
            
            # Check for reasonable values
            if (data['High'] < data['Low']).any():
                return False
            
            if (data['Open'] < 0).any() or (data['Close'] < 0).any():
                return False
            
            # Check for NaN values
            if data.isnull().any().any():
                return False
            
            return True
            
        except Exception as e:
            self.logger.warning(f"Error validating volume support for {symbol}: {e}")
            return False
    
    def _get_current_price(self, data: pd.DataFrame) -> float:
        """Get current price with validation."""
        try:
            if data is None or data.empty or 'Close' not in data.columns:
                return 0.0
            
            current_price = data['Close'].iloc[-1]
            
            if pd.isna(current_price) or np.isinf(current_price) or current_price <= 0:
                return 0.0
            
            return float(current_price)
            
        except Exception as e:
            self.logger.warning(f"Error getting current price from data: {e}")
            return None  # N/A instead of fake 0.0
    
    def _get_default_signal(self, symbol: str, error_message: str) -> Signal:
        """Get default signal when generation fails."""
        return Signal(
            symbol=symbol,
            signal_type=SignalType.HOLD,
            strength=SignalStrength.WEAK,
            confidence=None,  # N/A instead of fake 0.5
            price=None,  # N/A instead of fake 0.0
            timestamp=datetime.now(),
            indicators={},
            market_condition=MarketCondition.SIDEWAYS,
            volume_support=False,
            trend_alignment=False,
            metadata={'error': error_message, 'fallback': True}
        )
    
    def get_signal_summary(self, symbols: List[str]) -> Dict[str, Dict]:
        """
        Get a summary of signals for multiple symbols.
        
        Args:
            symbols: List of stock symbols
            
        Returns:
            Signal summary dictionary
        """
        signals = self.generate_signals_for_watchlist(symbols)
        
        summary = {
            'timestamp': datetime.now().isoformat(),
            'total_symbols': len(symbols),
            'signals_generated': len([s for s in signals.values() if s is not None]),
            'buy_signals': [],
            'sell_signals': [],
            'strong_signals': [],
            'market_conditions': {}
        }
        
        for symbol, signal in signals.items():
            if signal is None:
                continue
            
            signal_info = {
                'symbol': symbol,
                'signal': signal.signal_type.value,
                'strength': signal.strength.value,
                'confidence': signal.confidence,
                'price': signal.price,
                'market_condition': signal.market_condition.value,
                'volume_support': signal.volume_support,
                'trend_alignment': signal.trend_alignment
            }
            
            if signal.signal_type in [SignalType.BUY, SignalType.STRONG_BUY]:
                summary['buy_signals'].append(signal_info)
            elif signal.signal_type in [SignalType.SELL, SignalType.STRONG_SELL]:
                summary['sell_signals'].append(signal_info)
            
            if signal.strength in [SignalStrength.STRONG, SignalStrength.VERY_STRONG]:
                summary['strong_signals'].append(signal_info)
            
            # Track market conditions
            condition = signal.market_condition.value
            if condition not in summary['market_conditions']:
                summary['market_conditions'][condition] = 0
            summary['market_conditions'][condition] += 1
        
        return summary
    
    def _analyze_market_condition(self, data: pd.DataFrame, indicators: Dict[str, IndicatorResult]) -> MarketCondition:
        """Analyze market condition with improved logic for mixed signals."""
        try:
            if data is None or data.empty or len(data) < 20:
                return MarketCondition.SIDEWAYS
            
            # Get key indicators
            rsi = indicators.get('rsi', None)
            macd = indicators.get('macd', None)
            sma = indicators.get('sma', None)
            ema = indicators.get('ema', None)
            bollinger = indicators.get('bollinger_bands', None)
            
            # Calculate price movement
            current_price = data['Close'].iloc[-1]
            price_20_ago = data['Close'].iloc[-20] if len(data) >= 20 else current_price
            price_change_20d = ((current_price - price_20_ago) / price_20_ago) * 100
            
            # Calculate volatility
            returns = data['Close'].pct_change().dropna()
            volatility = returns.std() * np.sqrt(252) * 100  # Annualized volatility
            
            # Trend analysis
            trend_bullish = False
            trend_bearish = False
            
            if sma and ema:
                sma_value = sma.value if hasattr(sma, 'value') else None
                ema_value = ema.value if hasattr(ema, 'value') else None
                
                if sma_value and ema_value and not pd.isna(sma_value) and not pd.isna(ema_value):
                    trend_bullish = current_price > sma_value and current_price > ema_value
                    trend_bearish = current_price < sma_value and current_price < ema_value
            
            # Momentum analysis
            momentum_bullish = False
            momentum_bearish = False
            
            if rsi:
                rsi_value = rsi.value if hasattr(rsi, 'value') else None
                if rsi_value and not pd.isna(rsi_value):
                    momentum_bullish = rsi_value > 50
                    momentum_bearish = rsi_value < 50
            
            # Volatility analysis
            high_volatility = volatility > 30  # High volatility threshold
            low_volatility = volatility < 15   # Low volatility threshold
            
            # Market condition determination with improved logic
            if trend_bullish and momentum_bullish and price_change_20d > 5:
                return MarketCondition.BULLISH
            elif trend_bearish and momentum_bearish and price_change_20d < -5:
                return MarketCondition.BEARISH
            elif high_volatility and abs(price_change_20d) > 10:
                return MarketCondition.VOLATILE
            elif low_volatility and abs(price_change_20d) < 3:
                return MarketCondition.SIDEWAYS
            elif trend_bullish or momentum_bullish:
                return MarketCondition.BULLISH
            elif trend_bearish or momentum_bearish:
                return MarketCondition.BEARISH
            else:
                return MarketCondition.SIDEWAYS
                
        except Exception as e:
            self.logger.log_error(e, {'operation': 'analyze_market_condition'})
            return MarketCondition.SIDEWAYS
    
    def _check_volume_support(self, data: pd.DataFrame, signal: SignalType) -> bool:
        """Check volume support with improved normalization (ChatGPT's suggestion)."""
        try:
            if data is None or data.empty or 'Volume' not in data.columns:
                return False
            
            current_volume = data['Volume'].iloc[-1]
            
            # Calculate average volume with proper normalization
            if len(data) >= 20:
                # Use 20-day average for better normalization
                avg_volume = data['Volume'].rolling(window=20).mean().iloc[-1]
            else:
                avg_volume = data['Volume'].mean()
            
            # Handle zero or NaN values
            if pd.isna(current_volume) or pd.isna(avg_volume) or avg_volume == 0:
                return False
            
            # Calculate volume ratio
            volume_ratio = current_volume / avg_volume
            
            # ChatGPT's suggestion: normalize for time-of-day
            # For now, use a more conservative approach
            if volume_ratio > 0.8:  # At least 80% of average volume
                return True
            elif volume_ratio > 0.5:  # Moderate volume support
                return True
            else:
                return False
                
        except Exception as e:
            self.logger.log_error(e, {'operation': 'check_volume_support'})
            return False
    
    def _check_trend_alignment(self, data: pd.DataFrame, signal: SignalType) -> bool:
        """Check if signal aligns with current trend with improved logic."""
        try:
            if data is None or data.empty or 'Close' not in data.columns:
                return False
            
            # Check for sufficient data
            if len(data) < 50:  # Need minimum data for moving averages
                return False
            
            # Calculate moving averages with validation
            close_series = data['Close'].dropna()
            if len(close_series) < 20:
                return False
            
            sma_20 = close_series.rolling(window=20).mean()
            sma_50 = close_series.rolling(window=50).mean()
            
            # Validate moving averages
            if sma_20.isnull().all() or sma_50.isnull().all():
                return False
            
            current_price = data['Close'].iloc[-1]
            sma_20_current = sma_20.iloc[-1]
            sma_50_current = sma_50.iloc[-1]
            
            # Handle NaN values
            if pd.isna(current_price) or pd.isna(sma_20_current) or pd.isna(sma_50_current):
                return False
            
            # Calculate trend strength
            price_above_sma20 = current_price > sma_20_current
            price_above_sma50 = current_price > sma_50_current
            
            # Calculate trend strength as percentage
            trend_strength_20 = ((current_price - sma_20_current) / sma_20_current) * 100
            trend_strength_50 = ((current_price - sma_50_current) / sma_50_current) * 100
            
            # Determine trend strength
            strong_trend = abs(trend_strength_20) > 2 and abs(trend_strength_50) > 3
            moderate_trend = abs(trend_strength_20) > 1 or abs(trend_strength_50) > 1.5
            
            # Enhanced trend alignment logic
            if signal in [SignalType.BUY, SignalType.STRONG_BUY]:
                if price_above_sma20 and price_above_sma50:
                    return True
                elif moderate_trend and trend_strength_20 > 0:
                    return True
                else:
                    return False
            elif signal in [SignalType.SELL, SignalType.STRONG_SELL]:
                if not price_above_sma20 and not price_above_sma50:
                    return True
                elif moderate_trend and trend_strength_20 < 0:
                    return True
                else:
                    return False
            else:
                return True  # HOLD signals are neutral
                
        except Exception as e:
            self.logger.log_error(e, {'operation': 'check_trend_alignment'})
            return False
    
    def _check_multi_timeframe_alignment(self, data: pd.DataFrame, signal: SignalType) -> bool:
        """
        Check multi-timeframe alignment for STRONG signals (ChatGPT's suggestion).
        Requires daily and 60-min trends to agree before issuing STRONG labels.
        """
        try:
            if data is None or data.empty or len(data) < 50:
                return False
            
            # For now, we'll use the available data to simulate multi-timeframe analysis
            # In a full implementation, we'd fetch different timeframe data
            
            # Calculate short-term trend (5-day)
            if len(data) >= 5:
                short_term_trend = data['Close'].iloc[-1] > data['Close'].iloc[-5]
            else:
                short_term_trend = True
            
            # Calculate medium-term trend (20-day)
            if len(data) >= 20:
                medium_term_trend = data['Close'].iloc[-1] > data['Close'].rolling(window=20).mean().iloc[-1]
            else:
                medium_term_trend = True
            
            # Check alignment based on signal type
            if signal in [SignalType.STRONG_BUY, SignalType.BUY]:
                return short_term_trend and medium_term_trend
            elif signal in [SignalType.STRONG_SELL, SignalType.SELL]:
                return not short_term_trend and not medium_term_trend
            else:
                return True  # HOLD signals don't require alignment
                
        except Exception as e:
            self.logger.log_error(e, {'operation': 'check_multi_timeframe_alignment'})
            return False
    
    def _calculate_signal_strength(self, 
                                 signal: SignalType, 
                                 confidence: float, 
                                 volume_support: bool, 
                                 trend_alignment: bool) -> SignalStrength:
        """Calculate signal strength based on multiple factors with improved logic."""
        strength_score = 0
        
        # Base score from signal type
        if signal == SignalType.STRONG_BUY or signal == SignalType.STRONG_SELL:
            strength_score += 3
        elif signal == SignalType.BUY or signal == SignalType.SELL:
            strength_score += 2
        else:
            strength_score += 1
        
        # Confidence score (reduced weight for conflicting signals)
        if confidence > 0.8:
            strength_score += 2
        elif confidence > 0.6:
            strength_score += 1
        elif confidence > 0.4:
            strength_score += 0.5
        
        # Volume support (increased weight)
        if volume_support:
            strength_score += 1.5
        
        # Trend alignment (increased weight for trend-following signals)
        if trend_alignment:
            strength_score += 1.5
        else:
            # Reduce strength for signals that go against the trend
            strength_score -= 1
        
        # Multi-timeframe alignment check (ChatGPT's suggestion)
        # This would require access to the data, so we'll implement it in the main signal generation
        # For now, we'll assume it's checked elsewhere
        
        # Additional checks for STRONG signals (ChatGPT's grading system)
        if signal == SignalType.STRONG_SELL:
            # Require higher threshold for STRONG_SELL to avoid false signals
            if strength_score < 6:
                # Downgrade to SELL if not strong enough
                return SignalStrength.MODERATE
            # Additional requirement: trend must be down for STRONG_SELL
            if trend_alignment:  # If trend is up, downgrade
                return SignalStrength.MODERATE
        elif signal == SignalType.STRONG_BUY:
            # Require higher threshold for STRONG_BUY
            if strength_score < 6:
                # Downgrade to BUY if not strong enough
                return SignalStrength.MODERATE
            # Additional requirement: trend must be up for STRONG_BUY
            if not trend_alignment:  # If trend is down, downgrade
                return SignalStrength.MODERATE
        
        # Determine strength level with adjusted thresholds
        if strength_score >= 7:
            return SignalStrength.VERY_STRONG
        elif strength_score >= 5:
            return SignalStrength.STRONG
        elif strength_score >= 3:
            return SignalStrength.MODERATE
        else:
            return SignalStrength.WEAK
    
    def _generate_metadata(self, data: pd.DataFrame, indicators: Dict[str, IndicatorResult]) -> Dict[str, Union[str, float, bool]]:
        """Generate metadata for the signal."""
        try:
            metadata = {
                'data_points': len(data),
                'price_range': {
                    'high': data['High'].max(),
                    'low': data['Low'].min(),
                    'current': data['Close'].iloc[-1]
                },
                'volume_stats': {
                    'current': data['Volume'].iloc[-1],
                    'average': data['Volume'].rolling(window=20).mean().iloc[-1],
                    'max': data['Volume'].max()
                },
                'volatility': data['Close'].pct_change().std() * np.sqrt(252),
                'trend_direction': 'up' if data['Close'].iloc[-1] > data['Close'].iloc[-5] else 'down'
            }
            
            # Add key indicator values
            for name, indicator in indicators.items():
                if hasattr(indicator, 'value'):
                    metadata[f'{name}_value'] = indicator.value
                    metadata[f'{name}_signal'] = indicator.signal.value
                    metadata[f'{name}_confidence'] = indicator.confidence
            
            return metadata
            
        except Exception as e:
            self.logger.log_error(e, {'operation': 'generate_metadata'})
            return {}
    
    def _store_signal(self, signal: Signal):
        """Store signal in history."""
        try:
            # Convert signal to dictionary for JSON serialization
            signal_dict = {
                'symbol': signal.symbol,
                'signal_type': signal.signal_type.value,
                'strength': signal.strength.value,
                'confidence': float(signal.confidence),  # Ensure float
                'price': float(signal.price),  # Ensure float
                'timestamp': signal.timestamp.isoformat(),
                'indicators': {k: float(v) for k, v in signal.indicators.items()},  # Convert all to float
                'market_condition': signal.market_condition.value,
                'volume_support': str(signal.volume_support),  # Convert bool to string
                'trend_alignment': str(signal.trend_alignment),  # Convert bool to string
                'metadata': self._serialize_metadata(signal.metadata)
            }
            
            self.signal_history.append(signal_dict)
            
            # Keep only last 1000 signals
            if len(self.signal_history) > 1000:
                self.signal_history = self.signal_history[-1000:]
            
            # Validate data before saving
            self._validate_signal_data(signal_dict)
            
            # Save to file
            with open(self.signal_file, 'w') as f:
                json.dump(self.signal_history, f, indent=2)
                
        except Exception as e:
            self.logger.log_error(e, {'operation': 'store_signal'})
    
    def _serialize_metadata(self, metadata: Dict[str, Union[str, float, bool]]) -> Dict[str, Union[str, float]]:
        """Serialize metadata to be JSON compatible."""
        serialized = {}
        for k, v in metadata.items():
            if isinstance(v, bool):
                serialized[k] = str(v)
            elif hasattr(v, 'item'):  # NumPy types
                serialized[k] = float(v.item())
            elif isinstance(v, (int, float)):
                serialized[k] = float(v)
            else:
                serialized[k] = str(v)
        return serialized
    
    def _validate_signal_data(self, signal_dict: Dict):
        """Validate signal data before saving."""
        try:
            required_fields = ['symbol', 'signal_type', 'strength', 'confidence', 'price', 'timestamp', 'indicators']
            for field in required_fields:
                if field not in signal_dict:
                    raise ValueError(f"Missing required field: {field}")
                
            # Validate data types
            if not isinstance(signal_dict['symbol'], str):
                raise ValueError("Symbol must be a string")
            if not isinstance(signal_dict['confidence'], (int, float)):
                raise ValueError("Confidence must be a number")
            if not isinstance(signal_dict['price'], (int, float)):
                raise ValueError("Price must be a number")
            if not isinstance(signal_dict['indicators'], dict):
                raise ValueError("Indicators must be a dictionary")
                
            # Validate indicator values
            for key, value in signal_dict['indicators'].items():
                if not isinstance(value, (int, float)):
                    raise ValueError(f"Indicator value for {key} must be a number")
                    
        except Exception as e:
            self.logger.log_error(e, {'operation': 'validate_signal_data'})
            raise
    
    def _load_signal_history(self):
        """Load signal history from file."""
        try:
            if self.signal_file.exists():
                with open(self.signal_file, 'r') as f:
                    content = f.read().strip()
                    if content:  # Check if file is not empty
                        self.signal_history = json.loads(content)
                    else:
                        self.signal_history = []
            else:
                self.signal_history = []
        except (json.JSONDecodeError, FileNotFoundError) as e:
            self.logger.log_error(e, {'operation': 'load_signal_history'})
            # Backup corrupted file and start fresh
            if self.signal_file.exists():
                backup_file = self.signal_file.with_suffix('.json.backup')
                try:
                    self.signal_file.rename(backup_file)
                    self.logger.info(f"Corrupted signal file backed up to {backup_file}")
                except Exception as backup_error:
                    self.logger.log_error(backup_error, {'operation': 'backup_corrupted_file'})
            self.signal_history = []
    
    def get_signal_history(self, symbol: Optional[str] = None, days: int = 30) -> List[Dict]:
        """
        Get signal history for analysis.
        
        Args:
            symbol: Filter by symbol (optional)
            days: Number of days to look back
            
        Returns:
            List of historical signals
        """
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            
            filtered_signals = []
            for signal in self.signal_history:
                signal_date = datetime.fromisoformat(signal['timestamp'])
                if signal_date >= cutoff_date:
                    if symbol is None or signal['symbol'] == symbol:
                        filtered_signals.append(signal)
            
            return filtered_signals
            
        except Exception as e:
            self.logger.log_error(e, {'operation': 'get_signal_history'})
            return []
    
    def analyze_signal_performance(self, symbol: str, days: int = 30) -> Dict[str, float]:
        """
        Analyze signal performance for a symbol.
        
        Args:
            symbol: Stock symbol
            days: Number of days to analyze
            
        Returns:
            Performance metrics
        """
        try:
            signals = self.get_signal_history(symbol, days)
            
            if not signals:
                return {}
            
            # Calculate performance metrics
            total_signals = len(signals)
            buy_signals = len([s for s in signals if s['signal_type'] in ['BUY', 'STRONG_BUY']])
            sell_signals = len([s for s in signals if s['signal_type'] in ['SELL', 'STRONG_SELL']])
            
            strong_signals = len([s for s in signals if s['strength'] >= 3])
            high_confidence_signals = len([s for s in signals if s['confidence'] > 0.7])
            
            avg_confidence = sum(s['confidence'] for s in signals) / total_signals
            
            return {
                'total_signals': total_signals,
                'buy_signals': buy_signals,
                'sell_signals': sell_signals,
                'strong_signals': strong_signals,
                'high_confidence_signals': high_confidence_signals,
                'avg_confidence': avg_confidence,
                'buy_ratio': buy_signals / total_signals if total_signals > 0 else 0,
                'strong_signal_ratio': strong_signals / total_signals if total_signals > 0 else 0
            }
            
        except Exception as e:
            self.logger.log_error(e, {'operation': 'analyze_signal_performance', 'symbol': symbol})
            return {}
