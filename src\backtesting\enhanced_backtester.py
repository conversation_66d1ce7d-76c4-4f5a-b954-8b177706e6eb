#!/usr/bin/env python3
"""
Enhanced Backtesting Engine with Comprehensive Trade Logging and PnL Tracking

This module provides:
- Detailed trade logging with timestamps
- Accurate PnL calculation
- Comprehensive reporting
- Trade history export
- Performance metrics
- Technical indicators + Reddit sentiment + Tier 1 AI analysis
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Union, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import yfinance as yf
from pathlib import Path
import json
import os
import asyncio

from utils.config import get_config
from utils.logging import get_logger
from services.indicators import TechnicalIndicators, SignalType, IndicatorResult
from services.ai_advisor import AIAdvisor, AIAnalysisType
from services.sentiment_data import SentimentDataService


class TradeType(Enum):
    """Trade types for backtesting."""
    BUY = "BUY"
    SELL = "SELL"
    HOLD = "HOLD"


@dataclass
class Trade:
    """Enhanced trade record for backtesting."""
    symbol: str
    timestamp: datetime
    trade_type: TradeType
    quantity: float
    price: float
    value: float
    signal_strength: float
    indicators: Dict[str, float]
    sentiment_score: Optional[float] = None
    ai_recommendation: Optional[str] = None
    ai_confidence: Optional[float] = None
    ai_reasoning: Optional[str] = None
    pnl: float = 0.0
    cumulative_pnl: float = 0.0
    position_id: str = ""
    entry_price: float = 0.0
    exit_price: float = 0.0
    trade_duration: Optional[timedelta] = None
    commission: float = 0.0
    slippage: float = 0.0
    net_pnl: float = 0.0


@dataclass
class Position:
    """Enhanced position tracking for backtesting."""
    symbol: str
    quantity: float
    avg_price: float
    current_value: float
    unrealized_pnl: float
    entry_date: datetime
    position_id: str
    last_update: datetime
    total_commission: float = 0.0
    total_slippage: float = 0.0


@dataclass
class BacktestResult:
    """Enhanced results of a backtest run."""
    total_return: float
    annualized_return: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    profit_factor: float
    total_trades: int
    winning_trades: int
    losing_trades: int
    avg_win: float
    avg_loss: float
    trades: List[Trade]
    equity_curve: pd.Series
    positions: List[Position]
    metrics: Dict[str, float]
    start_date: datetime
    end_date: datetime
    initial_capital: float
    final_capital: float
    total_pnl: float
    total_commission: float
    total_slippage: float


class EnhancedBacktestEngine:
    """
    Enhanced backtesting engine with comprehensive trade logging and PnL tracking.
    
    Features:
    - Detailed trade logging with timestamps
    - Accurate PnL calculation including commissions and slippage
    - Comprehensive reporting
    - Trade history export to JSON/CSV
    - Performance metrics
    - Position tracking
    - Technical indicators + Reddit sentiment + Tier 1 AI analysis
    """
    
    def __init__(self, config: Dict, initial_capital: float = 100000):
        """
        Initialize the enhanced backtesting engine.
        
        Args:
            config: Configuration dictionary or Pydantic model
            initial_capital: Starting capital for backtesting
        """
        self.config = config
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.positions: Dict[str, Position] = {}
        self.trades: List[Trade] = []
        self.equity_curve = []
        self.logger = get_logger()
        self.position_counter = 0
        
        # Convert Pydantic model to dict if needed
        if hasattr(config, 'dict'):
            config_dict = config.dict()
        else:
            config_dict = config
        
        # Risk management parameters - handle both dict and Pydantic model
        if hasattr(config, 'risk_management'):
            # Pydantic model
            self.risk_config = config.risk_management
            self.max_position_size = self.risk_config.max_position_size
            self.stop_loss = self.risk_config.stop_loss_percentage
            self.take_profit = self.risk_config.take_profit_percentage
        else:
            # Dictionary
            self.risk_config = config_dict.get('risk_management', {})
            self.max_position_size = self.risk_config.get('max_position_size', 0.02)
            self.stop_loss = self.risk_config.get('stop_loss_percentage', 0.05)
            self.take_profit = self.risk_config.get('take_profit_percentage', 0.10)
        
        # Trading costs - handle both dict and Pydantic model
        if hasattr(config, 'trading'):
            # Pydantic model - use default values for now
            self.commission_rate = 0.005  # 0.5% commission
            self.slippage = 0.001  # 0.1% slippage
        else:
            # Dictionary
            trading_config = config_dict.get('trading', {})
            self.commission_rate = trading_config.get('commission_rate', 0.005)
            self.slippage = trading_config.get('slippage', 0.001)
        
        # Technical indicators
        self.indicators = TechnicalIndicators(config)
        
        # AI advisor for tier 1 analysis - convert to dict for compatibility
        self.ai_advisor = AIAdvisor(config_dict)
        
        # Sentiment service for Reddit sentiment - convert to dict for compatibility
        self.sentiment_service = SentimentDataService(config_dict)
        
        # Performance tracking
        self.total_commission = 0.0
        self.total_slippage = 0.0
        self.cumulative_pnl = 0.0
        
    async def run_backtest(self, symbols: List[str], start_date: str, end_date: str, 
                          strategy_func: Optional[Callable] = None) -> BacktestResult:
        """
        Run a complete enhanced backtest with AI and sentiment analysis.
        
        Args:
            symbols: List of symbols to backtest
            start_date: Start date for backtest (YYYY-MM-DD)
            end_date: End date for backtest (YYYY-MM-DD)
            strategy_func: Custom strategy function
            
        Returns:
            Enhanced backtest results
        """
        self.logger.log_performance({
            'event': 'enhanced_backtest_started',
            'symbols': symbols,
            'start_date': start_date,
            'end_date': end_date,
            'initial_capital': self.initial_capital
        })
        
        # Reset state
        self.current_capital = self.initial_capital
        self.positions = {}
        self.trades = []
        self.equity_curve = []
        self.total_commission = 0.0
        self.total_slippage = 0.0
        self.cumulative_pnl = 0.0
        self.position_counter = 0
        
        # Load historical data
        data = self._load_historical_data(symbols, start_date, end_date)
        
        # Run strategy
        if strategy_func:
            await self._run_custom_strategy(data, strategy_func)
        else:
            await self._run_enhanced_strategy(data)
        
        # Calculate results
        results = self._calculate_results(start_date, end_date)
        
        # Export trade history
        self._export_trade_history(results)
        
        self.logger.log_performance({
            'event': 'enhanced_backtest_completed',
            'total_return': results.total_return,
            'sharpe_ratio': results.sharpe_ratio,
            'max_drawdown': results.max_drawdown,
            'total_trades': results.total_trades
        })
        
        return results
    
    def _load_historical_data(self, symbols: List[str], start_date: str, end_date: str) -> Dict[str, pd.DataFrame]:
        """Load historical data for all symbols."""
        data = {}
        
        if not symbols:
            self.logger.log_error(
                Exception("No symbols provided for backtest"),
                {'operation': 'load_historical_data'}
            )
            return data
        
        for symbol in symbols:
            try:
                if not symbol or not isinstance(symbol, str):
                    self.logger.log_error(
                        Exception(f"Invalid symbol: {symbol}"),
                        {'symbol': symbol, 'operation': 'load_historical_data'}
                    )
                    continue
                
                ticker = yf.Ticker(symbol)
                df = ticker.history(start=start_date, end=end_date, interval='1d')
                
                if not df.empty and len(df) >= 10:  # Require minimum data points
                    # Validate data quality
                    if self._validate_backtest_data(df):
                        data[symbol] = df
                        self.logger.log_market_data(symbol, {
                            'data_points': len(df),
                            'date_range': f"{df.index[0].date()} to {df.index[-1].date()}"
                        })
                    else:
                        self.logger.log_error(
                            Exception(f"Data validation failed for {symbol}"),
                            {'symbol': symbol, 'operation': 'load_historical_data'}
                        )
                else:
                    self.logger.log_error(
                        Exception(f"Insufficient data for {symbol}"),
                        {'symbol': symbol, 'data_points': len(df) if not df.empty else 0}
                    )
                    
            except Exception as e:
                self.logger.log_error(e, {'symbol': symbol, 'operation': 'load_historical_data'})
        
        return data
    
    def _validate_backtest_data(self, df: pd.DataFrame) -> bool:
        """Validate backtest data quality."""
        if df.empty:
            return False
        
        # Check for required columns
        required_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
        if not all(col in df.columns for col in required_columns):
            return False
        
        # Check for sufficient data points
        if len(df) < 10:
            return False
        
        # Check for missing values
        if df[required_columns].isnull().any().any():
            return False
        
        # Check for zero or negative prices
        if (df[['Open', 'High', 'Low', 'Close']] <= 0).any().any():
            return False
        
        return True
    
    async def _run_enhanced_strategy(self, data: Dict[str, pd.DataFrame]):
        """Run enhanced strategy with technical indicators + sentiment + AI analysis."""
        # Get all unique dates
        all_dates = set()
        for symbol_data in data.values():
            all_dates.update(symbol_data.index)
        dates = sorted(list(all_dates))
        
        for date in dates:
            await self._process_daily_signals_enhanced(data, date)
            self._update_positions(data, date)
            self._record_equity(date)
    
    async def _process_daily_signals_enhanced(self, data: Dict[str, pd.DataFrame], date: datetime):
        """Process daily signals with enhanced analysis (technical + sentiment + AI)."""
        for symbol, symbol_data in data.items():
            if date not in symbol_data.index:
                continue
            
            # Get data up to current date
            current_data = symbol_data.loc[:date]
            if len(current_data) < 50:
                continue
            
            # Calculate technical indicators
            indicators = self.indicators.calculate_all_indicators(current_data)
            
            # Generate technical signal
            technical_signal, technical_confidence = self.indicators.generate_consensus_signal(indicators)
            
            if technical_signal is None:
                continue
            
            # Get sentiment data (Reddit)
            sentiment_score = None
            sentiment_data = []
            try:
                sentiment_data = await self.sentiment_service.get_sentiment_data(symbol, hours=24)
                if sentiment_data:
                    # Calculate aggregated sentiment
                    sentiment_scores = [item.sentiment_score for item in sentiment_data if item.sentiment_score is not None]
                    if sentiment_scores:
                        sentiment_score = np.mean(sentiment_scores)
                        self.logger.info(f"📊 {symbol}: Sentiment score: {sentiment_score:.3f} (from {len(sentiment_data)} data points)")
            except Exception as e:
                self.logger.warning(f"⚠️ Failed to get sentiment data for {symbol}: {e}")
            
            # Determine if we should use AI analysis (tier 1)
            should_use_ai = self._should_use_ai_analysis(technical_confidence, sentiment_score)
            
            ai_recommendation = None
            ai_confidence = None
            ai_reasoning = None
            
            if should_use_ai and self.ai_advisor.tier_1_providers:
                try:
                    # Create comprehensive prompt for AI analysis
                    prompt = self._create_ai_prompt(symbol, current_data, indicators, sentiment_data)
                    
                    # Get AI analysis from tier 1 providers
                    ai_result = await self.ai_advisor._get_consensus_analysis(
                        prompt, 
                        {'symbol': symbol, 'date': date.isoformat()}, 
                        AIAnalysisType.PATTERN
                    )
                    
                    if ai_result and ai_result.recommendation:
                        ai_recommendation = ai_result.recommendation
                        ai_confidence = ai_result.confidence
                        ai_reasoning = ai_result.reasoning
                        self.logger.info(f"🤖 {symbol}: AI recommendation: {ai_recommendation} (conf: {ai_confidence:.2f})")
                    
                except Exception as e:
                    self.logger.warning(f"⚠️ AI analysis failed for {symbol}: {e}")
            
            # Generate final signal based on all factors
            final_signal = self._generate_final_signal(
                technical_signal, technical_confidence, 
                sentiment_score, ai_recommendation, ai_confidence
            )
            
            # Execute signal
            current_price = current_data.iloc[-1]['Close']
            await self._execute_signal_enhanced(
                symbol, date, final_signal, current_price, 
                indicators=indicators, sentiment_score=sentiment_score,
                ai_recommendation=ai_recommendation, ai_confidence=ai_confidence,
                ai_reasoning=ai_reasoning
            )
    
    def _should_use_ai_analysis(self, technical_confidence: float, sentiment_score: Optional[float]) -> bool:
        """Determine if we should use AI analysis based on technical confidence and sentiment."""
        # Use AI if technical confidence is moderate to high (>0.4) or sentiment is strong (>0.6 or <-0.6)
        if technical_confidence and technical_confidence > 0.4:
            return True
        
        if sentiment_score and abs(sentiment_score) > 0.6:
            return True
        
        return False
    
    def _create_ai_prompt(self, symbol: str, data: pd.DataFrame, indicators: Dict[str, IndicatorResult], 
                         sentiment_data: List) -> str:
        """Create comprehensive prompt for AI analysis."""
        
        # Get latest technical indicators
        indicator_summary = []
        for name, result in indicators.items():
            if result.value is not None:
                indicator_summary.append(f"{name.upper()}: {result.value:.3f} ({result.signal.value})")
        
        # Get sentiment summary
        sentiment_summary = "No sentiment data available"
        if sentiment_data:
            sentiment_scores = [item.sentiment_score for item in sentiment_data if item.sentiment_score is not None]
            if sentiment_scores:
                avg_sentiment = np.mean(sentiment_scores)
                sentiment_summary = f"Average sentiment: {avg_sentiment:.3f} (from {len(sentiment_data)} sources)"
        
        # Get price data summary
        latest_price = data.iloc[-1]['Close']
        price_change = ((latest_price - data.iloc[-2]['Close']) / data.iloc[-2]['Close']) * 100 if len(data) > 1 else 0
        
        prompt = f"""Analyze {symbol} for trading recommendation based on the following data:

PRICE DATA:
- Current Price: ${latest_price:.2f}
- Price Change: {price_change:.2f}%
- 20-day SMA: ${data['Close'].rolling(20).mean().iloc[-1]:.2f}
- 50-day SMA: ${data['Close'].rolling(50).mean().iloc[-1]:.2f}

TECHNICAL INDICATORS:
{chr(10).join(indicator_summary)}

SENTIMENT DATA:
{sentiment_summary}

Provide a trading recommendation based on this comprehensive analysis. Consider:
1. Technical indicator signals and their strength
2. Price momentum and trend direction
3. Sentiment analysis from social media
4. Overall market conditions

Use these exact recommendation levels:
- STRONG_BUY: Very high confidence bullish signals across all factors
- BUY: Moderate confidence positive signals
- HOLD: Mixed or neutral signals
- SELL: Moderate confidence negative signals
- STRONG_SELL: Very high confidence bearish signals across all factors

Provide your reasoning in 2-3 sentences."""
        
        return prompt
    
    def _generate_final_signal(self, technical_signal: SignalType, technical_confidence: float,
                              sentiment_score: Optional[float], ai_recommendation: Optional[str],
                              ai_confidence: Optional[float]) -> SignalType:
        """Generate final signal based on technical indicators, sentiment, and AI analysis."""
        
        # Start with technical signal
        final_signal = technical_signal
        final_confidence = technical_confidence or 0.5
        
        # Adjust based on sentiment
        if sentiment_score is not None:
            if abs(sentiment_score) > 0.6:  # Strong sentiment
                if sentiment_score > 0.6 and technical_signal in [SignalType.BUY, SignalType.STRONG_BUY]:
                    final_signal = SignalType.STRONG_BUY
                    final_confidence = min(0.95, final_confidence + 0.1)
                elif sentiment_score < -0.6 and technical_signal in [SignalType.SELL, SignalType.STRONG_SELL]:
                    final_signal = SignalType.STRONG_SELL
                    final_confidence = min(0.95, final_confidence + 0.1)
                elif sentiment_score > 0.6 and technical_signal == SignalType.HOLD:
                    final_signal = SignalType.BUY
                    final_confidence = 0.6
                elif sentiment_score < -0.6 and technical_signal == SignalType.HOLD:
                    final_signal = SignalType.SELL
                    final_confidence = 0.6
        
        # Adjust based on AI recommendation
        if ai_recommendation and ai_confidence:
            ai_signal_map = {
                'STRONG_BUY': SignalType.STRONG_BUY,
                'BUY': SignalType.BUY,
                'HOLD': SignalType.HOLD,
                'SELL': SignalType.SELL,
                'STRONG_SELL': SignalType.STRONG_SELL
            }
            
            if ai_recommendation in ai_signal_map:
                ai_signal = ai_signal_map[ai_recommendation]
                
                # If AI agrees with current signal, boost confidence
                if ai_signal == final_signal:
                    final_confidence = min(0.95, final_confidence + (ai_confidence * 0.2))
                # If AI disagrees, reduce confidence
                elif ai_signal != SignalType.HOLD:
                    final_confidence = max(0.3, final_confidence - (ai_confidence * 0.1))
        
        return final_signal
    
    async def _execute_signal_enhanced(self, symbol: str, date: datetime, signal: SignalType, 
                                     price: float, indicators: Optional[Dict[str, IndicatorResult]] = None,
                                     sentiment_score: Optional[float] = None,
                                     ai_recommendation: Optional[str] = None,
                                     ai_confidence: Optional[float] = None,
                                     ai_reasoning: Optional[str] = None):
        """Execute a trading signal with enhanced logging including sentiment and AI data."""
        if signal == SignalType.HOLD:
            return
        
        # Calculate position size based on Kelly Criterion
        position_size = self._calculate_position_size(ai_confidence or 0.5, price)
        
        if signal in [SignalType.BUY, SignalType.STRONG_BUY]:
            if symbol not in self.positions:
                # Open new position
                quantity = position_size / price
                commission = quantity * price * self.commission_rate
                slippage_cost = quantity * price * self.slippage
                total_cost = quantity * price + commission + slippage_cost
                
                if total_cost <= self.current_capital:
                    position_id = f"POS_{self.position_counter:06d}"
                    self.position_counter += 1
                    
                    self.positions[symbol] = Position(
                        symbol=symbol,
                        quantity=quantity,
                        avg_price=price,
                        current_value=quantity * price,
                        unrealized_pnl=0,
                        entry_date=date,
                        position_id=position_id,
                        last_update=date,
                        total_commission=commission,
                        total_slippage=slippage_cost
                    )
                    
                    self.current_capital -= total_cost
                    self.total_commission += commission
                    self.total_slippage += slippage_cost
                    
                    trade = Trade(
                        symbol=symbol,
                        timestamp=date,
                        trade_type=TradeType.BUY,
                        quantity=quantity,
                        price=price,
                        value=total_cost,
                        signal_strength=ai_confidence or 0.5,
                        indicators={name: result.value for name, result in (indicators or {}).items()},
                        sentiment_score=sentiment_score,
                        ai_recommendation=ai_recommendation,
                        ai_confidence=ai_confidence,
                        ai_reasoning=ai_reasoning,
                        pnl=0.0,
                        cumulative_pnl=self.cumulative_pnl,
                        position_id=position_id,
                        entry_price=price,
                        commission=commission,
                        slippage=slippage_cost,
                        net_pnl=-total_cost
                    )
                    self.trades.append(trade)
                    
                    self.logger.log_trade(symbol, "BUY", quantity, price, date)
        
        elif signal in [SignalType.SELL, SignalType.STRONG_SELL]:
            if symbol in self.positions:
                # Close position
                position = self.positions[symbol]
                quantity = position.quantity
                commission = quantity * price * self.commission_rate
                slippage_cost = quantity * price * self.slippage
                proceeds = quantity * price - commission - slippage_cost
                
                # Calculate PnL (gross and net)
                gross_pnl = quantity * (price - position.avg_price)
                net_pnl = gross_pnl - commission - slippage_cost
                
                self.current_capital += proceeds
                self.total_commission += commission
                self.total_slippage += slippage_cost
                self.cumulative_pnl += net_pnl
                
                trade_duration = date - position.entry_date
                
                trade = Trade(
                    symbol=symbol,
                    timestamp=date,
                    trade_type=TradeType.SELL,
                    quantity=quantity,
                    price=price,
                    value=proceeds,
                    signal_strength=ai_confidence or 0.5,
                    indicators={name: result.value for name, result in (indicators or {}).items()},
                    sentiment_score=sentiment_score,
                    ai_recommendation=ai_recommendation,
                    ai_confidence=ai_confidence,
                    ai_reasoning=ai_reasoning,
                    pnl=gross_pnl,
                    cumulative_pnl=self.cumulative_pnl,
                    position_id=position.position_id,
                    entry_price=position.avg_price,
                    exit_price=price,
                    trade_duration=trade_duration,
                    commission=commission,
                    slippage=slippage_cost,
                    net_pnl=net_pnl
                )
                self.trades.append(trade)
                
                del self.positions[symbol]
                
                self.logger.log_trade(symbol, "SELL", quantity, price, date)
    
    def _calculate_position_size(self, confidence: float, price: float) -> float:
        """Calculate position size using Kelly Criterion with proper risk management."""
        # Use a more sophisticated position sizing approach
        if confidence is None or confidence <= 0:
            confidence = 0.5
        
        # Kelly Criterion calculation
        win_rate = confidence
        avg_win = self.take_profit
        avg_loss = self.stop_loss
        
        # Avoid division by zero
        if avg_win <= 0:
            avg_win = 0.1
        
        kelly_fraction = (win_rate * avg_win - (1 - win_rate) * avg_loss) / avg_win
        
        # Limit position size for risk management
        max_position_value = self.current_capital * self.max_position_size
        kelly_position_value = self.current_capital * max(0, min(kelly_fraction, 0.25))
        
        # Use the smaller of the two limits
        position_value = min(max_position_value, kelly_position_value)
        
        # Ensure minimum position size for meaningful trades
        min_position_value = self.current_capital * 0.001  # 0.1% minimum
        position_value = max(position_value, min_position_value)
        
        return position_value
    
    def _update_positions(self, data: Dict[str, pd.DataFrame], date: datetime):
        """Update position values and check stop losses."""
        positions_to_close = []
        
        for symbol, position in self.positions.items():
            if symbol in data and date in data[symbol].index:
                current_price = data[symbol].loc[date]['Close']
                position.current_value = position.quantity * current_price
                position.unrealized_pnl = position.current_value - (position.quantity * position.avg_price)
                position.last_update = date
                
                # Check stop loss
                loss_pct = (position.avg_price - current_price) / position.avg_price
                if loss_pct >= self.stop_loss:
                    positions_to_close.append(symbol)
        
        # Close positions that hit stop loss
        for symbol in positions_to_close:
            position = self.positions[symbol]
            current_price = data[symbol].loc[date]['Close']
            
            commission = position.quantity * current_price * self.commission_rate
            slippage_cost = position.quantity * current_price * self.slippage
            proceeds = position.quantity * current_price - commission - slippage_cost
            
            gross_pnl = position.quantity * (current_price - position.avg_price)
            net_pnl = gross_pnl - commission - slippage_cost
            
            self.current_capital += proceeds
            self.total_commission += commission
            self.total_slippage += slippage_cost
            self.cumulative_pnl += net_pnl
            
            trade_duration = date - position.entry_date
            
            trade = Trade(
                symbol=symbol,
                timestamp=date,
                trade_type=TradeType.SELL,
                quantity=position.quantity,
                price=current_price,
                value=proceeds,
                signal_strength=0.0,
                indicators={},
                pnl=net_pnl,
                cumulative_pnl=self.cumulative_pnl,
                position_id=position.position_id,
                entry_price=position.avg_price,
                exit_price=current_price,
                trade_duration=trade_duration,
                commission=commission,
                slippage=slippage_cost,
                net_pnl=net_pnl
            )
            self.trades.append(trade)
            
            del self.positions[symbol]
    
    def _record_equity(self, date: datetime):
        """Record equity curve data."""
        total_position_value = sum(pos.current_value for pos in self.positions.values())
        total_equity = self.current_capital + total_position_value
        
        self.equity_curve.append({
            'date': date,
            'equity': total_equity,
            'cash': self.current_capital,
            'positions': total_position_value,
            'cumulative_pnl': self.cumulative_pnl
        })
    
    def _calculate_results(self, start_date: str, end_date: str) -> BacktestResult:
        """Calculate comprehensive backtest results."""
        if not self.equity_curve:
            raise ValueError("No equity curve data available")
        
        equity_df = pd.DataFrame(self.equity_curve)
        equity_df.set_index('date', inplace=True)
        
        # Calculate returns
        equity_df['returns'] = equity_df['equity'].pct_change()
        
        # Basic metrics
        initial_equity = self.initial_capital
        final_equity = equity_df['equity'].iloc[-1]
        total_return = (final_equity - initial_equity) / initial_equity
        annualized_return = self._calculate_annualized_return(equity_df)
        
        # Risk metrics
        sharpe_ratio = self._calculate_sharpe_ratio(equity_df)
        max_drawdown = self._calculate_max_drawdown(equity_df)
        
        # Trade metrics
        selling_trades = [t for t in self.trades if t.trade_type == TradeType.SELL]
        winning_trades = [t for t in selling_trades if t.pnl > 0]
        losing_trades = [t for t in selling_trades if t.pnl <= 0]
        
        win_rate = len(winning_trades) / len(selling_trades) if selling_trades else 0
        avg_win = np.mean([t.pnl for t in winning_trades]) if winning_trades else 0
        avg_loss = np.mean([t.pnl for t in losing_trades]) if losing_trades else 0
        
        profit_factor = abs(avg_win * len(winning_trades) / (avg_loss * len(losing_trades))) if losing_trades and avg_loss != 0 else 0
        
        return BacktestResult(
            total_return=total_return,
            annualized_return=annualized_return,
            sharpe_ratio=sharpe_ratio,
            max_drawdown=max_drawdown,
            win_rate=win_rate,
            profit_factor=profit_factor,
            total_trades=len(self.trades),
            winning_trades=len(winning_trades),
            losing_trades=len(losing_trades),
            avg_win=avg_win,
            avg_loss=avg_loss,
            trades=self.trades,
            equity_curve=equity_df['equity'],
            positions=list(self.positions.values()),
            metrics={
                'calmar_ratio': annualized_return / max_drawdown if max_drawdown > 0 else 0,
                'sortino_ratio': self._calculate_sortino_ratio(equity_df),
                'max_consecutive_losses': self._calculate_max_consecutive_losses(),
                'avg_trade_duration': self._calculate_avg_trade_duration()
            },
            start_date=datetime.strptime(start_date, '%Y-%m-%d'),
            end_date=datetime.strptime(end_date, '%Y-%m-%d'),
            initial_capital=self.initial_capital,
            final_capital=final_equity,
            total_pnl=self.cumulative_pnl,
            total_commission=self.total_commission,
            total_slippage=self.total_slippage
        )
    
    def _calculate_annualized_return(self, equity_df: pd.DataFrame) -> float:
        """Calculate annualized return."""
        try:
            if equity_df.empty or len(equity_df) < 2:
                return 0
            
            total_days = (equity_df.index[-1] - equity_df.index[0]).days
            if total_days <= 0:
                return 0
            
            final_equity = equity_df['equity'].iloc[-1]
            if final_equity <= 0 or self.initial_capital <= 0:
                return 0
            
            total_return = (final_equity - self.initial_capital) / self.initial_capital
            
            # Handle edge cases
            if total_return <= -1:  # Complete loss
                return -1.0
            
            annualized_return = ((1 + total_return) ** (365 / total_days)) - 1
            
            # Handle extreme values
            if np.isnan(annualized_return) or np.isinf(annualized_return):
                return 0
            
            return annualized_return
            
        except Exception as e:
            self.logger.log_error(e, {'operation': 'calculate_annualized_return'})
            return 0
    
    def _calculate_sharpe_ratio(self, equity_df: pd.DataFrame) -> float:
        """Calculate Sharpe ratio."""
        try:
            if equity_df.empty or 'returns' not in equity_df.columns:
                return 0
            
            returns = equity_df['returns'].dropna()
            if len(returns) < 2:
                return 0
            
            # Handle both dict and Pydantic model for risk-free rate
            if hasattr(self.config, 'performance'):
                risk_free_rate = self.config.performance.risk_free_rate
            else:
                risk_free_rate = self.config.get('performance', {}).get('risk_free_rate', 0.02)
            
            excess_returns = returns - risk_free_rate / 252  # Daily risk-free rate
            
            if len(excess_returns) < 2:
                return 0
            
            sharpe_ratio = np.mean(excess_returns) / np.std(excess_returns) * np.sqrt(252)
            
            if np.isnan(sharpe_ratio) or np.isinf(sharpe_ratio):
                return 0
            
            return sharpe_ratio
            
        except Exception as e:
            self.logger.log_error(e, {'operation': 'calculate_sharpe_ratio'})
            return 0
    
    def _calculate_sortino_ratio(self, equity_df: pd.DataFrame) -> float:
        """Calculate Sortino ratio."""
        try:
            if equity_df.empty or 'returns' not in equity_df.columns:
                return 0
            
            returns = equity_df['returns'].dropna()
            if len(returns) < 2:
                return 0
            
            # Handle both dict and Pydantic model for risk-free rate
            if hasattr(self.config, 'performance'):
                risk_free_rate = self.config.performance.risk_free_rate
            else:
                risk_free_rate = self.config.get('performance', {}).get('risk_free_rate', 0.02)
            
            excess_returns = returns - risk_free_rate / 252
            
            if len(excess_returns) < 2:
                return 0
            
            # Only consider downside returns
            downside_returns = excess_returns[excess_returns < 0]
            if len(downside_returns) == 0:
                return 0
            
            downside_std = np.std(downside_returns)
            if downside_std == 0:
                return 0
            
            sortino_ratio = np.mean(excess_returns) / downside_std * np.sqrt(252)
            
            if np.isnan(sortino_ratio) or np.isinf(sortino_ratio):
                return 0
            
            return sortino_ratio
            
        except Exception as e:
            self.logger.log_error(e, {'operation': 'calculate_sortino_ratio'})
            return 0
    
    def _calculate_max_drawdown(self, equity_df: pd.DataFrame) -> float:
        """Calculate maximum drawdown."""
        try:
            if equity_df.empty:
                return 0
            
            equity_series = equity_df['equity']
            peak = equity_series.expanding().max()
            drawdown = (equity_series - peak) / peak
            max_drawdown = drawdown.min()
            
            if np.isnan(max_drawdown) or np.isinf(max_drawdown):
                return 0
            
            return abs(max_drawdown)
            
        except Exception as e:
            self.logger.log_error(e, {'operation': 'calculate_max_drawdown'})
            return 0
    
    def _calculate_max_consecutive_losses(self) -> int:
        """Calculate maximum consecutive losses."""
        try:
            selling_trades = [t for t in self.trades if t.trade_type == TradeType.SELL]
            if not selling_trades:
                return 0
            
            max_consecutive = 0
            current_consecutive = 0
            
            for trade in selling_trades:
                if trade.pnl < 0:
                    current_consecutive += 1
                    max_consecutive = max(max_consecutive, current_consecutive)
                else:
                    current_consecutive = 0
            
            return max_consecutive
            
        except Exception as e:
            self.logger.log_error(e, {'operation': 'calculate_max_consecutive_losses'})
            return 0
    
    def _calculate_avg_trade_duration(self) -> float:
        """Calculate average trade duration in days."""
        try:
            selling_trades = [t for t in self.trades if t.trade_type == TradeType.SELL and t.trade_duration]
            if not selling_trades:
                return 0
            
            durations = [t.trade_duration.total_seconds() / (24 * 3600) for t in selling_trades]
            return np.mean(durations)
            
        except Exception as e:
            self.logger.log_error(e, {'operation': 'calculate_avg_trade_duration'})
            return 0
    
    def _export_trade_history(self, results: BacktestResult):
        """Export comprehensive trade history to JSON and CSV."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Create backtests directory if it doesn't exist
        backtests_dir = Path("backtests")
        backtests_dir.mkdir(exist_ok=True)
        
        # Export trade history to JSON
        trade_history = {
            'backtest_info': {
                'start_date': results.start_date.isoformat(),
                'end_date': results.end_date.isoformat(),
                'initial_capital': results.initial_capital,
                'final_capital': results.final_capital,
                'total_return': results.total_return,
                'total_pnl': results.total_pnl,
                'total_commission': results.total_commission,
                'total_slippage': results.total_slippage,
                'total_trades': results.total_trades,
                'winning_trades': results.winning_trades,
                'losing_trades': results.losing_trades,
                'win_rate': results.win_rate,
                'sharpe_ratio': results.sharpe_ratio,
                'max_drawdown': results.max_drawdown
            },
            'trades': []
        }
        
        for trade in results.trades:
            trade_dict = asdict(trade)
            # Convert datetime objects to strings
            trade_dict['timestamp'] = trade.timestamp.isoformat()
            if trade.trade_duration:
                trade_dict['trade_duration'] = str(trade.trade_duration)
            trade_history['trades'].append(trade_dict)
        
        # Save JSON file
        json_filename = backtests_dir / f"trade_history_{timestamp}.json"
        with open(json_filename, 'w') as f:
            json.dump(trade_history, f, indent=2, default=str)
        
        # Export to CSV
        csv_filename = backtests_dir / f"trade_history_{timestamp}.csv"
        trades_df = pd.DataFrame([asdict(trade) for trade in results.trades])
        if not trades_df.empty:
            # Convert datetime columns
            trades_df['timestamp'] = pd.to_datetime(trades_df['timestamp'])
            trades_df['trade_duration'] = trades_df['trade_duration'].apply(lambda x: str(x) if x else '')
            trades_df.to_csv(csv_filename, index=False)
        
        self.logger.info(f"Trade history exported to {json_filename} and {csv_filename}")
        
        return json_filename, csv_filename


async def run_enhanced_backtest(symbols: List[str], start_date: str, end_date: str, 
                               config: Optional[Dict] = None) -> BacktestResult:
    """
    Run an enhanced backtest with comprehensive trade logging, sentiment analysis, and AI analysis.
    
    Args:
        symbols: List of symbols to backtest
        start_date: Start date for backtest (YYYY-MM-DD)
        end_date: End date for backtest (YYYY-MM-DD)
        config: Configuration dictionary
        
    Returns:
        Enhanced backtest results
    """
    if config is None:
        config = get_config()
    
    engine = EnhancedBacktestEngine(config)
    return await engine.run_backtest(symbols, start_date, end_date)
