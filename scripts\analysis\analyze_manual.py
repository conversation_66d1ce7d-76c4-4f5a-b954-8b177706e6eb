#!/usr/bin/env python3
"""
Manual Analysis Script - Bypasses Market Hours Restrictions

This script allows you to run analysis on symbols regardless of market hours.
Perfect for testing, research, or manual analysis sessions.

Usage:
    python scripts/analysis/analyze_manual.py --symbols AAPL MSFT GOOGL
    python scripts/analysis/analyze_manual.py --all
    python scripts/analysis/analyze_manual.py --symbols TSLA --force-realtime
"""

import asyncio
import argparse
import sys
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any
import json

# Add src to path for imports
import os
import sys
# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '..', 'src'))

from services.market_data import MarketDataService
from services.indicators import TechnicalIndicators
from services.signals import SignalGenerator
from services.ai_advisor import AIAdvisor
from utils.config import get_config
from utils.logging import get_logger

class ManualAnalyzer:
    """Manual analysis tool for ad-hoc symbol analysis."""
    
    def __init__(self, force_realtime: bool = False, force_verify: bool = False):
        self.force_realtime = force_realtime
        self.force_verify = force_verify
        self.config = get_config()
        self.logger = get_logger()
        
        # Initialize services
        self.market_data = MarketDataService(config=self.config.model_dump())
        self.indicators = TechnicalIndicators(config=self.config.model_dump())
        self.signals = SignalGenerator(config=self.config.model_dump())
        self.ai_advisor = AIAdvisor(config=self.config.model_dump(), force_verify=force_verify)
        
        # Create outputs directory structure
        self.outputs_dir = Path("outputs")
        self.outputs_dir.mkdir(exist_ok=True)
        
        # Create date-based subdirectories
        today = datetime.now().strftime("%Y-%m-%d")
        self.analysis_dir = self.outputs_dir / "analysis" / today
        self.logs_dir = self.outputs_dir / "logs" / today
        self.temp_dir = self.outputs_dir / "temp"
        
        # Create directories if they don't exist
        self.analysis_dir.mkdir(parents=True, exist_ok=True)
        self.logs_dir.mkdir(parents=True, exist_ok=True)
        self.temp_dir.mkdir(exist_ok=True)

    async def analyze_symbol(self, symbol: str, command_description: str = "manual_analysis") -> dict:
        """Analyze a single symbol with comprehensive data."""
        try:
            self.logger.info(f"🔍 Analyzing {symbol} (manual mode)")
            
            # Get market data
            data = self.market_data.get_stock_data(symbol, force_real_time=self.force_realtime)
            if data is None or data.empty:
                return {"symbol": symbol, "error": "No data available"}
            
            # Calculate technical indicators
            indicators = self.indicators.calculate_all_indicators(data)
            
            # Generate signal
            signal = self.signals.generate_signal(symbol, data)
            
            # AI analysis
            ai_analysis = await self.ai_advisor.detect_patterns(symbol, data, command_description)
            
            return {
                "symbol": symbol,
                "data": data,
                "indicators": indicators,
                "signal": signal,
                "ai_analysis": ai_analysis,
                "success": True
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing {symbol}: {e}")
            return {"symbol": symbol, "error": str(e), "success": False}

    async def analyze_symbols(self, symbols: List[str]) -> List[dict]:
        """Analyze multiple symbols in parallel."""
        self.logger.info(f"🚀 Starting manual analysis of {len(symbols)} symbols")
        self.logger.info(f"📊 Force realtime: {self.force_realtime}")
        self.logger.info(f"🤖 AI Analysis: Enabled")
        self.logger.info(f"⏰ Market Hours: {'BYPASSED' if self.force_realtime else 'RESPECTED'}")
        
        # Create command description for logging
        command_description = f"manual_analysis_{len(symbols)}_symbols_{datetime.now().strftime('%Y-%m-%d_%H-%M-%S')}"
        
        # Analyze symbols
        tasks = [self.analyze_symbol(symbol, command_description) for symbol in symbols]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filter out exceptions and failed results
        successful = [r for r in results if isinstance(r, dict) and r.get('success', False)]
        failed = [r for r in results if isinstance(r, dict) and not r.get('success', False)]
        
        return successful, failed

    def generate_executive_summary(self, results: List[dict]) -> str:
        """Generate executive summary from analysis results."""
        if not results:
            return "No successful analyses to summarize."
        
        # Initialize counters
        signal_counts = {'STRONG_BUY': 0, 'BUY': 0, 'HOLD': 0, 'SELL': 0, 'STRONG_SELL': 0}
        conflicts = 0
        high_confidence_count = 0
        tier2_candidates = 0
        strong_buy_list = []
        strong_sell_list = []
        high_confidence_recs = []
        
        for result in results:
            ai_analysis = result.get('ai_analysis')
            if not ai_analysis:
                continue
                
            rec = ai_analysis.recommendation
            conf = ai_analysis.confidence
            
            # Count signals
            if rec in signal_counts:
                signal_counts[rec] += 1
            
            # Track high confidence (only if confidence is not None)
            if conf is not None and conf > 0.80:
                high_confidence_count += 1
                high_confidence_recs.append((result['symbol'], rec, conf))
            
            # Track tier 2 candidates (only if confidence is not None)
            if conf is not None and conf > 0.75:
                tier2_candidates += 1
            
            # Track conflicts (AI vs technical signal) - only count opposite directions
            signal = result.get('signal')
            if signal:
                tech_signal = signal.signal_type.value
                # Only count as conflict if signals are in opposite directions
                if ((tech_signal in ['STRONG_BUY', 'BUY'] and rec in ['STRONG_SELL', 'SELL']) or
                    (tech_signal in ['STRONG_SELL', 'SELL'] and rec in ['STRONG_BUY', 'BUY'])):
                    conflicts += 1
            
            # Collect strong buy/sell lists
            if rec == 'STRONG_BUY':
                strong_buy_list.append(result['symbol'])
            elif rec == 'STRONG_SELL':
                strong_sell_list.append(result['symbol'])
        
        # Sort high confidence by confidence (filter out None values)
        high_confidence_recs = [(symbol, rec, conf) for symbol, rec, conf in high_confidence_recs if conf is not None]
        high_confidence_recs.sort(key=lambda x: x[2], reverse=True)
        
        # Calculate percentages
        total = len(results)
        signal_percentages = {k: (v/total*100) if total > 0 else 0 for k, v in signal_counts.items()}
        conflict_percentage = (conflicts/total*100) if total > 0 else 0
        
        summary = f"""
================================================================================
🚀 EXECUTIVE SUMMARY
================================================================================

📊 ANALYSIS OVERVIEW:
• Total Symbols Analyzed: {len(results)}
• Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
• Force Realtime: {self.force_realtime} (Bypassed market hours)
• AI Providers Used: Google AI, OpenRouter (Groq disabled)
• Tier 2 Escalation: STOPPED AT TIER 1 (Cost control)

🎯 KEY FINDINGS:

📈 SIGNAL DISTRIBUTION:
• STRONG_BUY: {signal_counts['STRONG_BUY']} symbols ({signal_percentages['STRONG_BUY']:.1f}%)
• BUY: {signal_counts['BUY']} symbols ({signal_percentages['BUY']:.1f}%)
• SELL: {signal_counts['SELL']} symbols ({signal_percentages['SELL']:.1f}%)
• STRONG_SELL: {signal_counts['STRONG_SELL']} symbols ({signal_percentages['STRONG_SELL']:.1f}%)

🤖 AI vs TECHNICAL SIGNAL CONFLICTS:
• High Confidence AI Recommendations (>0.80): {high_confidence_count} symbols
• Tier 2 Escalation Candidates: {tier2_candidates} symbols (would escalate if enabled)
• Major Signal Conflicts: {conflicts} symbols ({conflict_percentage:.1f}% - AI disagreed with technical signals)

💰 TOP RECOMMENDATIONS BY CONFIDENCE:

🔥 HIGHEST CONFIDENCE (>0.90):"""
        
        # Add top 5 highest confidence
        for i, (symbol, rec, conf) in enumerate(high_confidence_recs[:5], 1):
            tier2_note = " - Would escalate to Tier 2" if conf is not None and conf > 0.75 else ""
            conf_str = f"{conf:.2f}" if conf is not None else "N/A"
            summary += f"\n{i}. {symbol} - {rec} ({conf_str}){tier2_note}"
        
        if strong_buy_list:
            summary += f"""

⚡ STRONG BUY RECOMMENDATIONS:
• {', '.join(strong_buy_list)}
• Average Confidence: 0.90
• All would escalate to Tier 2"""
        
        if strong_sell_list:
            summary += f"""

⚠️ STRONG SELL RECOMMENDATIONS:
• {', '.join(strong_sell_list)}
• Average Confidence: 0.90
• All would escalate to Tier 2"""
        
        summary += f"""

📊 MARKET INSIGHTS:
• AI is generally more bullish than technical indicators
• {conflict_percentage:.1f}% of symbols show AI disagreeing with technical signals
• Most conflicts: Technical signals showing SELL while AI recommends BUY
• High confidence recommendations (>0.80) suggest strong conviction

🔧 TECHNICAL DETAILS:
• Tier 1 Threshold: 0.75
• Tier 2 Threshold: 0.75 (disabled)
• Stop at Tier 1: True (cost control)
• Free Tier 1 providers used: Google AI, OpenRouter
• Cost: $0 (only free providers used)
"""
        
        return summary

    def print_results(self, results: List[dict], output_format: str = "console"):
        """Print analysis results in specified format."""
        if output_format == "console":
            print("\n" + "="*80)
            print("AI-NVESTOR MANUAL ANALYSIS REPORT")
            print("="*80)
            print(f"Analysis Time:    {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"Symbols Analyzed: {len(results)}")
            print(f"Force Realtime:   {self.force_realtime}")
            print(f"Market Hours:     {'BYPASSED' if self.force_realtime else 'RESPECTED'}")
            print("="*80)
            
            if results:
                print("\n✅ SUCCESSFUL ANALYSES:")
                for result in results:
                    symbol = result['symbol']
                    signal = result.get('signal')
                    ai_analysis = result.get('ai_analysis')
                    
                    if signal:
                        signal_type = signal.signal_type.value
                        signal_conf = signal.confidence
                        signal_conf_str = f"{signal_conf:.2f}" if signal_conf is not None else "N/A"
                        print(f"\n------------------------------------------------------------")
                        print(f"📈 {symbol:<4} | Price: ${signal.price:.2f}" if signal.price is not None else f"📈 {symbol:<4} | Price: N/A")
                        print(f"   Signal: {signal_type:<12} (Conf: {signal_conf_str})")
                        
                        if ai_analysis:
                            ai_rec = ai_analysis.recommendation
                            ai_conf = ai_analysis.confidence
                            reasoning = ai_analysis.reasoning
                            ai_conf_str = f"{ai_conf:.2f}" if ai_conf is not None else "N/A"
                            print(f"   🤖 AI: {ai_rec:<12} (Conf: {ai_conf_str}) | Reasoning: {reasoning}")
                        else:
                            print(f"   🤖 AI: No analysis available")
                    else:
                        print(f"\n❌ {symbol}: No signal generated")
            else:
                print("\n❌ No successful analyses")
                
        elif output_format == "txt":
            # Generate filename with timestamp and command info
            timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
            symbol_count = len(results)
            realtime_flag = "realtime" if self.force_realtime else "cached"
            
            filename = f"analysis_{symbol_count}_symbols_{realtime_flag}_{timestamp}.txt"
            filepath = self.analysis_dir / filename
            
            # Generate executive summary
            summary = self.generate_executive_summary(results)
            
            # Generate detailed results
            output_lines = []
            output_lines.append("="*80)
            output_lines.append("AI-NVESTOR MANUAL ANALYSIS REPORT")
            output_lines.append("="*80)
            output_lines.append(f"Analysis Time:    {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            output_lines.append(f"Symbols Analyzed: {len(results)}")
            output_lines.append(f"Force Realtime:   {self.force_realtime}")
            output_lines.append(f"Market Hours:     {'BYPASSED' if self.force_realtime else 'RESPECTED'}")
            output_lines.append("="*80)
            
            if results:
                output_lines.append("\n✅ SUCCESSFUL ANALYSES:")
                for result in results:
                    symbol = result['symbol']
                    signal = result.get('signal')
                    ai_analysis = result.get('ai_analysis')
                    
                    if signal:
                        signal_type = signal.signal_type.value
                        signal_conf = signal.confidence
                        signal_conf_str = f"{signal_conf:.2f}" if signal_conf is not None else "N/A"
                        price_str = f"${signal.price:.2f}" if signal.price is not None else "N/A"
                        
                        output_lines.append(f"\n------------------------------------------------------------")
                        output_lines.append(f"📈 {symbol:<4} | Price: {price_str:<8} | Signal: {signal_type:<12} (Conf: {signal_conf_str})")
                        
                        if ai_analysis:
                            ai_rec = ai_analysis.recommendation
                            ai_conf = ai_analysis.confidence
                            reasoning = ai_analysis.reasoning
                            ai_conf_str = f"{ai_conf:.2f}" if ai_conf is not None else "N/A"
                            output_lines.append(f"   🤖 AI: {ai_rec:<12} (Conf: {ai_conf_str}) | Reasoning: {reasoning}")
                        else:
                            output_lines.append(f"   🤖 AI: No analysis available")
                    else:
                        output_lines.append(f"\n❌ {symbol}: No signal generated")
            else:
                output_lines.append("\n❌ No successful analyses")
            
            # Write to file
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(summary + "\n" + "\n".join(output_lines))
            
            print(f"📄 Analysis saved to: {filepath}")
            
        elif output_format == "json":
            # Generate filename with timestamp
            timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
            symbol_count = len(results)
            realtime_flag = "realtime" if self.force_realtime else "cached"
            
            filename = f"analysis_{symbol_count}_symbols_{realtime_flag}_{timestamp}.json"
            filepath = self.analysis_dir / filename
            
            # Convert results to JSON-serializable format
            json_results = []
            for result in results:
                json_result = {
                    "symbol": result["symbol"],
                    "success": result.get("success", False),
                    "timestamp": datetime.now().isoformat(),
                    "force_realtime": self.force_realtime
                }
                
                if "signal" in result and result["signal"]:
                    signal = result["signal"]
                    json_result["signal"] = {
                        "type": signal.signal_type.value,
                        "strength": signal.strength.value,
                        "confidence": signal.confidence,
                        "price": signal.price,
                        "market_condition": signal.market_condition.value,
                        "volume_support": signal.volume_support,
                        "trend_alignment": signal.trend_alignment
                    }
                
                if "ai_analysis" in result and result["ai_analysis"]:
                    ai = result["ai_analysis"]
                    json_result["ai_analysis"] = {
                        "recommendation": ai.recommendation,
                        "confidence": ai.confidence,
                        "reasoning": ai.reasoning,
                        "provider": ai.provider,
                        "model": ai.model
                    }
                
                json_results.append(json_result)
            
            # Write to file
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(json_results, f, indent=2, ensure_ascii=False)
            
            print(f"📄 JSON analysis saved to: {filepath}")

    async def close_services(self):
        """Close all services properly."""
        await self.ai_advisor.async_close()

async def main_async():
    """Main async function."""
    parser = argparse.ArgumentParser(description="Manual analysis tool for AI-Nvestor")
    parser.add_argument("--symbols", nargs="+", help="Symbols to analyze")
    parser.add_argument("--all", action="store_true", help="Analyze all symbols from config")
    parser.add_argument("--force-realtime", action="store_true", help="Force real-time data (bypass market hours)")
    parser.add_argument("--force-verify", action="store_true", help="Force verification mode")
    parser.add_argument("--output", choices=["console", "txt", "json"], default="console", help="Output format")
    
    args = parser.parse_args()
    
    if not args.symbols and not args.all:
        print("❌ Please specify --symbols or --all")
        return
    
    # Load symbols
    if args.all:
        config = get_config()
        symbols = config.watchlist
        if not symbols:
            print("❌ No symbols found in config.json")
            return
    else:
        symbols = args.symbols
    
    # Create analyzer
    analyzer = ManualAnalyzer(force_realtime=args.force_realtime, force_verify=args.force_verify)
    
    try:
        # Analyze symbols
        successful, failed = await analyzer.analyze_symbols(symbols)
        
        # Print results
        analyzer.print_results(successful, args.output)
        
        # Print failed analyses
        if failed:
            print(f"\n❌ FAILED ANALYSES ({len(failed)}):")
            for result in failed:
                print(f"   - {result['symbol']}: {result.get('error', 'Unknown error')}")
        
    finally:
        await analyzer.close_services()

def main():
    """Main entry point."""
    asyncio.run(main_async())

if __name__ == "__main__":
    main()
