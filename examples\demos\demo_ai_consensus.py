"""
AI Consensus Analysis Example

This script demonstrates the complete data flow:
SYMBOL -> GET DATA -> SEND DATA TO the 3 AIs -> GET RESULTS and ANALYZE -> yield advice

Shows how STRONG_BUY/SELL consensus works when multiple AI providers agree.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'src'))

from utils.config import get_config
from services.ai_advisor import AIAdvisor, AIAnalysisType
from services.market_data import MarketDataService
import pandas as pd
import numpy as np
from datetime import datetime, <PERSON><PERSON><PERSON>

def analyze_symbol_with_ai_consensus(symbol: str, config: dict):
    """
    Complete analysis flow: Symbol -> Data -> AI Analysis -> Consensus
    
    Args:
        symbol: Stock symbol to analyze
        config: Application configuration
    """
    print(f"🔍 Analyzing {symbol} with AI Consensus")
    print("=" * 60)
    
    # Step 1: GET DATA
    print(f"📊 Step 1: Fetching market data for {symbol}")
    market_data_service = MarketDataService(config.model_dump())
    
    # Get recent market data (last 60 days for better analysis)
    data = market_data_service.get_stock_data(symbol, interval='1d', days=60)
    
    if data is None or data.empty:
        print(f"❌ No data available for {symbol}")
        return None
    
    print(f"   ✅ Retrieved {len(data)} data points")
    print(f"   📈 Price range: ${data['Low'].min():.2f} - ${data['High'].max():.2f}")
    print(f"   📅 Date range: {data.index[0].strftime('%Y-%m-%d')} to {data.index[-1].strftime('%Y-%m-%d')}")
    
    # Step 2: Initialize AI Advisor
    print(f"\n🤖 Step 2: Initializing AI Advisor")
    ai_advisor = AIAdvisor(config.model_dump())
    
    if not ai_advisor.enabled_providers:
        print("   ⚠️  No AI providers enabled - using placeholder analysis")
        print("   💡 Enable AI providers in config.json to get real AI analysis")
    
    print(f"   ✅ AI Advisor initialized with {len(ai_advisor.enabled_providers)} providers")
    
    # Step 3: SEND DATA TO AIs and GET RESULTS
    print(f"\n🧠 Step 3: Running AI Analysis")
    
    # Prepare sample news data (in real implementation, this would come from news API)
    sample_news = [
        {'title': f'{symbol} reports strong quarterly earnings', 'sentiment': 'positive'},
        {'title': f'Analysts upgrade {symbol} rating', 'sentiment': 'positive'},
        {'title': f'Market volatility affects {symbol} performance', 'sentiment': 'neutral'}
    ]
    
    # Run different types of AI analysis
    analyses = [
        ("Pattern Recognition", ai_advisor.detect_patterns, data),
        ("Price Prediction", lambda: ai_advisor.predict_price_movement(symbol, data), None),
        ("Risk Assessment", lambda: ai_advisor.assess_risk(symbol, data), None),
        ("Sentiment Analysis", lambda: ai_advisor.analyze_sentiment(symbol, sample_news), None)
    ]
    
    results = {}
    
    for analysis_name, analysis_func, analysis_data in analyses:
        print(f"\n   📊 {analysis_name}:")
        try:
            if analysis_data is not None:
                result = analysis_func(analysis_data)
            else:
                result = analysis_func()
            
            results[analysis_name] = result
            
            print(f"      Recommendation: {result.recommendation}")
            print(f"      Confidence: {result.confidence:.2f}")
            print(f"      Provider: {result.provider}")
            print(f"      Reasoning: {result.reasoning[:80]}...")
            
            # Highlight strong signals
            if result.recommendation in ["STRONG_BUY", "STRONG_SELL"]:
                print(f"      🚨 STRONG SIGNAL DETECTED: {result.recommendation}")
            
        except Exception as e:
            print(f"      ❌ Error: {e}")
    
    # Step 4: ANALYZE and yield advice
    print(f"\n🎯 Step 4: Consensus Analysis & Final Advice")
    print("=" * 60)
    
    # Calculate overall consensus
    recommendations = [r.recommendation for r in results.values() if r]
    confidences = [r.confidence for r in results.values() if r]
    
    if recommendations:
        # Count different recommendation types
        strong_buy_count = recommendations.count("STRONG_BUY")
        strong_sell_count = recommendations.count("STRONG_SELL")
        buy_count = recommendations.count("BUY")
        sell_count = recommendations.count("SELL")
        hold_count = recommendations.count("HOLD")
        
        print(f"📊 Analysis Results Summary:")
        print(f"   STRONG_BUY: {strong_buy_count}")
        print(f"   BUY: {buy_count}")
        print(f"   HOLD: {hold_count}")
        print(f"   SELL: {sell_count}")
        print(f"   STRONG_SELL: {strong_sell_count}")
        
        # Determine final consensus
        total_bullish = strong_buy_count + buy_count
        total_bearish = strong_sell_count + sell_count
        
        if strong_buy_count >= 2:
            final_recommendation = "STRONG_BUY"
            confidence_level = "Very High"
        elif strong_sell_count >= 2:
            final_recommendation = "STRONG_SELL"
            confidence_level = "Very High"
        elif total_bullish > total_bearish and total_bullish > hold_count:
            final_recommendation = "BUY"
            confidence_level = "Moderate"
        elif total_bearish > total_bullish and total_bearish > hold_count:
            final_recommendation = "SELL"
            confidence_level = "Moderate"
        else:
            final_recommendation = "HOLD"
            confidence_level = "Low"
        
        avg_confidence = np.mean(confidences)
        
        print(f"\n🎯 FINAL CONSENSUS ADVICE:")
        print(f"   Symbol: {symbol}")
        print(f"   Recommendation: {final_recommendation}")
        print(f"   Confidence Level: {confidence_level}")
        print(f"   Average Confidence: {avg_confidence:.2f}")
        
        # Provide actionable advice
        print(f"\n💡 ACTIONABLE ADVICE:")
        if final_recommendation == "STRONG_BUY":
            print("   🚀 STRONG BUY SIGNAL: Consider significant position")
            print("   📈 High confidence in bullish outcome")
            print("   ⚠️  Monitor for any changes in market conditions")
        elif final_recommendation == "BUY":
            print("   📈 BUY SIGNAL: Consider moderate position")
            print("   📊 Moderate confidence in positive outcome")
            print("   🔍 Continue monitoring for confirmation")
        elif final_recommendation == "HOLD":
            print("   ⏸️  HOLD SIGNAL: Maintain current position")
            print("   📊 Mixed signals - wait for clearer direction")
            print("   🔍 Monitor for new developments")
        elif final_recommendation == "SELL":
            print("   📉 SELL SIGNAL: Consider reducing position")
            print("   📊 Moderate confidence in negative outcome")
            print("   ⚠️  Monitor for reversal signals")
        elif final_recommendation == "STRONG_SELL":
            print("   🚨 STRONG SELL SIGNAL: Consider significant position reduction")
            print("   📉 High confidence in bearish outcome")
            print("   ⚠️  Monitor for any positive developments")
        
        return {
            'symbol': symbol,
            'recommendation': final_recommendation,
            'confidence': avg_confidence,
            'confidence_level': confidence_level,
            'analysis_results': results
        }
    
    else:
        print("❌ No analysis results available")
        return None

def main():
    """Run the AI consensus analysis example."""
    print("🤖 AI-Nvestor Consensus Analysis Example")
    print("=" * 60)
    print("Demonstrating: SYMBOL -> GET DATA -> SEND DATA TO AIs -> GET RESULTS -> ANALYZE -> ADVICE")
    print("=" * 60)
    
    # Load configuration
    config = get_config()
    
    # Analyze multiple symbols
    symbols = ['AAPL', 'GOOGL', 'MSFT']
    
    results = {}
    
    for symbol in symbols:
        result = analyze_symbol_with_ai_consensus(symbol, config)
        if result:
            results[symbol] = result
        
        print("\n" + "=" * 60)
    
    # Summary
    print("📊 ANALYSIS SUMMARY")
    print("=" * 60)
    
    if results:
        print(f"Analyzed {len(results)} symbols:")
        for symbol, result in results.items():
            print(f"   {symbol}: {result['recommendation']} ({result['confidence_level']} confidence)")
        
        # Portfolio recommendations
        strong_buys = [s for s, r in results.items() if r['recommendation'] == 'STRONG_BUY']
        buys = [s for s, r in results.items() if r['recommendation'] == 'BUY']
        holds = [s for s, r in results.items() if r['recommendation'] == 'HOLD']
        sells = [s for s, r in results.items() if r['recommendation'] == 'SELL']
        strong_sells = [s for s, r in results.items() if r['recommendation'] == 'STRONG_SELL']
        
        print(f"\n🎯 Portfolio Recommendations:")
        if strong_buys:
            print(f"   🚀 STRONG BUY: {', '.join(strong_buys)}")
        if buys:
            print(f"   📈 BUY: {', '.join(buys)}")
        if holds:
            print(f"   ⏸️  HOLD: {', '.join(holds)}")
        if sells:
            print(f"   📉 SELL: {', '.join(sells)}")
        if strong_sells:
            print(f"   🚨 STRONG SELL: {', '.join(strong_sells)}")
    
    print(f"\n💡 Next Steps:")
    print("   1. Add real AI API keys to config.json")
    print("   2. Enable AI providers for live analysis")
    print("   3. Integrate with real-time market data")
    print("   4. Set up automated trading signals")
    print("   5. Monitor consensus accuracy over time")

if __name__ == "__main__":
    main() 