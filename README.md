# 🤖 AI-Nvestor

**AI-Powered Trading Analysis Platform**

AI-Nvestor is a comprehensive trading analysis platform that combines technical indicators, sentiment analysis, and AI-powered insights to provide intelligent trading recommendations.

## 📁 **Project Structure**

```
ai-nvestor/
├── 📁 src/                          # Main source code
│   ├── 📁 core/                     # Core business logic
│   ├── 📁 services/                 # Business services
│   ├── 📁 utils/                    # Utility functions
│   ├── 📁 constants/                # Constants and settings
│   └── 📁 backtesting/              # Backtesting functionality
│
├── 📁 tests/                        # Test files
│   ├── 📁 unit/                     # Unit tests
│   │   ├── test_indicators.py       # Technical indicators
│   │   ├── test_config.py           # Configuration
│   │   └── test_fixes.py            # Bug fixes
│   ├── 📁 integration/              # Integration tests
│   │   ├── test_reddit_sentiment.py # Reddit sentiment
│   │   ├── test_apify_sentiment.py  # Apify sentiment
│   │   ├── test_adaptive_learning.py # Adaptive learning
│   │   └── test_platform_integration.py # Platform integration
│   ├── 📁 verification/             # Verification tests
│   │   ├── test_math_verification.py # Mathematical correctness
│   │   └── test_real_data_verification.py # Real data verification
│   └── 📁 fixtures/                 # Test data and fixtures
│
├── 📁 examples/                     # Example scripts and demos
│   ├── 📁 demos/                    # Demo scripts
│   │   ├── demo_ai.py               # AI functionality demo
│   │   ├── demo_system.py           # General system demo
│   │   └── demo_ai_consensus.py     # AI consensus demo
│   ├── 📁 tutorials/                # Tutorial scripts
│   └── 📁 utilities/                # Example utilities
│       └── count_symbols.py         # Symbol counting utility
│
├── 📁 scripts/                      # Utility scripts
│   ├── 📁 analysis/                 # Analysis scripts
│   │   └── analyze_manual.py        # Manual analysis utility
│   ├── 📁 setup/                    # Setup scripts
│   │   └── setup_project.py         # Project setup
│   ├── 📁 maintenance/              # Maintenance scripts
│   └── 📁 tools/                    # Utility tools
│
├── 📁 docs/                         # Documentation
├── 📁 data/                         # Data files
├── 📁 outputs/                      # Output files
└── 📁 logs/                         # Application logs
```

## 🚀 **Quick Start**

### **1. Setup**

```bash
# Clone the repository
git clone https://github.com/HVNT/ai-nvestor.git
cd ai-nvestor

# Install dependencies
pip install -r requirements.txt

# Setup project (optional - creates virtual environment and directories)
python scripts/setup/setup_project.py
```

### **2. Configuration**

1. **Copy environment template**:
   ```bash
   cp env.example .env
   ```

2. **Configure API keys** in `.env`:
   ```bash
   # Data Source API Keys
   ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key_here
   POLYGON_API_KEY=your_polygon_key_here
   
   # Reddit API (for sentiment analysis)
   REDDIT_CLIENT_ID=your_reddit_client_id_here
   REDDIT_CLIENT_SECRET=your_reddit_client_secret_here
   REDDIT_USERNAME=your_reddit_username_here
   REDDIT_PASSWORD=your_reddit_password_here
   
   # AI Provider API Keys
   OPENAI_API_KEY=your_openai_key_here
   ANTHROPIC_API_KEY=your_anthropic_key_here
   GOOGLE_AI_API_KEY=your_google_ai_key_here
   ```

### **3. Run Demos**

```bash
# AI functionality demo
python examples/demos/demo_ai.py

# System demo
python examples/demos/demo_system.py

# AI consensus demo
python examples/demos/demo_ai_consensus.py
```

### **4. Run Analysis**

```bash
# Manual analysis for specific symbols
python scripts/analysis/analyze_manual.py --symbols AAPL TSLA NVDA

# Analysis for all symbols in config
python scripts/analysis/analyze_manual.py --all

# Force real-time data (bypass market hours)
python scripts/analysis/analyze_manual.py --symbols AAPL --force-realtime

# Output to JSON file
python scripts/analysis/analyze_manual.py --symbols AAPL TSLA --output json
```

### **5. Run Main Platform**

```bash
# Run analysis mode (default)
python src/main.py --mode analysis --symbols AAPL TSLA

# Run monitoring mode
python src/main.py --mode monitor

# Run backtesting mode
python src/main.py --mode backtest
```

## 🧪 **Testing**

### **Unit Tests**
```bash
# Run all unit tests
python -m pytest tests/unit/

# Run specific test
python -m pytest tests/unit/test_indicators.py
```

### **Integration Tests**
```bash
# Run all integration tests
python -m pytest tests/integration/

# Run sentiment tests
python -m pytest tests/integration/test_reddit_sentiment.py

# Run AI backtest integration tests
python -m pytest tests/integration/test_ai_backtest_integration.py
```

### **Verification Tests**
```bash
# Run verification tests
python -m pytest tests/verification/

# Run math verification
python -m pytest tests/verification/test_math_verification.py
```

## 📊 **Features**

### **🤖 AI-Powered Analysis**
- **Multi-provider AI integration** (OpenAI, Anthropic, Google AI)
- **Consensus analysis** with STRONG_BUY/SELL signals
- **Pattern recognition** and price prediction
- **Risk assessment** and portfolio optimization

### **📈 Technical Analysis**
- **Comprehensive indicators** (RSI, MACD, Bollinger Bands, Stochastic)
- **Signal generation** with confidence scoring
- **Real-time data** from multiple sources
- **Historical backtesting** capabilities

### **🧠 Sentiment Analysis**
- **Reddit sentiment** from trading subreddits
- **News sentiment** from multiple sources
- **Social media sentiment** integration
- **Real-time sentiment** tracking

### **🔄 Adaptive Learning**
- **Machine learning** for weight optimization
- **Iterative intelligence** for continuous improvement
- **Feature importance** analysis
- **Performance tracking** and metrics

## 📚 **Documentation**

- **[Architecture](docs/ARCHITECTURE.md)** - System architecture overview
- **[API Documentation](docs/API_DOCUMENTATION.md)** - API reference
- **[Quick Start](docs/QUICK_START.md)** - Getting started guide
- **[Reddit Setup](docs/REDDIT_SETUP.md)** - Reddit API setup
- **[Adaptive Learning](docs/ADAPTIVE_LEARNING.md)** - ML features

## 🔧 **Development**

### **Adding New Tests**
```bash
# Unit tests go in tests/unit/
# Integration tests go in tests/integration/
# Verification tests go in tests/verification/
```

### **Adding New Demos**
```bash
# Demo scripts go in examples/demos/
# Tutorial scripts go in examples/tutorials/
```

### **Adding New Scripts**
```bash
# Analysis scripts go in scripts/analysis/
# Setup scripts go in scripts/setup/
# Maintenance scripts go in scripts/maintenance/
```

## 📈 **Performance**

- **Real-time analysis** for 60+ symbols
- **Parallel processing** for efficient data handling
- **Intelligent caching** to reduce API calls
- **Rate limiting** to respect API limits

## 🤝 **Contributing**

1. **Fork** the repository
2. **Create** a feature branch
3. **Add** tests for new functionality
4. **Run** all tests to ensure they pass
5. **Submit** a pull request

## 📄 **License**

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 **Support**

- **Documentation**: Check the [docs/](docs/) directory
- **Issues**: Report bugs and feature requests on GitHub
- **Discussions**: Join the community discussions

---

**Happy Trading! 🚀**
