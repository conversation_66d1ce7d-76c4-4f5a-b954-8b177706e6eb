#!/usr/bin/env python3
"""
AI Integration Demo for AI-Nvestor

This script demonstrates the AI-powered analysis capabilities including:
- Multi-provider AI integration (OpenAI, Anthropic, Google AI)
- STRONG_BUY/SELL consensus analysis
- Sentiment analysis, pattern recognition, price prediction
- Risk assessment and portfolio optimization
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '..', 'src'))

from utils.config import get_config
from services.ai_advisor import AIAdvisor, AIAnalysisType
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def main():
    print("🤖 AI-Nvestor AI Integration Demo")
    print("=" * 50)
    
    # Load configuration
    config = get_config()
    ai_config = config.ai_analysis
    
    print(f"\n📋 AI Configuration:")
    print(f"   Enabled: {'✅' if ai_config.enabled else '❌'}")
    print(f"   Model Type: {ai_config.model_type}")
    print(f"   Confidence Threshold: {ai_config.confidence_threshold}")
    
    # Show enabled providers
    providers = ai_config.providers
    print(f"\n🔌 AI Providers:")
    for provider_name, provider_config in providers.items():
        enabled = provider_config.enabled
        model = provider_config.model
        status = "✅" if enabled else "❌"
        print(f"   {provider_name.upper()}: {status} ({model})")
    
    # Show analysis types
    print(f"\n📈 Analysis Types:")
    analysis_types = ai_config.analysis_types
    for field_name, field_value in analysis_types.__dict__.items():
        if not field_name.startswith('_'):
            status = "✅" if field_value else "❌"
            print(f"   {field_name.replace('_', ' ').title()}: {status}")
    
    # Initialize AI advisor
    ai_advisor = AIAdvisor(config.model_dump())
    
    print(f"\n🤖 AI Advisor Status:")
    print(f"   Enabled Providers: {len(ai_advisor.enabled_providers)}")
    if ai_advisor.enabled_providers:
        provider_names = [p.provider_name for p in ai_advisor.enabled_providers]
        print(f"   Active Providers: {', '.join(provider_names)}")
    else:
        print("   ⚠️  No AI providers are enabled or configured")
        print("   💡 To enable AI providers:")
        print("      1. Add your API keys to config.json")
        print("      2. Set 'enabled': true for desired providers")
        print("      3. Configure models and parameters")
    
    # Demo with sample data
    print(f"\n🧪 Demo Analysis (using sample data):")
    
    # Sample market data
    dates = pd.date_range(start='2024-01-01', end='2024-12-01', freq='D')
    sample_data = pd.DataFrame({
        'Open': np.random.uniform(100, 200, len(dates)),
        'High': np.random.uniform(150, 250, len(dates)),
        'Low': np.random.uniform(50, 150, len(dates)),
        'Close': np.random.uniform(100, 200, len(dates)),
        'Volume': np.random.uniform(1000000, 5000000, len(dates))
    }, index=dates)
    
    # Sample news data
    sample_news = [
        {'title': 'Company reports strong Q4 earnings', 'sentiment': 'positive'},
        {'title': 'New product launch announced', 'sentiment': 'positive'},
        {'title': 'Market volatility increases', 'sentiment': 'neutral'}
    ]
    
    # Sample portfolio
    sample_portfolio = {'AAPL': 0.4, 'GOOGL': 0.3, 'MSFT': 0.3}
    
    # Run different types of analysis
    analyses = [
        ("Pattern Detection", ai_advisor.detect_patterns, sample_data),
        ("Price Prediction", lambda: ai_advisor.predict_price_movement("AAPL", sample_data), None),
        ("Risk Assessment", lambda: ai_advisor.assess_risk("AAPL", sample_data), None),
        ("Portfolio Optimization", lambda: ai_advisor.optimize_portfolio(sample_portfolio, ["AAPL", "GOOGL", "MSFT"], {}), None)
    ]
    
    for analysis_name, analysis_func, data in analyses:
        print(f"\n📊 {analysis_name}:")
        try:
            if data is not None:
                result = analysis_func(data)
            else:
                result = analysis_func()
            
            print(f"   Recommendation: {result.recommendation}")
            print(f"   Confidence: {result.confidence:.2f}")
            print(f"   Provider: {result.provider}")
            print(f"   Reasoning: {result.reasoning[:100]}...")
            
            # Highlight STRONG_BUY/SELL recommendations
            if result.recommendation in ["STRONG_BUY", "STRONG_SELL"]:
                print(f"   🚨 STRONG SIGNAL: {result.recommendation}")
            
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print(f"\n🎯 STRONG_BUY/SELL Consensus Features:")
    print("   • Enhanced parsing for 'STRONG_BUY' and 'STRONG_SELL' recommendations")
    print("   • Consensus requires 2+ AI providers to agree for strong signals")
    print("   • Detailed breakdown of individual AI provider recommendations")
    print("   • Improved system prompts to encourage specific recommendation levels")
    
    print(f"\n💡 Next Steps:")
    print("   1. Add your API keys to config.json")
    print("   2. Enable desired AI providers")
    print("   3. Configure analysis types")
    print("   4. Run with real market data")
    print("   5. Monitor consensus signals for trading decisions")

if __name__ == "__main__":
    main() 