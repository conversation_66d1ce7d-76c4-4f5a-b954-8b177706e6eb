# 🔧 Trading Services

> **The core business logic of the AI-Nvestor platform, including market data, signal generation, AI analysis, and notifications.**

## 📊 **Services Overview**

The `services/` directory contains the primary components responsible for the trading logic. Each service is designed to be modular, independently testable, and managed by the `ServiceFactory` in the `core` module.

```
services/
├── market_data.py    # Multi-source data aggregation and caching
├── signals.py        # Advanced signal generation with consensus logic
├── indicators.py     # 12+ vectorized technical indicators
├── alerts.py         # Real-time notifications (SMS, Email, etc.)
└── ai_advisor.py     # Tiered, cost-optimized AI analysis
```

## 🚀 **Key Service Features**

### **Market Data Service**
- **Multi-Source Aggregation**: Fetches data from Yahoo Finance, Alpha Vantage, and Polygon.io, with automatic fallbacks.
- **Intelligent Caching**: A time-aware caching layer reduces redundant API calls, especially outside of market hours.
- **Trading Hours Awareness**: Differentiates between pre-market, regular hours, and after-hours to fetch the most appropriate data.

### **Signal Generation Service**
- **Multi-Indicator Consensus**: Generates a single, high-confidence signal by weighing the output of over a dozen technical indicators (RSI, MACD, Bollinger Bands, etc.).
- **Signal Strength**: Classifies signals from `WEAK` to `VERY_STRONG` based on the level of consensus.
- **Market Condition Analysis**: Considers the broader market trend (bullish, bearish, sideways) when generating signals.

### **AI Advisor Service**
- **Tiered AI Orchestrator**: Uses a cost-optimization strategy by querying free, high-speed AI models (Tier 1) for initial analysis, and only engaging powerful, paid models (Tier 2) to validate signals that have high confidence.
- **Multi-Provider Consensus**: Gathers insights from up to 7 different AI providers to form a robust, AI-driven recommendation.
- **Async by Design**: All AI provider API calls are made concurrently for maximum performance.

### **Alert System Service**
- **Multi-Channel Notifications**: Can be configured to send alerts via SMS (Twilio), Email, Slack, and Discord.
- **Smart Filtering**: Only sends alerts for high-confidence signals that have been validated by Tier 2 AI, preventing notification fatigue.
- **Rate Limiting**: Manages the frequency of alerts to avoid spam and control costs.

## 🔄 **Service Integration**

All services are designed to work together seamlessly. The typical flow is as follows:

1.  **`MarketDataService`** fetches and caches the latest market data.
2.  **`TechnicalIndicators`** calculates all technical indicators on the data.
3.  **`SignalGenerator`** forms a consensus signal from the indicators.
4.  **`AIAdvisor`** performs a tiered AI analysis to validate and enhance the signal.
5.  **`AlertSystem`** sends a notification if the final, AI-enhanced signal meets the configured confidence threshold.
