# AI-Nvestor Commands Reference

## 🚀 **Main Platform Commands**

The primary way to interact with the platform is through `run.py`.

```bash
# Analyze symbols from your watchlist
python run.py --mode analysis --symbols AAPL MSFT GOOGL

# Analyze all symbols in the config watchlist
python run.py --mode analysis

# Start real-time monitoring (analyzes watchlist every 5 minutes)
python run.py --mode monitor

# Run a backtest for the watchlist over a specified period
python run.py --mode backtest --start-date 2023-01-01
```

## 🛠️ **Ad-hoc & Demo Scripts**

These are standalone scripts for specific tasks, demos, or analysis that works outside of market hours.

### **Manual Analysis (`scripts/analyze_manual.py`)**

This is the main script for ad-hoc analysis. It bypasses market-hour restrictions and is useful for quick checks or data exports.

```bash
# Analyze a few specific symbols
python scripts/analyze_manual.py --symbols AAPL MSFT

# Analyze all symbols from your config watchlist
python scripts/analyze_manual.py --all

# Force the use of real-time data instead of cache
python scripts/analyze_manual.py --symbols NVDA --force-realtime

# Export analysis results to JSON or CSV
python scripts/analyze_manual.py --symbols AMD --output json > analysis.json
python scripts/analyze_manual.py --symbols AMD --output csv > analysis.csv
```

### **AI Demos**

These scripts demonstrate the AI capabilities. **Note:** They require valid AI provider API keys in your `.env` file.

```bash
# A simple demo of the AI advisor's analysis capabilities
python scripts/demo_ai.py

# A more complex example showing the full AI consensus and tiered logic
python examples/example_ai_consensus.py
```

### **Backtesting Demo (`scripts/demo.py`)**

A simple script to demonstrate the backtesting engine's capabilities over the last year.

```bash
python scripts/demo.py
```

## 🧪 **Troubleshooting**

You can quickly check if core components are importable and configured correctly.

```bash
# Test configuration loading
python -c "import sys; sys.path.insert(0, 'src'); from utils.config import get_config; print('Config OK')"

# Test logging system
python -c "import sys; sys.path.insert(0, 'src'); from utils.logging import get_logger; get_logger().info('Logging OK')"

# Check if services can be imported
python -c "import sys; sys.path.insert(0, 'src'); from services.market_data import MarketDataService; print('MarketDataService OK')"
python -c "import sys; sys.path.insert(0, 'src'); from services.ai_advisor import AIAdvisor; print('AIAdvisor OK')"
```
