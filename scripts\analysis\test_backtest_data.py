#!/usr/bin/env python3
"""
Test script to debug backtest data loading
"""

import sys
import os
from datetime import datetime
import pandas as pd

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '..', 'src'))

from backtesting.backtest import BacktestEngine
from utils.config import get_config

def test_backtest_data_loading():
    """Test backtest data loading."""
    print("🧪 Testing Backtest Data Loading")
    print("=" * 40)
    
    # Get configuration
    config = get_config()
    config_dict = config.model_dump()
    
    # Initialize backtest engine
    engine = BacktestEngine(config_dict, initial_capital=100000)
    
    # Test data loading
    symbols = ["AAPL"]
    start_date = "2022-01-01"
    end_date = "2022-01-31"
    
    print(f"🔍 Loading data for {symbols}")
    print(f"📅 Period: {start_date} to {end_date}")
    
    data = engine._load_historical_data(symbols, start_date, end_date)
    
    print(f"\n📊 Data Loading Results:")
    print(f"   Symbols loaded: {list(data.keys())}")
    
    for symbol, df in data.items():
        print(f"\n📈 {symbol} Data:")
        print(f"   Shape: {df.shape}")
        print(f"   Columns: {list(df.columns)}")
        print(f"   Date range: {df.index[0]} to {df.index[-1]}")
        print(f"   Latest price: ${df['Close'].iloc[-1]:.2f}")
        
        # Check if columns are multi-level
        if isinstance(df.columns, pd.MultiIndex):
            print(f"   ⚠️ Multi-level columns detected!")
            print(f"   Flattened columns: {df.columns.get_level_values(0).tolist()}")
            
            # Flatten columns for testing
            df.columns = df.columns.get_level_values(0)
        
        # Test signal generation
        print(f"\n🔍 Testing signal generation for {symbol}:")
        try:
            from services.signals import SignalGenerator
            signal_generator = SignalGenerator(config_dict)
            
            # Test with the last 50 days of data
            test_data = df.tail(50)
            if len(test_data) >= 50:
                signal = signal_generator.generate_signal(symbol, test_data)
                if signal:
                    print(f"   ✅ Signal: {signal.signal_type.value} | Strength: {signal.strength.value} | Confidence: {signal.confidence:.2f}")
                else:
                    print(f"   ❌ No signal generated")
            else:
                print(f"   ⚠️ Insufficient data for signal generation (need 50+, got {len(test_data)})")
        except Exception as e:
            print(f"   ❌ Error generating signal: {e}")

if __name__ == "__main__":
    test_backtest_data_loading() 