# LLM API Limit Management Guide

## 🚨 **API Limit Challenges**

### **Common Rate Limits**
- **OpenAI GPT-4**: 3,500 requests/minute, 10,000 requests/hour
- **Anthropic Claude**: 50 requests/minute, 800 requests/hour  
- **Google AI Gemini**: 60 requests/minute, 1,000 requests/hour
- **Cost per request**: $0.01-$0.10 per analysis

### **Risk Factors**
- **High-frequency trading**: Multiple symbols analyzed simultaneously
- **Real-time analysis**: Continuous market monitoring
- **Multiple AI providers**: Tripling API calls for consensus
- **Extended hours**: 16+ hours of potential analysis

## 🛡️ **Comprehensive Solutions**

### **1. Intelligent Caching System**

```python
# Cache configuration in config.json
{
  "ai_cache": {
    "enabled": true,
    "max_age_hours": 24,
    "cache_dir": "data/ai_cache",
    "smart_caching": {
      "sentiment_analysis": 48,      # 48 hours (news doesn't change quickly)
      "pattern_recognition": 12,     # 12 hours (patterns evolve slowly)
      "price_prediction": 2,         # 2 hours (market changes rapidly)
      "risk_assessment": 6,          # 6 hours (risk changes moderately)
      "portfolio_optimization": 24   # 24 hours (portfolio changes slowly)
    }
  }
}
```

**Benefits:**
- **80-90% API reduction** for repeated analyses
- **Intelligent expiration** based on analysis type
- **Market-aware caching** (longer cache during low volatility)

### **2. Request Batching & Prioritization**

```python
# Batch similar requests together
def batch_ai_requests(symbols: List[str], analysis_type: str):
    """Batch multiple symbols for single AI request."""
    
    # Group symbols by analysis type
    if analysis_type == "sentiment":
        # Combine multiple symbols in one prompt
        combined_prompt = f"Analyze sentiment for: {', '.join(symbols)}"
        return single_ai_request(combined_prompt)
    
    elif analysis_type == "pattern":
        # Analyze patterns across related symbols
        sector_symbols = group_by_sector(symbols)
        return [analyze_sector_patterns(sector) for sector in sector_symbols]
```

### **3. Market-Aware Request Scheduling**

```python
# Request frequency based on market conditions
def get_request_frequency(market_volatility: float, trading_hours: bool):
    """Determine optimal request frequency."""
    
    if not trading_hours:
        return 3600  # 1 hour during off-hours
    
    if market_volatility > 0.05:  # High volatility
        return 300   # 5 minutes
    elif market_volatility > 0.02:  # Medium volatility  
        return 900   # 15 minutes
    else:  # Low volatility
        return 1800  # 30 minutes
```

### **4. Provider Rotation & Load Balancing**

```python
# Rotate providers to distribute load
class ProviderRotator:
    def __init__(self):
        self.providers = ["openai", "anthropic", "google_ai"]
        self.current_index = 0
        self.request_counts = {p: 0 for p in self.providers}
    
    def get_next_provider(self) -> str:
        """Get next provider with lowest request count."""
        provider = min(self.request_counts, key=self.request_counts.get)
        self.request_counts[provider] += 1
        return provider
    
    def reset_counts(self):
        """Reset request counts (call hourly)."""
        self.request_counts = {p: 0 for p in self.providers}
```

### **5. Intelligent Prompt Optimization**

```python
# Optimize prompts to reduce token usage
def optimize_prompt(analysis_type: str, data: Dict) -> str:
    """Create efficient prompts that minimize tokens."""
    
    if analysis_type == "sentiment":
        # Use headlines only, not full articles
        headlines = [article["title"] for article in data["news"][:5]]
        return f"Analyze sentiment: {headlines}"
    
    elif analysis_type == "pattern":
        # Use key technical indicators only
        indicators = {
            "rsi": data["rsi"],
            "macd": data["macd_signal"],
            "volume": data["volume_ratio"]
        }
        return f"Pattern analysis: {indicators}"
    
    elif analysis_type == "prediction":
        # Focus on recent price action
        recent_data = data["price_data"].tail(20)
        return f"Predict next 24h: {recent_data}"
```

## 📊 **Usage Monitoring & Alerts**

### **Real-Time Monitoring Dashboard**

```python
# Monitor API usage in real-time
def monitor_api_usage():
    """Track API usage and provide alerts."""
    
    stats = {
        "openai": {"requests": 45, "limit": 60, "cost": 2.50},
        "anthropic": {"requests": 38, "limit": 50, "cost": 1.80},
        "google_ai": {"requests": 52, "limit": 60, "cost": 2.20}
    }
    
    for provider, data in stats.items():
        usage_pct = (data["requests"] / data["limit"]) * 100
        
        if usage_pct > 80:
            logger.warning(f"{provider}: {usage_pct:.1f}% of limit used")
        
        if usage_pct > 95:
            logger.critical(f"{provider}: CRITICAL - {usage_pct:.1f}% of limit")
            # Switch to cached results only
            enable_emergency_mode()
```

### **Cost Optimization Strategies**

```python
# Cost-effective analysis strategies
def optimize_for_cost(analysis_type: str, urgency: str):
    """Choose most cost-effective approach."""
    
    if urgency == "low":
        # Use cheaper models for non-critical analysis
        model_mapping = {
            "openai": "gpt-3.5-turbo",  # $0.002 vs $0.03 per 1K tokens
            "anthropic": "claude-instant",  # Cheaper than Claude-3
            "google_ai": "gemini-pro"  # Already cost-effective
        }
    else:
        # Use premium models for critical analysis
        model_mapping = {
            "openai": "gpt-4",
            "anthropic": "claude-3-sonnet", 
            "google_ai": "gemini-pro"
        }
    
    return model_mapping
```

## ⚙️ **Configuration Examples**

### **Conservative Configuration (Low Cost)**

```json
{
  "ai_analysis": {
    "enabled": true,
    "request_frequency": {
      "trading_hours": 1800,    // 30 minutes
      "off_hours": 7200         // 2 hours
    },
    "caching": {
      "enabled": true,
      "max_age_hours": 48,
      "force_cache_during_off_hours": true
    },
    "providers": {
      "openai": {
        "enabled": true,
        "model": "gpt-3.5-turbo",
        "max_tokens": 500,
        "temperature": 0.3
      },
      "anthropic": {
        "enabled": false,  // Disable to save costs
        "model": "claude-instant"
      },
      "google_ai": {
        "enabled": true,
        "model": "gemini-pro",
        "max_tokens": 500
      }
    },
    "analysis_types": {
      "sentiment": true,
      "pattern": true,
      "predictive": false,  // Disable expensive analysis
      "risk_assessment": false,
      "optimization": false
    }
  }
}
```

### **Aggressive Configuration (High Performance)**

```json
{
  "ai_analysis": {
    "enabled": true,
    "request_frequency": {
      "trading_hours": 300,     // 5 minutes
      "off_hours": 1800         // 30 minutes
    },
    "caching": {
      "enabled": true,
      "max_age_hours": 6,
      "force_cache_during_off_hours": false
    },
    "providers": {
      "openai": {
        "enabled": true,
        "model": "gpt-4",
        "max_tokens": 1000,
        "temperature": 0.3
      },
      "anthropic": {
        "enabled": true,
        "model": "claude-3-sonnet",
        "max_tokens": 1000
      },
      "google_ai": {
        "enabled": true,
        "model": "gemini-pro",
        "max_tokens": 1000
      }
    },
    "analysis_types": {
      "sentiment": true,
      "pattern": true,
      "predictive": true,
      "risk_assessment": true,
      "optimization": true
    }
  }
}
```

## 🎯 **Best Practices**

### **1. Start Conservative**
- Begin with 2 AI providers instead of 3
- Use longer cache durations (24-48 hours)
- Disable expensive analysis types initially
- Monitor usage for 1-2 weeks before scaling

### **2. Implement Smart Scheduling**
- **Trading Hours**: Higher frequency (5-15 minutes)
- **Extended Hours**: Medium frequency (30-60 minutes)  
- **Off Hours**: Low frequency (2-4 hours)
- **Weekends**: Minimal requests (6-12 hours)

### **3. Use Market Conditions**
- **High Volatility**: More frequent analysis
- **Low Volatility**: Longer cache durations
- **News Events**: Real-time sentiment analysis
- **Earnings**: Disable predictive analysis (unreliable)

### **4. Monitor and Optimize**
- Track hit rates (aim for >70%)
- Monitor costs per analysis
- Adjust cache durations based on accuracy
- Rotate providers based on performance

### **5. Emergency Procedures**
- **Rate limit hit**: Switch to cached results only
- **High costs**: Disable non-critical analysis
- **Provider down**: Fallback to remaining providers
- **System overload**: Implement request queuing

## 💰 **Cost Estimation**

### **Monthly Cost Examples**

**Conservative Setup (2 providers, limited analysis):**
- 100 requests/day × 30 days = 3,000 requests/month
- Average cost: $0.05/request
- **Total: ~$150/month**

**Moderate Setup (3 providers, full analysis):**
- 300 requests/day × 30 days = 9,000 requests/month  
- Average cost: $0.08/request
- **Total: ~$720/month**

**Aggressive Setup (3 providers, high frequency):**
- 1,000 requests/day × 30 days = 30,000 requests/month
- Average cost: $0.10/request
- **Total: ~$3,000/month**

### **Cost Reduction Strategies**

1. **Caching**: 80-90% reduction in API calls
2. **Provider rotation**: Distribute load across providers
3. **Smart scheduling**: Reduce off-hours requests
4. **Model selection**: Use cheaper models for non-critical analysis
5. **Prompt optimization**: Reduce token usage by 50-70%

## 🚀 **Implementation Commands**

```bash
# Test API limits and caching
python -c "from src.services.ai_cache import IntelligentRequestManager; print('Cache system ready')"

# Monitor API usage
python -c "from src.services.ai_advisor import AIAdvisor; ai = AIAdvisor(config); print(ai.get_usage_stats())"

# Run with conservative settings
python scripts/demo_ai.py --conservative

# Run with aggressive settings  
python scripts/demo_ai.py --aggressive

# Test cache effectiveness
python -c "from src.services.ai_cache import AICache; cache = AICache(); print(cache.get_stats())"
```

## 📈 **Performance Monitoring**

### **Key Metrics to Track**

1. **Cache Hit Rate**: Target >70%
2. **API Request Frequency**: Monitor per provider
3. **Cost per Analysis**: Track and optimize
4. **Response Quality**: Compare cached vs. fresh results
5. **Rate Limit Proximity**: Stay under 80% of limits

### **Alert Thresholds**

- **Cache hit rate < 50%**: Increase cache duration
- **API usage > 80%**: Enable emergency mode
- **Cost > $500/month**: Review analysis frequency
- **Provider errors > 5%**: Switch to backup providers

This comprehensive approach ensures you can leverage AI analysis while managing costs and avoiding rate limits effectively. 