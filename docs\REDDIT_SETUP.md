# 🔴 Reddit API Setup Guide

This guide explains how to set up Reddit API credentials for sentiment analysis in AI-Nvestor.

## 🎯 **Why Use Reddit API?**

Reddit is a valuable source of real-time sentiment data for trading symbols. The platform provides:
- **Real-time discussions** about stocks and trading
- **Community sentiment** from various subreddits
- **High-quality content** with upvotes/downvotes
- **Diverse perspectives** from retail investors to professionals

## 📋 **Prerequisites**

1. **Reddit Account**: You need a Reddit account (free)
2. **Python Package**: Install `praw` package: `pip install praw` (optional, for better results)
3. **Reddit App**: Create a Reddit application (see steps below)

## 🚀 **Step-by-Step Setup**

### **Step 1: Create a Reddit Application**

1. **Go to Reddit App Preferences**:
   - Visit: https://www.reddit.com/prefs/apps
   - Sign in to your Reddit account

2. **Create a New App**:
   - Click **"Create App"** or **"Create Another App"**
   - Fill in the following details:
     - **Name**: `AI-Nvestor Sentiment Analysis`
     - **App Type**: Select **"script"**
     - **Description**: `AI-powered trading sentiment analysis`
     - **About URL**: Leave blank or add your GitHub repo
     - **Redirect URI**: `http://localhost:8080`

3. **Save Your Credentials**:
   - After creating, you'll see your app details
   - **Note down**:
     - **Client ID** (under the app name)
     - **Client Secret** (labeled "secret")

### **Step 2: Configure Environment Variables**

1. **Copy Environment Template**:
   ```bash
   cp env.example .env
   ```

2. **Add Reddit Credentials**:
   ```bash
   # Reddit API Credentials (for sentiment analysis)
   REDDIT_CLIENT_ID=your_client_id_here
   REDDIT_CLIENT_SECRET=your_client_secret_here
   REDDIT_USERNAME=your_reddit_username
   REDDIT_PASSWORD=your_reddit_password
   ```

3. **Replace with Your Values**:
   - `your_client_id_here`: The client ID from Step 1
   - `your_client_secret_here`: The client secret from Step 1
   - `your_reddit_username`: Your Reddit username
   - `your_reddit_password`: Your Reddit password

### **Step 3: Test the Setup**

1. **Run the Test Script**:
   ```bash
   python tests/integration/test_reddit_sentiment.py
   ```

2. **Expected Output**:
   ```
   🔴 Testing Reddit Sentiment Analysis (Integration Test)
   ============================================================
   ✅ Reddit API client initialized successfully (praw)
   📊 Testing Reddit sentiment for 3 symbols...
   
   🔍 Analyzing AAPL on Reddit...
   ✅ Found 75 Reddit sentiment data points
   📝 Posts: 65
   💬 Comments: 10
   ```

## 🔧 **Configuration Options**

### **Subreddits Configuration**

You can customize which subreddits to monitor in `config.json`:

```json
{
  "sentiment_data": {
    "reddit": {
      "subreddits": [
        "stocks",
        "investing", 
        "wallstreetbets",
        "stockmarket",
        "options",
        "daytrading",
        "investments",
        "financialindependence"
      ]
    }
  }
}
```

### **Rate Limiting and Limits**

```json
{
  "sentiment_data": {
    "reddit": {
      "max_posts_per_subreddit": 50,
      "max_comments_per_post": 20,
      "min_score_threshold": 5
    }
  }
}
```

## 🚨 **Important Notes**

### **Rate Limiting**
- **praw (Authenticated)**: Higher rate limits (60 requests per minute)
- **JSON API (Unauthenticated)**: Lower rate limits (30 requests per minute)
- The service automatically handles rate limiting
- Respect Reddit's terms of service

### **Privacy and Security**
- **Never commit** your Reddit credentials to version control
- Use environment variables for sensitive data
- Consider using a dedicated Reddit account for the bot

### **API Limits**
- Reddit API is free but has usage limits
- Monitor your usage to avoid hitting limits
- Consider implementing caching for better performance

## 🐛 **Troubleshooting**

### **Common Issues**

1. **"praw package not found"**:
   ```bash
   pip install praw
   ```
   - **Note**: This is optional. The service will use JSON API fallback if `praw` is not installed.

2. **"Failed to initialize Reddit API client"**:
   - Check your credentials in `.env` file
   - Verify your Reddit app is set to "script" type
   - Ensure your Reddit account is active
   - **Note**: The service will automatically fall back to JSON API if this fails.

3. **"No Reddit sentiment data available"**:
   - Check if the symbol is mentioned in monitored subreddits
   - Verify subreddit names are correct
   - Check if posts meet the minimum score threshold
   - **Note**: This can happen due to rate limiting. Try again later.

4. **"429 Too Many Requests"**:
   - This is expected behavior when using JSON API
   - The service automatically handles rate limiting
   - Consider setting up Reddit credentials for higher limits

### **Debug Mode**

Enable debug logging to see detailed API interactions:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📊 **Usage Examples**

### **Basic Sentiment Analysis**

```python
from services.sentiment_data import RedditSentimentService

# Initialize service
reddit_service = RedditSentimentService(config)

# Get sentiment for a symbol
sentiment_data = await reddit_service.get_reddit_sentiment("AAPL", hours=24)

# Analyze results
for item in sentiment_data:
    print(f"Sentiment: {item.sentiment_score:.3f}")
    print(f"Text: {item.text[:100]}...")
    print(f"Source: r/{item.metadata['subreddit']}")
```

### **Integration with Main Service**

```python
from services.sentiment_data import SentimentDataService

# Initialize main service (includes Reddit)
sentiment_service = SentimentDataService(config)

# Get all sentiment data (Reddit + other sources)
sentiment_data = await sentiment_service.get_sentiment_data("AAPL", hours=24)

# Get aggregated metrics
aggregated = sentiment_service.get_aggregated_sentiment(sentiment_data)
print(f"Overall Sentiment: {aggregated['overall_sentiment']:.3f}")
```

## 🎯 **Next Steps**

1. **Test with Different Symbols**: Try popular stocks like AAPL, TSLA, NVDA
2. **Customize Subreddits**: Add or remove subreddits based on your needs
3. **Fine-tune Parameters**: Adjust score thresholds and limits
4. **Monitor Performance**: Check logs for any issues or rate limiting
5. **Set Up Credentials**: For better results, configure Reddit API credentials

## 📚 **Additional Resources**

- [Reddit API Documentation](https://www.reddit.com/dev/api/)
- [PRAW Documentation](https://praw.readthedocs.io/)
- [Reddit App Creation Guide](https://github.com/reddit-archive/reddit/wiki/OAuth2)
- [Reddit API Terms of Service](https://www.redditinc.com/policies/api-terms)

---

**Happy Trading! 🚀** 