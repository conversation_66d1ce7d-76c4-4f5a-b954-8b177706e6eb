# AI-Nvestor Environment Variables
# Copy this file to .env and fill in your actual API keys

# Data Source API Keys
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key_here
POLYGON_API_KEY=your_polygon_key_here
QUANDL_API_KEY=your_quandl_key_here

# Sentiment Data API Keys
NEWS_API_KEY=your_news_api_key_here

# Reddit API Credentials (for sentiment analysis)
REDDIT_CLIENT_ID=your_reddit_client_id_here
REDDIT_CLIENT_SECRET=your_reddit_client_secret_here
REDDIT_USERNAME=your_reddit_username_here
REDDIT_PASSWORD=your_reddit_password_here

# Apify API Credentials (for historical Reddit scraping)
APIFY_API_TOKEN=your_apify_api_token_here
APIFY_REDDIT_SCRAPER_ID=your_apify_reddit_scraper_id_here

# AI Provider API Keys
OPENAI_API_KEY=your_openai_key_here
ANTHROPIC_API_KEY=your_anthropic_key_here
GOOGLE_AI_API_KEY=your_google_ai_key_here
COHERE_API_KEY=your_cohere_key_here
HUGGINGFACE_API_KEY=your_huggingface_key_here

# Trading Configuration
TRADING_PAPER_TRADING=true
TRADING_AUTO_TRADING=false
TRADING_REAL_TIME=true

# Logging Configuration
LOGGING_LEVEL=INFO

# AI Analysis Configuration
AI_ANALYSIS_ENABLED=false

# Risk Management Configuration
RISK_MAX_POSITION_SIZE=0.02
RISK_STOP_LOSS=0.05 