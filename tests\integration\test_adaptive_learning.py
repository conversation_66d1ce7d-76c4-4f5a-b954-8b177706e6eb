#!/usr/bin/env python3
"""
Integration tests for Adaptive Learning Service.

This module tests the adaptive learning functionality for variable weights and significance.
"""

import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent.parent.parent / "src"))

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from services.adaptive_learning import AdaptiveLearningService, LearningMode, WeightCategory, VariableWeight
from services.sentiment_data import SentimentData, SentimentSource
from services.indicators import IndicatorResult, SignalType
from utils.config import setup_config


class TestAdaptiveLearning:
    """Test class for adaptive learning functionality."""
    
    @pytest.fixture
    async def adaptive_learning_service(self):
        """Fixture to create adaptive learning service."""
        config_manager = setup_config("config.json")
        config = config_manager.get_config()
        config_dict = config.model_dump()
        
        service = AdaptiveLearningService(config_dict)
        yield service
        await service.close()
    
    @pytest.fixture
    def sample_data(self):
        """Fixture to create sample OHLCV data."""
        dates = pd.date_range(start='2024-01-01', end='2024-01-31', freq='D')
        data = pd.DataFrame({
            'Open': np.random.uniform(100, 200, len(dates)),
            'High': np.random.uniform(150, 250, len(dates)),
            'Low': np.random.uniform(50, 150, len(dates)),
            'Close': np.random.uniform(100, 200, len(dates)),
            'Volume': np.random.uniform(1000000, 5000000, len(dates))
        }, index=dates)
        return data
    
    @pytest.fixture
    def sample_sentiment_data(self):
        """Fixture to create sample sentiment data."""
        sentiment_data = [
            SentimentData(
                symbol="AAPL",
                source=SentimentSource.REDDIT,
                sentiment_score=0.8,
                confidence=0.9,
                timestamp=datetime.now(),
                text="AAPL is going to the moon!",
                metadata={'type': 'post', 'subreddit': 'stocks', 'score': 100}
            ),
            SentimentData(
                symbol="AAPL",
                source=SentimentSource.REDDIT,
                sentiment_score=-0.3,
                confidence=0.7,
                timestamp=datetime.now(),
                text="AAPL might be overvalued",
                metadata={'type': 'comment', 'subreddit': 'investing', 'score': 50}
            ),
            SentimentData(
                symbol="AAPL",
                source=SentimentSource.REDDIT,
                sentiment_score=0.5,
                confidence=0.8,
                timestamp=datetime.now(),
                text="AAPL has strong fundamentals",
                metadata={'type': 'post', 'subreddit': 'stockmarket', 'score': 75}
            )
        ]
        return sentiment_data
    
    @pytest.fixture
    def sample_technical_indicators(self):
        """Fixture to create sample technical indicators."""
        indicators = {
            'sma': IndicatorResult(
                value=150.0,
                signal=SignalType.BUY,
                confidence=0.8,
                metadata={'period': 20}
            ),
            'rsi': IndicatorResult(
                value=65.0,
                signal=SignalType.HOLD,
                confidence=0.6,
                metadata={'period': 14}
            ),
            'macd': IndicatorResult(
                value=2.5,
                signal=SignalType.BUY,
                confidence=0.7,
                metadata={'fast': 12, 'slow': 26}
            ),
            'bollinger': IndicatorResult(
                value=0.8,
                signal=SignalType.SELL,
                confidence=0.5,
                metadata={'period': 20, 'std': 2}
            )
        }
        return indicators
    
    @pytest.mark.asyncio
    async def test_adaptive_learning_initialization(self, adaptive_learning_service):
        """Test adaptive learning service initialization."""
        assert adaptive_learning_service is not None
        assert hasattr(adaptive_learning_service, 'variable_weights')
        assert len(adaptive_learning_service.variable_weights) > 0
        
        # Check sentiment weights
        sentiment_weights = [w for w in adaptive_learning_service.variable_weights.values() 
                           if w.category == WeightCategory.SENTIMENT]
        assert len(sentiment_weights) > 0
        
        # Check technical weights
        technical_weights = [w for w in adaptive_learning_service.variable_weights.values() 
                           if w.category == WeightCategory.TECHNICAL]
        assert len(technical_weights) > 0
    
    @pytest.mark.asyncio
    async def test_adaptive_learning_with_sentiment_data(self, adaptive_learning_service, sample_data, sample_sentiment_data):
        """Test adaptive learning with sentiment data."""
        symbol = "AAPL"
        
        # Test learning with sentiment data
        learning_result = await adaptive_learning_service.learn_from_data(
            symbol=symbol,
            data=sample_data,
            sentiment_data=sample_sentiment_data
        )
        
        assert learning_result is not None
        assert learning_result.symbol == symbol
        assert learning_result.learning_mode == LearningMode.HYBRID
        assert len(learning_result.weights) > 0
        
        # Check that sentiment weights were updated
        sentiment_weights = adaptive_learning_service.get_optimized_weights(symbol, WeightCategory.SENTIMENT)
        assert len(sentiment_weights) > 0
        
        # Check feature importance
        feature_importance = adaptive_learning_service.get_feature_importance(symbol)
        assert isinstance(feature_importance, dict)
    
    @pytest.mark.asyncio
    async def test_adaptive_learning_with_technical_indicators(self, adaptive_learning_service, sample_data, sample_technical_indicators):
        """Test adaptive learning with technical indicators."""
        symbol = "TSLA"
        
        # Test learning with technical indicators
        learning_result = await adaptive_learning_service.learn_from_data(
            symbol=symbol,
            data=sample_data,
            technical_indicators=sample_technical_indicators
        )
        
        assert learning_result is not None
        assert learning_result.symbol == symbol
        assert learning_result.learning_mode == LearningMode.HYBRID
        
        # Check that technical weights were updated
        technical_weights = adaptive_learning_service.get_optimized_weights(symbol, WeightCategory.TECHNICAL)
        assert len(technical_weights) > 0
    
    @pytest.mark.asyncio
    async def test_adaptive_learning_hybrid(self, adaptive_learning_service, sample_data, sample_sentiment_data, sample_technical_indicators):
        """Test adaptive learning with both sentiment and technical data."""
        symbol = "NVDA"
        
        # Test hybrid learning
        learning_result = await adaptive_learning_service.learn_from_data(
            symbol=symbol,
            data=sample_data,
            sentiment_data=sample_sentiment_data,
            technical_indicators=sample_technical_indicators
        )
        
        assert learning_result is not None
        assert learning_result.symbol == symbol
        assert learning_result.learning_mode == LearningMode.HYBRID
        
        # Check that both sentiment and technical weights were updated
        sentiment_weights = adaptive_learning_service.get_optimized_weights(symbol, WeightCategory.SENTIMENT)
        technical_weights = adaptive_learning_service.get_optimized_weights(symbol, WeightCategory.TECHNICAL)
        
        assert len(sentiment_weights) > 0
        assert len(technical_weights) > 0
        
        # Check performance metrics
        performance_metrics = adaptive_learning_service.get_performance_metrics(symbol)
        assert isinstance(performance_metrics, dict)
    
    @pytest.mark.asyncio
    async def test_weight_optimization(self, adaptive_learning_service, sample_data, sample_sentiment_data):
        """Test weight optimization functionality."""
        symbol = "MSFT"
        
        # Initial weights
        initial_weights = adaptive_learning_service.get_optimized_weights(symbol)
        
        # Learn from data
        learning_result = await adaptive_learning_service.learn_from_data(
            symbol=symbol,
            data=sample_data,
            sentiment_data=sample_sentiment_data
        )
        
        # Updated weights
        updated_weights = adaptive_learning_service.get_optimized_weights(symbol)
        
        # Check that weights were updated
        assert len(updated_weights) > 0
        
        # Check that some weights changed (learning occurred)
        weight_changes = 0
        for weight_name in updated_weights:
            if weight_name in initial_weights:
                if abs(updated_weights[weight_name] - initial_weights[weight_name]) > 0.001:
                    weight_changes += 1
        
        # At least some weights should have changed
        assert weight_changes > 0
    
    @pytest.mark.asyncio
    async def test_feature_importance_analysis(self, adaptive_learning_service, sample_data, sample_sentiment_data, sample_technical_indicators):
        """Test feature importance analysis."""
        symbol = "GOOGL"
        
        # Learn from data
        learning_result = await adaptive_learning_service.learn_from_data(
            symbol=symbol,
            data=sample_data,
            sentiment_data=sample_sentiment_data,
            technical_indicators=sample_technical_indicators
        )
        
        # Get feature importance
        feature_importance = adaptive_learning_service.get_feature_importance(symbol)
        
        assert isinstance(feature_importance, dict)
        
        # Check that feature importance contains expected keys
        if feature_importance:
            # Should have feature importance for different models
            assert any('sentiment_model' in key or 'technical_model' in key or 'hybrid_model' in key 
                      for key in feature_importance.keys())
    
    @pytest.mark.asyncio
    async def test_learning_result_storage(self, adaptive_learning_service, sample_data, sample_sentiment_data):
        """Test that learning results are properly stored."""
        symbol = "AMZN"
        
        # Learn from data
        learning_result = await adaptive_learning_service.learn_from_data(
            symbol=symbol,
            data=sample_data,
            sentiment_data=sample_sentiment_data
        )
        
        # Check that learning result was stored
        assert learning_result is not None
        assert learning_result.symbol == symbol
        
        # Check that performance history was updated
        performance_metrics = adaptive_learning_service.get_performance_metrics(symbol)
        assert isinstance(performance_metrics, dict)
        
        # Check that feature importance history was updated
        feature_importance = adaptive_learning_service.get_feature_importance(symbol)
        assert isinstance(feature_importance, dict)


async def test_adaptive_learning_manual():
    """Manual test function for adaptive learning."""
    print("🧠 Testing Adaptive Learning Service")
    print("=" * 50)
    
    # Load configuration
    config_manager = setup_config("config.json")
    config = config_manager.get_config()
    config_dict = config.model_dump()
    
    # Initialize adaptive learning service
    adaptive_learning = AdaptiveLearningService(config_dict)
    
    # Create sample data
    dates = pd.date_range(start='2024-01-01', end='2024-01-31', freq='D')
    sample_data = pd.DataFrame({
        'Open': np.random.uniform(100, 200, len(dates)),
        'High': np.random.uniform(150, 250, len(dates)),
        'Low': np.random.uniform(50, 150, len(dates)),
        'Close': np.random.uniform(100, 200, len(dates)),
        'Volume': np.random.uniform(1000000, 5000000, len(dates))
    }, index=dates)
    
    # Create sample sentiment data
    sentiment_data = [
        SentimentData(
            symbol="AAPL",
            source=SentimentSource.REDDIT,
            sentiment_score=0.8,
            confidence=0.9,
            timestamp=datetime.now(),
            text="AAPL is going to the moon!",
            metadata={'type': 'post', 'subreddit': 'stocks', 'score': 100}
        ),
        SentimentData(
            symbol="AAPL",
            source=SentimentSource.REDDIT,
            sentiment_score=-0.3,
            confidence=0.7,
            timestamp=datetime.now(),
            text="AAPL might be overvalued",
            metadata={'type': 'comment', 'subreddit': 'investing', 'score': 50}
        )
    ]
    
    # Create sample technical indicators
    technical_indicators = {
        'sma': IndicatorResult(
            value=150.0,
            signal=SignalType.BUY,
            confidence=0.8,
            metadata={'period': 20}
        ),
        'rsi': IndicatorResult(
            value=65.0,
            signal=SignalType.HOLD,
            confidence=0.6,
            metadata={'period': 14}
        )
    }
    
    print(f"📊 Testing adaptive learning with sample data...")
    print(f"   Data shape: {sample_data.shape}")
    print(f"   Sentiment data points: {len(sentiment_data)}")
    print(f"   Technical indicators: {len(technical_indicators)}")
    
    # Test learning
    learning_result = await adaptive_learning.learn_from_data(
        symbol="AAPL",
        data=sample_data,
        sentiment_data=sentiment_data,
        technical_indicators=technical_indicators
    )
    
    if learning_result:
        print(f"✅ Adaptive learning completed successfully!")
        print(f"   Model accuracy: {learning_result.model_accuracy:.3f}")
        print(f"   Learning mode: {learning_result.learning_mode.value}")
        print(f"   Weights updated: {len(learning_result.weights)}")
        
        # Show optimized weights
        optimized_weights = adaptive_learning.get_optimized_weights("AAPL")
        print(f"\n📊 Optimized Weights:")
        for weight_name, weight_value in optimized_weights.items():
            print(f"   {weight_name}: {weight_value:.3f}")
        
        # Show feature importance
        feature_importance = adaptive_learning.get_feature_importance("AAPL")
        if feature_importance:
            print(f"\n🎯 Feature Importance:")
            for model_name, importance_dict in feature_importance.items():
                print(f"   {model_name}:")
                for feature, importance in sorted(importance_dict.items(), key=lambda x: x[1], reverse=True)[:5]:
                    print(f"     {feature}: {importance:.3f}")
        
        # Show performance metrics
        performance_metrics = adaptive_learning.get_performance_metrics("AAPL")
        if performance_metrics:
            print(f"\n📈 Performance Metrics:")
            for model_name, metrics in performance_metrics.items():
                print(f"   {model_name}:")
                for metric_name, metric_value in metrics.items():
                    print(f"     {metric_name}: {metric_value:.3f}")
    else:
        print(f"❌ Adaptive learning failed")
    
    # Cleanup
    await adaptive_learning.close()
    print(f"\n✅ Adaptive learning test completed!")


if __name__ == "__main__":
    asyncio.run(test_adaptive_learning_manual()) 