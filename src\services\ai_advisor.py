"""
AI Advisor service for AI-Nvestor.

This module provides:
- AI-powered market analysis using multiple providers
- Sentiment analysis of news and social media
- Pattern recognition in price data
- Predictive analytics for price movements
- Portfolio optimization recommendations
- Risk assessment using AI models
"""

import pandas as pd
import numpy as np
import json
import httpx
import asyncio
import uuid
import time
import random
from typing import Dict, List, Optional, Any
from datetime import datetime
from dataclasses import dataclass
from enum import Enum
import os

from utils.logging import get_logger
from utils.ai_logger import ai_logger
from core.exceptions import TradingPlatformError


class AIAnalysisType(Enum):
    """Types of AI analysis."""
    SENTIMENT = "sentiment"
    PATTERN = "pattern"
    PREDICTIVE = "predictive"
    OPTIMIZATION = "optimization"
    RISK_ASSESSMENT = "risk_assessment"


@dataclass
class AIAnalysisResult:
    """Result of AI analysis."""
    analysis_type: AIAnalysisType
    confidence: Optional[float]  # Can be None for undefined confidence
    recommendation: str
    reasoning: str
    provider: str
    model: str
    metadata: Dict[str, Any]


class AIProvider:
    """Base class for AI providers."""
    
    def __init__(self, config: Dict, provider_name: str, client: httpx.AsyncClient):
        self.config = config
        self.provider_name = provider_name
        self.client = client
        
        ai_config = config.get('ai_analysis', {})
        
        tier_1_config = ai_config.get('tier_1_providers', {}).get(provider_name, {})
        tier_2_config = ai_config.get('tier_2_providers', {}).get(provider_name, {})
        
        if tier_1_config:
            self.provider_config = tier_1_config
        elif tier_2_config:
            self.provider_config = tier_2_config
        else:
            self.provider_config = ai_config.get('providers', {}).get(provider_name, {})
        
        self.api_key = config.get('api_keys', {}).get(provider_name, '')
        self.enabled = self.provider_config.get('enabled', False)
        self.model = self.provider_config.get('model', '')

        self.max_tokens = self.provider_config.get('max_tokens', 1000)
        self.temperature = self.provider_config.get('temperature', 0.3)
        self.logger = get_logger()
        
        # Retry and rate limiting configuration
        self.max_retries = self.provider_config.get('max_retries', 3)
        self.retry_delay = self.provider_config.get('retry_delay', 1.0)
        self.max_retry_delay = self.provider_config.get('max_retry_delay', 10.0)
        self.rate_limit_delay = self.provider_config.get('rate_limit_delay', 0.5)
        self.last_request_time = 0.0
    
    def is_available(self) -> bool:
        """Check if provider is available."""
        return self.enabled and bool(self.api_key)
    
    async def _rate_limit(self):
        """Implement rate limiting to avoid hitting API limits."""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        if time_since_last < self.rate_limit_delay:
            sleep_time = self.rate_limit_delay - time_since_last
            self.logger.debug(f"🔧 Rate limiting {self.provider_name}: sleeping for {sleep_time:.2f}s")
            await asyncio.sleep(sleep_time)
        self.last_request_time = time.time()
    
    async def _retry_with_backoff(self, func, *args, **kwargs):
        """Retry a function with exponential backoff."""
        last_exception = None
        
        for attempt in range(self.max_retries):
            try:
                await self._rate_limit()
                return await func(*args, **kwargs)
            except Exception as e:
                last_exception = e
                self.logger.warning(f"⚠️ {self.provider_name} attempt {attempt + 1}/{self.max_retries} failed: {e}")
                
                if attempt < self.max_retries - 1:
                    # Exponential backoff with jitter
                    delay = min(self.retry_delay * (2 ** attempt) + random.uniform(0, 1), self.max_retry_delay)
                    self.logger.info(f"🔄 Retrying {self.provider_name} in {delay:.2f}s...")
                    await asyncio.sleep(delay)
        
        self.logger.error(f"❌ {self.provider_name} failed after {self.max_retries} attempts: {last_exception}")
        return None
    
    async def analyze(self, prompt: str, context: Dict[str, Any] = None) -> Optional[AIAnalysisResult]:
        """Analyze using the AI provider with retry logic."""
        return await self._retry_with_backoff(self._analyze_impl, prompt, context)
    
    async def _analyze_impl(self, prompt: str, context: Dict[str, Any] = None) -> Optional[AIAnalysisResult]:
        """Implementation of analyze method - to be overridden by subclasses."""
        raise NotImplementedError
    
    async def analyze_with_logging(self, prompt: str, context: Dict[str, Any] = None, analysis_log = None) -> Optional[AIAnalysisResult]:
        """Analyze with logging support."""
        result = await self.analyze(prompt, context)
        if result and analysis_log:
            # Convert AIAnalysisResult to dict for logging
            response_data = {
                "recommendation": result.recommendation,
                "confidence": result.confidence,
                "reasoning": result.reasoning,
                "provider": result.provider,
                "model": result.model,
                "metadata": result.metadata
            }
            # Use the global ai_logger instance
            from utils.ai_logger import ai_logger
            ai_logger.log_ai_response(analysis_log, self.provider_name, response_data)
        return result


class OpenAIProvider(AIProvider):
    """OpenAI GPT integration."""
    
    def __init__(self, config: Dict, client: httpx.AsyncClient):
        super().__init__(config, 'openai', client)
        self.base_url = "https://api.openai.com/v1/chat/completions"
    
    async def _analyze_impl(self, prompt: str, context: Dict[str, Any] = None) -> Optional[AIAnalysisResult]:
        """Analyze using OpenAI GPT."""
        if not self.is_available():
            return None
        
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            system_message = """You are an expert financial analyst and trading advisor. 
            Analyze the provided market data and give concise, actionable trading advice."""
            
            user_message = f"Context: {json.dumps(context) if context else 'No additional context'}\n\nAnalysis Request: {prompt}"
            
            data = {
                "model": self.model or "gpt-4o-mini",
                "messages": [
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": user_message}
                ],
                "max_tokens": self.max_tokens,
                "temperature": self.temperature
            }
            
            response = await self.client.post(self.base_url, headers=headers, json=data)
            response.raise_for_status()
            
            result = response.json()
            content = result['choices'][0]['message']['content']
            
            recommendation, confidence, reasoning = self._parse_response(content)
            
            return AIAnalysisResult(
                analysis_type=AIAnalysisType.SENTIMENT,
                confidence=confidence,
                recommendation=recommendation,
                reasoning=reasoning,
                provider="openai",
                model=self.model,
                metadata={"response": content, "context": context}
            )
            
        except Exception as e:
            self.logger.error(f"OpenAI analysis error: {e}")
            return None
    
    def _parse_response(self, content: str) -> tuple[str, float, str]:
        content_lower = content.lower()
        if any(phrase in content_lower for phrase in ['strong buy', 'very bullish']):
            return "STRONG_BUY", 0.95, content
        if any(phrase in content_lower for phrase in ['strong sell', 'very bearish']):
            return "STRONG_SELL", 0.95, content
        if 'buy' in content_lower:
            return "BUY", 0.8, content
        if 'sell' in content_lower:
            return "SELL", 0.8, content
        return "HOLD", 0.6, content


class AnthropicProvider(AIProvider):
    """Anthropic Claude integration."""
    
    def __init__(self, config: Dict, client: httpx.AsyncClient):
        super().__init__(config, 'anthropic', client)
        self.base_url = "https://api.anthropic.com/v1/messages"
    
    async def _analyze_impl(self, prompt: str, context: Dict[str, Any] = None) -> Optional[AIAnalysisResult]:
        """Analyze using Anthropic Claude."""
        if not self.is_available():
            return None
        
        try:
            headers = {
                "x-api-key": self.api_key,
                "Content-Type": "application/json",
                "anthropic-version": "2023-06-01"
            }
            
            system_message = "You are an expert financial analyst."
            user_message = f"Context: {json.dumps(context) if context else 'No context'}\n\nRequest: {prompt}"
            
            data = {
                "model": self.model or "claude-3-haiku-20240307",
                "max_tokens": self.max_tokens,
                "messages": [{"role": "user", "content": user_message}],
                "system": system_message
            }
            
            response = await self.client.post(self.base_url, headers=headers, json=data)
            response.raise_for_status()
            
            result = response.json()
            content = result['content'][0]['text']
            
            recommendation, confidence, reasoning = self._parse_response(content)
            
            return AIAnalysisResult(
                analysis_type=AIAnalysisType.SENTIMENT,
                confidence=confidence,
                recommendation=recommendation,
                reasoning=reasoning,
                provider="anthropic",
                model=self.model,
                metadata={"response": content, "context": context}
            )
            
        except Exception as e:
            self.logger.error(f"Anthropic analysis error: {e}")
            return None
    
    def _parse_response(self, content: str) -> tuple[str, float, str]:
        content_lower = content.lower()
        if any(phrase in content_lower for phrase in ['strong buy', 'very bullish']):
            return "STRONG_BUY", 0.95, content
        if any(phrase in content_lower for phrase in ['strong sell', 'very bearish']):
            return "STRONG_SELL", 0.95, content
        if 'buy' in content_lower:
            return "BUY", 0.8, content
        if 'sell' in content_lower:
            return "SELL", 0.8, content
        return "HOLD", 0.6, content

class GroqProvider(AIProvider):
    def __init__(self, config: Dict, client: httpx.AsyncClient):
        super().__init__(config, 'groq', client)
        self.base_url = "https://api.groq.com/openai/v1/chat/completions"
    
    async def _analyze_impl(self, prompt: str, context: Dict[str, Any] = None) -> Optional[AIAnalysisResult]:
        if not self.is_available():
            self.logger.warning(f"⚠️ Groq provider not available (enabled: {self.enabled}, api_key: {'set' if self.api_key else 'not set'})")
            return None
        
        self.logger.info(f"🔧 Groq: Starting analysis for {context.get('symbol', 'unknown')}")
        self.logger.info(f"🔧 Groq: Model: {self.model}, Max tokens: {self.max_tokens}")
        
        try:
            import time
            start_time = time.time()
            
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            system_message = """You are an expert financial analyst and trading advisor. 
            Analyze the provided market data and give concise, actionable trading advice.
            Respond with a clear recommendation (BUY, SELL, HOLD, STRONG_BUY, STRONG_SELL) 
            and explain your reasoning based on technical analysis."""
            
            user_message = f"Context: {json.dumps(context) if context else 'No additional context'}\n\nAnalysis Request: {prompt}"
            
            data = {
                "model": self.model or "llama3-8b-8192",
                "messages": [
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": user_message}
                ],
                "max_tokens": self.max_tokens,
                "temperature": self.temperature
            }
            
            self.logger.info(f"🔧 Groq: Making API request to {self.base_url}")
            self.logger.info(f"🔧 Groq: Request data keys: {list(data.keys())}")
            
            response = await self.client.post(self.base_url, headers=headers, json=data)
            response_time = time.time() - start_time
            
            self.logger.info(f"🔧 Groq: Response status: {response.status_code}")
            self.logger.info(f"🔧 Groq: Response time: {response_time:.2f}s")
            
            response.raise_for_status()
            
            result = response.json()
            self.logger.info(f"🔧 Groq: Response keys: {list(result.keys())}")
            
            content = result['choices'][0]['message']['content']
            self.logger.info(f"🔧 Groq: Response content length: {len(content)}")
            self.logger.info(f"🔧 Groq: Response preview: {content[:200]}...")
            
            recommendation, confidence, reasoning = self._parse_response(content)
            self.logger.info(f"✅ Groq: Parsed result - {recommendation} (conf: {confidence:.2f})")
            
            # Store detailed HTTP information in metadata
            http_details = {
                "endpoint": self.base_url,
                "method": "POST",
                "status_code": response.status_code,
                "response_time": response_time,
                "request_headers": dict(headers),
                "request_data": data,
                "response_headers": dict(response.headers),
                "response_data": result,
                "raw_response": content
            }
            
            return AIAnalysisResult(
                analysis_type=AIAnalysisType.PATTERN,
                confidence=confidence,
                recommendation=recommendation,
                reasoning=reasoning,
                provider="groq",
                model=self.model,
                metadata={"response": content, "context": context, "http_details": http_details}
            )
            
        except Exception as e:
            self.logger.error(f"❌ Groq analysis error: {e}")
            self.logger.error(f"❌ Groq error type: {type(e).__name__}")
            import traceback
            self.logger.error(f"❌ Groq traceback: {traceback.format_exc()}")
            return None
    
    def _parse_response(self, content: str) -> tuple[str, float, str]:
        content_lower = content.lower()
        if any(phrase in content_lower for phrase in ['strong buy', 'very bullish']):
            return "STRONG_BUY", 0.95, content
        if any(phrase in content_lower for phrase in ['strong sell', 'very bearish']):
            return "STRONG_SELL", 0.95, content
        if 'buy' in content_lower:
            return "BUY", 0.8, content
        if 'sell' in content_lower:
            return "SELL", 0.8, content
        return "HOLD", 0.6, content

class HuggingFaceProvider(AIProvider):
    def __init__(self, config: Dict, client: httpx.AsyncClient):
        super().__init__(config, 'huggingface', client)
        self.base_url = "https://api-inference.huggingface.co/models"
    
    async def _analyze_impl(self, prompt: str, context: Dict[str, Any] = None) -> Optional[AIAnalysisResult]:
        if not self.is_available():
            return None
        
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            # Format the prompt for the model
            formatted_prompt = f"""You are an expert financial analyst. Analyze this trading scenario:

Context: {json.dumps(context) if context else 'No additional context'}
Request: {prompt}

Provide a clear recommendation (BUY, SELL, HOLD, STRONG_BUY, STRONG_SELL) and explain your reasoning."""

            data = {
                "inputs": formatted_prompt,
                "parameters": {
                    "max_new_tokens": self.max_tokens,
                    "temperature": self.temperature,
                    "do_sample": True,
                    "return_full_text": False
                }
            }
            
            # Use a more reliable model that's publicly available
            model_name = self.model or "openai-community/gpt2"  # Use GPT-2 as fallback since it's publicly available
            response = await self.client.post(f"{self.base_url}/{model_name}", headers=headers, json=data)
            response.raise_for_status()
            
            result = response.json()
            content = result[0]['generated_text'] if isinstance(result, list) else result.get('generated_text', str(result))
            
            recommendation, confidence, reasoning = self._parse_response(content)
            
            return AIAnalysisResult(
                analysis_type=AIAnalysisType.PATTERN,
                confidence=confidence,
                recommendation=recommendation,
                reasoning=reasoning,
                provider="huggingface",
                model=self.model,
                metadata={"response": content, "context": context}
            )
            
        except Exception as e:
            self.logger.error(f"HuggingFace analysis error: {e}")
            return None

class CerebrasProvider(AIProvider):
    def __init__(self, config: Dict, client: httpx.AsyncClient):
        super().__init__(config, 'cerebras', client)
        self.base_url = "https://api.cerebras.ai/v1/chat/completions"
    
    async def _analyze_impl(self, prompt: str, context: Dict[str, Any] = None) -> Optional[AIAnalysisResult]:
        if not self.is_available():
            return None
        
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            system_message = """You are an expert financial analyst and trading advisor. 
            Analyze the provided market data and give concise, actionable trading advice.
            Respond with a clear recommendation (BUY, SELL, HOLD, STRONG_BUY, STRONG_SELL) 
            and explain your reasoning based on technical analysis."""
            
            user_message = f"Context: {json.dumps(context) if context else 'No additional context'}\n\nAnalysis Request: {prompt}"
            
            data = {
                "model": self.model or "llama3.1-8b-instruct",
                "messages": [
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": user_message}
                ],
                "max_tokens": self.max_tokens,
                "temperature": self.temperature,
                "stream": False
            }
            
            response = await self.client.post(self.base_url, headers=headers, json=data)
            response.raise_for_status()
            
            result = response.json()
            content = result['choices'][0]['message']['content']
            
            recommendation, confidence, reasoning = self._parse_response(content)
            
            return AIAnalysisResult(
                analysis_type=AIAnalysisType.PATTERN,
                confidence=confidence,
                recommendation=recommendation,
                reasoning=reasoning,
                provider="cerebras",
                model=self.model,
                metadata={"response": content, "context": context}
            )
            
        except Exception as e:
            self.logger.error(f"Cerebras analysis error: {e}")
            return None

class OpenRouterProvider(AIProvider):
    def __init__(self, config: Dict, client: httpx.AsyncClient):
        super().__init__(config, 'openrouter', client)
        self.base_url = "https://openrouter.ai/api/v1/chat/completions"
    
    async def _analyze_impl(self, prompt: str, context: Dict[str, Any] = None) -> Optional[AIAnalysisResult]:
        if not self.is_available():
            return None
        
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json",
                "HTTP-Referer": "https://ai-nvestor.com",
                "X-Title": "AI-Nvestor Trading Platform"
            }
            
            system_message = """You are an expert financial analyst and trading advisor. 
            Analyze the provided market data and give concise, actionable trading advice.
            Respond with a clear recommendation (BUY, SELL, HOLD, STRONG_BUY, STRONG_SELL) 
            and explain your reasoning based on technical analysis."""
            
            user_message = f"Context: {json.dumps(context) if context else 'No additional context'}\n\nAnalysis Request: {prompt}"
            
            data = {
                "model": self.model or "anthropic/claude-3-haiku",
                "messages": [
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": user_message}
                ],
                "max_tokens": self.max_tokens,
                "temperature": self.temperature,
                "stream": False
            }
            
            response = await self.client.post(self.base_url, headers=headers, json=data)
            response.raise_for_status()
            
            result = response.json()
            content = result['choices'][0]['message']['content']
            
            recommendation, confidence, reasoning = self._parse_response(content)
            
            return AIAnalysisResult(
                analysis_type=AIAnalysisType.PATTERN,
                confidence=confidence,
                recommendation=recommendation,
                reasoning=reasoning,
                provider="openrouter",
                model=self.model,
                metadata={"response": content, "context": context}
            )
            
        except Exception as e:
            self.logger.error(f"OpenRouter analysis error: {e}")
            return None
    
    def _parse_response(self, content: str) -> tuple[str, float, str]:
        """Parse OpenRouter response to extract recommendation, confidence, and reasoning."""
        try:
            # Extract recommendation from content
            recommendation = "HOLD"
            confidence = 0.5
            reasoning = content
            
            # Look for recommendation keywords
            content_lower = content.lower()
            if "strong buy" in content_lower or "strong_buy" in content_lower:
                recommendation = "STRONG_BUY"
                confidence = 0.9
            elif "buy" in content_lower:
                recommendation = "BUY"
                confidence = 0.7
            elif "strong sell" in content_lower or "strong_sell" in content_lower:
                recommendation = "STRONG_SELL"
                confidence = 0.9
            elif "sell" in content_lower:
                recommendation = "SELL"
                confidence = 0.7
            elif "hold" in content_lower:
                recommendation = "HOLD"
                confidence = 0.5
            
            return recommendation, confidence, reasoning
            
        except Exception as e:
            self.logger.error(f"Error parsing OpenRouter response: {e}")
            return "(undefined)", None, f"⚠️ FALLBACK: {content}"  # N/A instead of fake values

class GoogleAIProvider(AIProvider):
    def __init__(self, config: Dict, client: httpx.AsyncClient):
        super().__init__(config, 'google_ai', client)
        self.base_url = "https://generativelanguage.googleapis.com/v1beta/models"
    
    async def _analyze_impl(self, prompt: str, context: Dict[str, Any] = None) -> Optional[AIAnalysisResult]:
        if not self.is_available():
            self.logger.warning(f"⚠️ Google AI provider not available (enabled: {self.enabled}, api_key: {'set' if self.api_key else 'not set'})")
            return None
        
        self.logger.info(f"🔧 Google AI: Starting analysis for {context.get('symbol', 'unknown')}")
        self.logger.info(f"🔧 Google AI: Model: {self.model}, Max tokens: {self.max_tokens}")
        
        try:
            import time
            start_time = time.time()
            
            # Google AI uses a different API structure
            model_name = self.model or "gemini-1.5-flash"  # Try the newer model
            url = f"{self.base_url}/{model_name}:generateContent"
            
            self.logger.info(f"🔧 Google AI: Using URL: {url}")
            
            headers = {
                "Content-Type": "application/json"
            }
            
            system_message = """You are an expert financial analyst and trading advisor. 
            Analyze the provided market data and give concise, actionable trading advice.
            Respond with a clear recommendation (BUY, SELL, HOLD, STRONG_BUY, STRONG_SELL) 
            and explain your reasoning based on technical analysis."""
            
            user_message = f"Context: {json.dumps(context) if context else 'No additional context'}\n\nAnalysis Request: {prompt}"
            
            data = {
                "contents": [
                    {
                        "parts": [
                            {"text": system_message + "\n\n" + user_message}
                        ]
                    }
                ],
                "generationConfig": {
                    "maxOutputTokens": self.max_tokens,
                    "temperature": self.temperature
                }
            }
            
            self.logger.info(f"🔧 Google AI: Making API request")
            self.logger.info(f"🔧 Google AI: Request data keys: {list(data.keys())}")
            
            # Add API key as query parameter for Google AI
            params = {"key": self.api_key}
            response = await self.client.post(url, headers=headers, json=data, params=params)
            response_time = time.time() - start_time
            
            self.logger.info(f"🔧 Google AI: Response status: {response.status_code}")
            self.logger.info(f"🔧 Google AI: Response time: {response_time:.2f}s")
            
            response.raise_for_status()
            
            result = response.json()
            self.logger.info(f"🔧 Google AI: Response keys: {list(result.keys())}")
            
            content = result['candidates'][0]['content']['parts'][0]['text']
            self.logger.info(f"🔧 Google AI: Response content length: {len(content)}")
            self.logger.info(f"🔧 Google AI: Response preview: {content[:200]}...")
            
            recommendation, confidence, reasoning = self._parse_response(content)
            self.logger.info(f"✅ Google AI: Parsed result - {recommendation} (conf: {confidence:.2f})")
            
            # Store detailed HTTP information in metadata
            http_details = {
                "endpoint": url,
                "method": "POST",
                "status_code": response.status_code,
                "response_time": response_time,
                "request_headers": dict(headers),
                "request_data": data,
                "request_params": params,
                "response_headers": dict(response.headers),
                "response_data": result,
                "raw_response": content
            }
            
            return AIAnalysisResult(
                analysis_type=AIAnalysisType.PATTERN,
                confidence=confidence,
                recommendation=recommendation,
                reasoning=reasoning,
                provider="google_ai",
                model=self.model,
                metadata={"response": content, "context": context, "http_details": http_details}
            )
            
        except Exception as e:
            self.logger.error(f"❌ Google AI analysis error: {e}")
            self.logger.error(f"❌ Google AI error type: {type(e).__name__}")
            import traceback
            self.logger.error(f"❌ Google AI traceback: {traceback.format_exc()}")
            return None
    
    def _parse_response(self, content: str) -> tuple[str, float, str]:
        content_lower = content.lower()
        if any(phrase in content_lower for phrase in ['strong buy', 'very bullish']):
            return "STRONG_BUY", 0.95, content
        if any(phrase in content_lower for phrase in ['strong sell', 'very bearish']):
            return "STRONG_SELL", 0.95, content
        if 'buy' in content_lower:
            return "BUY", 0.8, content
        if 'sell' in content_lower:
            return "SELL", 0.8, content
        return "HOLD", 0.6, content


class AIAdvisor:
    """AI-powered trading advisor with multiple provider support."""
    
    def __init__(self, config: Dict, force_verify: bool = False):
        self.config = config
        self.logger = get_logger()
        self.client = httpx.AsyncClient(timeout=30.0)
        self.force_verify = force_verify
        
        self.logger.info(f"🔧 Initializing AI Advisor with force_verify={force_verify}")
        
        self.tier_1_providers = {}
        self.tier_2_providers = {}
        self.all_providers = {}
        
        # Handle both Pydantic model and dict
        if hasattr(config, 'ai_analysis'):
            # Pydantic model
            ai_config = config.ai_analysis
            if hasattr(ai_config, 'dict'):
                ai_config = ai_config.dict()
            else:
                ai_config = {}
        else:
            # Dictionary
            ai_config = config.get('ai_analysis', {})
        
        self.logger.info(f"🔧 AI config keys: {list(ai_config.keys())}")
        self.logger.info(f"🔧 Full AI config: {ai_config}")
        
        provider_classes = {
            'google_ai': GoogleAIProvider, 'groq': GroqProvider, 'huggingface': HuggingFaceProvider,
            'cerebras': CerebrasProvider, 'openrouter': OpenRouterProvider, 'openai': OpenAIProvider,
            'anthropic': AnthropicProvider
        }

        # Initialize Tier 1 providers
        tier_1_config = ai_config.get('tier_1_providers', {})
        self.logger.info(f"🔧 Tier 1 config: {list(tier_1_config.keys())}")
        
        for name, p_class in provider_classes.items():
            if name in tier_1_config and tier_1_config[name].get('enabled'):
                self.logger.info(f"🔧 Attempting to initialize Tier 1 provider: {name}")
                try:
                    provider = p_class(config, self.client)
                    self.logger.info(f"🔧 Provider {name} created successfully")
                    if provider.is_available():
                        self.logger.info(f"✅ Provider {name} is available and enabled")
                        self.tier_1_providers[name] = provider
                        self.all_providers[name] = provider
                    else:
                        self.logger.warning(f"⚠️ Provider {name} is not available (enabled: {provider.enabled}, api_key: {'set' if provider.api_key else 'not set'})")
                except Exception as e:
                    self.logger.error(f"❌ Failed to initialize Tier 1 provider {name}: {e}")
                    import traceback
                    self.logger.error(f"❌ Traceback: {traceback.format_exc()}")
        
        # Initialize Tier 2 providers
        tier_2_config = ai_config.get('tier_2_providers', {})
        self.logger.info(f"🔧 Tier 2 config: {list(tier_2_config.keys())}")
        
        for name, p_class in provider_classes.items():
             if name in tier_2_config and tier_2_config[name].get('enabled'):
                self.logger.info(f"🔧 Attempting to initialize Tier 2 provider: {name}")
                try:
                    provider = p_class(config, self.client)
                    self.logger.info(f"🔧 Provider {name} created successfully")
                    if provider.is_available():
                        self.logger.info(f"✅ Provider {name} is available and enabled")
                        self.tier_2_providers[name] = provider
                        self.all_providers[name] = provider
                    else:
                        self.logger.warning(f"⚠️ Provider {name} is not available (enabled: {provider.enabled}, api_key: {'set' if provider.api_key else 'not set'})")
                except Exception as e:
                    self.logger.error(f"❌ Failed to initialize Tier 2 provider {name}: {e}")
                    import traceback
                    self.logger.error(f"❌ Traceback: {traceback.format_exc()}")

        self.enabled_providers = list(self.all_providers.values())
        self.logger.info(f"✅ AI providers initialized: {len(self.tier_1_providers)} Tier 1, {len(self.tier_2_providers)} Tier 2.")
        self.logger.info(f"🔧 Tier 1 providers: {list(self.tier_1_providers.keys())}")
        self.logger.info(f"🔧 Tier 2 providers: {list(self.tier_2_providers.keys())}")
        
    async def analyze_sentiment(self, symbol: str, news_data: List[Dict] = None) -> AIAnalysisResult:
        """
        Analyze sentiment for a symbol using multiple data sources.
        
        Args:
            symbol: Stock symbol
            news_data: Optional pre-fetched news data (for backward compatibility)
            
        Returns:
            AIAnalysisResult with sentiment analysis
        """
        if not self.enabled_providers:
            return self._get_placeholder_result(AIAnalysisType.SENTIMENT, "No AI providers available")
        
        try:
            # Import sentiment service
            from services.sentiment_data import SentimentDataService
            
            # Initialize sentiment service
            sentiment_service = SentimentDataService(self.config)
            
            # Get sentiment data
            sentiment_data = await sentiment_service.get_sentiment_data(symbol, hours=24)
            
            if not sentiment_data:
                self.logger.warning(f"No sentiment data available for {symbol}")
                return self._get_placeholder_result(AIAnalysisType.SENTIMENT, f"No sentiment data available for {symbol}")
            
            # Aggregate sentiment metrics
            aggregated_sentiment = sentiment_service.get_aggregated_sentiment(sentiment_data)
            
            # Prepare context for AI analysis
            headlines = [item.text for item in sentiment_data[:5]]  # Top 5 headlines
            context = {
                "symbol": symbol,
                "news_headlines": headlines,
                "sentiment_metrics": aggregated_sentiment,
                "sentiment_data_count": len(sentiment_data)
            }
            
            # Create prompt for AI analysis
            prompt = f"""Analyze sentiment for {symbol} based on the following data:

SENTIMENT METRICS:
- Overall Sentiment: {aggregated_sentiment['overall_sentiment']:.3f} (-1.0 to 1.0)
- Confidence: {aggregated_sentiment['confidence']:.3f}
- Volume: {aggregated_sentiment['volume']} articles/posts
- Positive Ratio: {aggregated_sentiment['positive_ratio']:.1%}
- Negative Ratio: {aggregated_sentiment['negative_ratio']:.1%}

RECENT HEADLINES:
{chr(10).join(f"- {headline}" for headline in headlines)}

Provide a trading recommendation based on this sentiment analysis. Consider:
1. Overall sentiment direction and strength
2. Volume of sentiment data
3. Confidence in the sentiment scores
4. Recent news impact on the stock

Use these exact recommendation levels:
- STRONG_BUY: Very high confidence bullish sentiment
- BUY: Moderate confidence positive sentiment
- HOLD: Neutral/mixed sentiment
- SELL: Moderate confidence negative sentiment
- STRONG_SELL: Very high confidence bearish sentiment"""
            
            # Get AI consensus analysis
            result = await self._get_consensus_analysis(prompt, context, AIAnalysisType.SENTIMENT)
            
            # Add sentiment metadata
            result.metadata.update({
                "sentiment_metrics": aggregated_sentiment,
                "headlines_analyzed": len(headlines),
                "total_sentiment_data": len(sentiment_data)
            })
            
            await sentiment_service.close()
            return result
            
        except Exception as e:
            self.logger.error(f"Error in sentiment analysis for {symbol}: {e}")
            return self._get_placeholder_result(AIAnalysisType.SENTIMENT, f"Sentiment analysis failed: {str(e)}")
    
    async def detect_patterns(self, symbol: str, data: pd.DataFrame, command_description: str = "pattern_analysis") -> AIAnalysisResult:
        """
        Detect patterns and generate AI recommendations with improved conflict handling and adaptive learning.
        
        Args:
            symbol: Stock symbol
            data: OHLCV data
            command_description: Description of the analysis command
            
        Returns:
            AIAnalysisResult with recommendation and confidence
        """
        if not self.enabled_providers:
            self.logger.info(f"🔍 No AI providers available for {symbol}")
            return self._get_placeholder_result(AIAnalysisType.PATTERN, "No AI providers available")
        
        # Create comprehensive analysis log
        analysis_id = str(uuid.uuid4())[:8]
        analysis_log = ai_logger.create_analysis_log(symbol, analysis_id)
        
        self.logger.info(f"🔍 Starting comprehensive analysis for {symbol} (ID: {analysis_id})")
        self.logger.info(f"📊 Data shape: {data.shape}, Columns: {list(data.columns)}")
        self.logger.info(f"📈 Latest price: ${data['Close'].iloc[-1]:.2f}")
        
        try:
            # Get comprehensive market analysis data
            from services.signals import SignalGenerator
            from services.indicators import TechnicalIndicators
            from services.sentiment_data import SentimentDataService
            from services.adaptive_learning import AdaptiveLearningService
            
            # Initialize services
            indicators = TechnicalIndicators(self.config)
            signal_generator = SignalGenerator(self.config)
            sentiment_service = SentimentDataService(self.config)
            adaptive_learning = AdaptiveLearningService(self.config)
            
            # Fetch sentiment data in parallel with technical analysis
            self.logger.info(f"🔧 Fetching sentiment data for {symbol}")
            sentiment_task = asyncio.create_task(sentiment_service.get_sentiment_data(symbol, hours=24))
            
            self.logger.info(f"🔧 Calculating technical indicators for {symbol}")
            # Calculate all technical indicators
            indicator_results = indicators.calculate_all_indicators(data)
            self.logger.info(f"✅ Calculated {len(indicator_results)} indicators: {list(indicator_results.keys())}")
            
            # Log technical indicators for JSON
            indicators_log = {}
            for name, result in indicator_results.items():
                if hasattr(result, 'value') and hasattr(result, 'signal') and hasattr(result, 'confidence'):
                    self.logger.info(f"📊 {name.upper()}: value={result.value:.2f}, signal={result.signal.value}, confidence={result.confidence:.2f}")
                    indicators_log[name] = {
                        "value": float(result.value),
                        "signal": result.signal.value,
                        "confidence": float(result.confidence),
                        "metadata": result.metadata if hasattr(result, 'metadata') else {}
                    }
                else:
                    self.logger.warning(f"⚠️ {name}: Missing attributes - {dir(result)}")
                    indicators_log[name] = {"error": "Missing attributes"}
            
            ai_logger.log_technical_indicators(analysis_log, indicators_log)
            
            self.logger.info(f"🔧 Generating signal for {symbol}")
            # Generate signal for additional context
            signal = signal_generator.generate_signal(symbol, data)
            self.logger.info(f"✅ Signal generated: {signal.signal_type.value if signal else 'None'}")
            
            # Wait for sentiment data to complete
            sentiment_data = await sentiment_task
            self.logger.info(f"✅ Sentiment data fetched: {len(sentiment_data)} data points")
            
            # 🧠 ADAPTIVE LEARNING: Learn from current data and update weights
            self.logger.info(f"🧠 Starting adaptive learning for {symbol}")
            learning_result = await adaptive_learning.learn_from_data(
                symbol=symbol,
                data=data,
                sentiment_data=sentiment_data,
                technical_indicators=indicator_results
            )
            
            # Get optimized weights for this symbol
            optimized_weights = adaptive_learning.get_optimized_weights(symbol)
            feature_importance = adaptive_learning.get_feature_importance(symbol)
            performance_metrics = adaptive_learning.get_performance_metrics(symbol)
            
            self.logger.info(f"🧠 Adaptive learning completed for {symbol}")
            self.logger.info(f"📊 Optimized weights: {len(optimized_weights)} weights updated")
            self.logger.info(f"📈 Model accuracy: {learning_result.model_accuracy:.3f}")
            
            # Log adaptive learning results
            learning_log = {
                "model_accuracy": learning_result.model_accuracy,
                "optimized_weights": optimized_weights,
                "feature_importance": feature_importance,
                "performance_metrics": performance_metrics,
                "learning_mode": learning_result.learning_mode.value,
                "metadata": learning_result.metadata
            }
            ai_logger.log_adaptive_learning(analysis_log, learning_log)
            
            if signal:
                self.logger.info(f"📊 Technical Signal: {signal.signal_type.value} (Confidence: {signal.confidence:.2f})")
                
                # Check for signal conflicts and adjust AI analysis accordingly
                signal_conflict = False
                if signal.signal_type.value in ['STRONG_SELL', 'SELL']:
                    # Look for bullish technical indicators that might conflict
                    bullish_indicators = []
                    for name, result in indicator_results.items():
                        if hasattr(result, 'signal') and result.signal.value in ['BUY', 'STRONG_BUY']:
                            bullish_indicators.append(name)
                    
                    if bullish_indicators:
                        signal_conflict = True
                        self.logger.info(f"⚠️ Signal conflict detected: Technical {signal.signal_type.value} but bullish indicators: {bullish_indicators}")
                
                elif signal.signal_type.value in ['STRONG_BUY', 'BUY']:
                    # Look for bearish technical indicators that might conflict
                    bearish_indicators = []
                    for name, result in indicator_results.items():
                        if hasattr(result, 'signal') and result.signal.value in ['SELL', 'STRONG_SELL']:
                            bearish_indicators.append(name)
                    
                    if bearish_indicators:
                        signal_conflict = True
                        self.logger.info(f"⚠️ Signal conflict detected: Technical {signal.signal_type.value} but bearish indicators: {bearish_indicators}")
                
                self.logger.info(f"📊 Signal details: strength={signal.strength.value}, confidence={signal.confidence:.2f}, market_condition={signal.market_condition.value}")
                self.logger.info(f"📊 Signal metadata: volume_support={signal.volume_support}, trend_alignment={signal.trend_alignment}")
                
                # Log signal analysis for JSON
                signal_log = {
                    "signal_type": signal.signal_type.value,
                    "signal_strength": signal.strength.value,
                    "confidence": float(signal.confidence),
                    "market_condition": signal.market_condition.value,
                    "trend_alignment": bool(signal.trend_alignment),
                    "volume_support": bool(signal.volume_support),
                    "metadata": signal.metadata if hasattr(signal, 'metadata') else {}
                }
                ai_logger.log_signal_analysis(analysis_log, signal_log)
            
            # Process sentiment data with optimized weights
            sentiment_metrics = {}
            sentiment_headlines = []
            if sentiment_data:
                # Aggregate sentiment metrics with optimized weights
                sentiment_metrics = sentiment_service.get_aggregated_sentiment(sentiment_data)
                sentiment_headlines = [item.text for item in sentiment_data[:5]]  # Top 5 headlines
                
                # Apply optimized sentiment weights
                if optimized_weights:
                    sentiment_weight = optimized_weights.get('reddit_sentiment', 0.3)
                    sentiment_volume_weight = optimized_weights.get('sentiment_volume', 0.15)
                    sentiment_confidence_weight = optimized_weights.get('sentiment_confidence', 0.25)
                    
                    # Adjust sentiment metrics based on learned weights
                    adjusted_sentiment = sentiment_metrics['overall_sentiment'] * sentiment_weight
                    adjusted_volume = sentiment_metrics['volume'] * sentiment_volume_weight
                    adjusted_confidence = sentiment_metrics['confidence'] * sentiment_confidence_weight
                    
                    sentiment_metrics.update({
                        'adjusted_sentiment': adjusted_sentiment,
                        'adjusted_volume': adjusted_volume,
                        'adjusted_confidence': adjusted_confidence,
                        'sentiment_weight': sentiment_weight,
                        'volume_weight': sentiment_volume_weight,
                        'confidence_weight': sentiment_confidence_weight
                    })
                
                self.logger.info(f"📊 Sentiment Metrics: Overall={sentiment_metrics['overall_sentiment']:.3f}, Confidence={sentiment_metrics['confidence']:.3f}, Volume={sentiment_metrics['volume']}")
                if 'adjusted_sentiment' in sentiment_metrics:
                    self.logger.info(f"🧠 Adjusted Sentiment: {sentiment_metrics['adjusted_sentiment']:.3f} (weight: {sentiment_metrics['sentiment_weight']:.3f})")
                
                # Log sentiment data for JSON
                sentiment_log = {
                    "metrics": sentiment_metrics,
                    "headlines": sentiment_headlines,
                    "total_data_points": len(sentiment_data),
                    "optimized_weights": {k: v for k, v in optimized_weights.items() if k.startswith('sentiment')}
                }
                ai_logger.log_sentiment_data(analysis_log, sentiment_log)
            else:
                self.logger.warning(f"⚠️ No sentiment data available for {symbol}")
                sentiment_metrics = {
                    "overall_sentiment": 0.0,
                    "confidence": 0.0,
                    "volume": 0,
                    "positive_ratio": 0.0,
                    "negative_ratio": 0.0
                }
            
            # Build comprehensive context with ALL available data (including sentiment and adaptive learning)
            self.logger.info(f"🔧 Building comprehensive context for {symbol}")
            
            # Helper function to convert numpy/pandas types to native Python types
            def convert_to_native(obj):
                if hasattr(obj, 'item'):  # numpy scalar
                    return obj.item()
                elif isinstance(obj, (np.integer, np.floating)):
                    return float(obj)
                elif isinstance(obj, (pd.Timestamp, datetime)):
                    return obj.isoformat()
                elif isinstance(obj, (list, tuple)):
                    return [convert_to_native(item) for item in obj]
                elif isinstance(obj, dict):
                    return {k: convert_to_native(v) for k, v in obj.items()}
                else:
                    return obj
            
            context = {
                "symbol": symbol,
                "current_price": float(data['Close'].iloc[-1]),
                "data_points": int(len(data)),
                "price_data": {
                    "current": float(data['Close'].iloc[-1]),
                    "open": float(data['Open'].iloc[-1]),
                    "high": float(data['High'].iloc[-1]),
                    "low": float(data['Low'].iloc[-1]),
                    "volume": int(data['Volume'].iloc[-1]),
                    "price_range": {
                        "high": float(data['High'].max()),
                        "low": float(data['Low'].min()),
                        "current": float(data['Close'].iloc[-1])
                    },
                    "trend_direction": "up" if data['Close'].iloc[-1] > data['Close'].iloc[-5] else "down"
                },
                "volume_analysis": {
                    "current_volume": int(data['Volume'].iloc[-1]),
                    "avg_volume": int(data['Volume'].rolling(window=20).mean().iloc[-1]),
                    "max_volume": int(data['Volume'].max()),
                    "volume_support": bool(signal.volume_support) if signal else False
                },
                "volatility": {
                    "annualized_volatility": float(data['Close'].pct_change().std() * np.sqrt(252)),
                    "atr": float(indicator_results.get('atr').value) if indicator_results.get('atr') else 0
                },
                "technical_indicators": {
                    name: {
                        "value": float(result.value),
                        "signal": result.signal.value,
                        "confidence": float(result.confidence)
                    } for name, result in indicator_results.items()
                },
                "signal_analysis": {
                    "signal_type": signal.signal_type.value if signal else "HOLD",
                    "signal_strength": int(signal.strength.value) if signal else 1,
                    "confidence": float(signal.confidence) if signal and signal.confidence is not None else None,
                    "market_condition": signal.market_condition.value if signal else "SIDEWAYS",
                    "trend_alignment": bool(signal.trend_alignment) if signal else False,
                    "volume_support": bool(signal.volume_support) if signal else False
                },
                "sentiment_analysis": {
                    "overall_sentiment": sentiment_metrics["overall_sentiment"],
                    "confidence": sentiment_metrics["confidence"],
                    "volume": sentiment_metrics["volume"],
                    "positive_ratio": sentiment_metrics["positive_ratio"],
                    "negative_ratio": sentiment_metrics["negative_ratio"],
                    "recent_headlines": sentiment_headlines
                },
                "metadata": convert_to_native(signal.metadata) if signal else {},
                "adaptive_learning": {
                    "optimized_weights": optimized_weights,
                    "feature_importance": feature_importance,
                    "performance_metrics": performance_metrics,
                    "learning_mode": learning_result.learning_mode.value,
                    "model_accuracy": learning_result.model_accuracy
                }
            }
            
            # Log input data for JSON
            ai_logger.log_input_data(analysis_log, context)
            
            self.logger.info(f"✅ Context built successfully for {symbol}")
            self.logger.info(f"📊 Context keys: {list(context.keys())}")
            self.logger.info(f"📊 Technical indicators count: {len(context['technical_indicators'])}")
            self.logger.info(f"📊 Sentiment data points: {sentiment_metrics['volume']}")
            
            # Create enhanced prompt with sentiment data
            sentiment_section = ""
            if sentiment_data:
                sentiment_section = f"""

SENTIMENT ANALYSIS:
- Overall Sentiment: {sentiment_metrics['overall_sentiment']:.3f} (-1.0 to 1.0)
- Confidence: {sentiment_metrics['confidence']:.3f}
- Volume: {sentiment_metrics['volume']} articles/posts
- Positive Ratio: {sentiment_metrics['positive_ratio']:.1%}
- Negative Ratio: {sentiment_metrics['negative_ratio']:.1%}

RECENT HEADLINES:
{chr(10).join(f"- {headline}" for headline in sentiment_headlines) if sentiment_headlines else "- No recent headlines available"}"""
            
            prompt = f"""Analyze technical patterns and market sentiment for {symbol} currently at ${context['current_price']:.2f}.

COMPREHENSIVE MARKET DATA:
{json.dumps(context, indent=2, default=str)}{sentiment_section}

Provide a detailed technical and sentiment analysis and trading recommendation based on ALL this data. Consider:
1. Technical indicators and patterns
2. Market sentiment and recent news
3. Volume analysis and trend alignment
4. Any conflicts between technical and sentiment signals

Use these exact recommendation levels:
- STRONG_BUY: Very high confidence bullish signal (technical + sentiment aligned)
- BUY: Moderate confidence buy signal
- HOLD: Neutral/mixed signals or unclear direction
- SELL: Moderate confidence sell signal
- STRONG_SELL: Very high confidence bearish signal (technical + sentiment aligned)"""
            
            self.logger.info(f"📝 Generated comprehensive prompt for {symbol}")
            self.logger.info(f"📝 Prompt length: {len(prompt)} characters")
            
            # Log the prompt for each provider
            for provider_name in self.tier_1_providers.keys():
                ai_logger.log_ai_prompt(analysis_log, provider_name, prompt)
            
            # Get consensus analysis with logging
            result = await self._get_consensus_analysis_with_logging(prompt, context, AIAnalysisType.PATTERN, analysis_log)
            
            # Log the final result
            consensus_log = {
                "recommendation": result.recommendation,
                "confidence": result.confidence,
                "reasoning": result.reasoning,
                "provider": result.provider,
                "model": result.model,
                "metadata": result.metadata
            }
            ai_logger.log_consensus_result(analysis_log, consensus_log)
            
            # Add sentiment metadata to result
            result.metadata.update({
                "sentiment_metrics": sentiment_metrics,
                "headlines_analyzed": len(sentiment_headlines),
                "total_sentiment_data": len(sentiment_data)
            })
            
            # Cleanup
            await sentiment_service.close()
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error in pattern detection for {symbol}: {e}")
            return self._get_placeholder_result(AIAnalysisType.PATTERN, f"Pattern detection failed: {str(e)}")

    async def predict_price_movement(self, symbol: str, data: pd.DataFrame) -> AIAnalysisResult:
        if not self.enabled_providers:
            return self._get_placeholder_result(AIAnalysisType.PREDICTIVE, "No AI providers available")
        context = {"symbol": symbol, "current_price": float(data['Close'].iloc[-1])}
        prompt = f"Predict price movement for {symbol} at ${context['current_price']:.2f}."
        return await self._get_consensus_analysis(prompt, context, AIAnalysisType.PREDICTIVE)

    async def assess_risk(self, symbol: str, data: pd.DataFrame, portfolio_context: Dict = None) -> AIAnalysisResult:
        if not self.enabled_providers:
            return self._get_placeholder_result(AIAnalysisType.RISK_ASSESSMENT, "No AI providers available")
        context = {"symbol": symbol, "volatility": float(data['Close'].pct_change().std())}
        prompt = f"Assess risk for {symbol} with volatility of {context['volatility']:.2%}"
        return await self._get_consensus_analysis(prompt, context, AIAnalysisType.RISK_ASSESSMENT)

    async def optimize_portfolio(self, portfolio: Dict[str, float], available_symbols: List[str], market_data: Dict[str, pd.DataFrame]) -> AIAnalysisResult:
        if not self.enabled_providers:
            return self._get_placeholder_result(AIAnalysisType.OPTIMIZATION, "No AI providers available")
        context = {"portfolio": portfolio, "available_symbols": available_symbols}
        prompt = "Optimize this portfolio."
        return await self._get_consensus_analysis(prompt, context, AIAnalysisType.OPTIMIZATION)

    async def _get_consensus_analysis_with_logging(self, prompt: str, context: Dict, analysis_type: AIAnalysisType, analysis_log: Dict) -> AIAnalysisResult:
        """
        Get consensus analysis with improved conflict detection (ChatGPT's suggestion).
        """
        try:
            if not self.enabled_providers:
                return self._get_placeholder_result(analysis_type, "No AI providers available")
            
            # Get individual provider responses
            provider_results = []
            for provider_name, provider in self.tier_1_providers.items():
                try:
                    result = await provider.analyze(prompt, context)
                    if result:
                        provider_results.append({
                            'provider': provider_name,
                            'recommendation': result.recommendation,
                            'confidence': result.confidence,
                            'reasoning': result.reasoning
                        })
                        self.logger.info(f"✅ {provider_name}: {result.recommendation} (Conf: {result.confidence:.2f})")
                except Exception as e:
                    self.logger.warning(f"⚠️ {provider_name} failed: {e}")
            
            if not provider_results:
                return self._get_placeholder_result(analysis_type, "All providers failed")
            
            # Calculate consensus with improved logic
            consensus_result = self._calculate_enhanced_consensus(provider_results, context, analysis_type)
            
            # Apply ChatGPT's suggestion: use AI consensus as meta-feature
            if consensus_result.recommendation in ['STRONG_BUY', 'STRONG_SELL']:
                # Check if AI providers disagree significantly (>60% disagreement)
                disagreement_ratio = self._calculate_ai_disagreement(provider_results, consensus_result.recommendation)
                if disagreement_ratio > 0.6:
                    # Downgrade STRONG signal to regular signal
                    if consensus_result.recommendation == 'STRONG_BUY':
                        consensus_result.recommendation = 'BUY'
                    elif consensus_result.recommendation == 'STRONG_SELL':
                        consensus_result.recommendation = 'SELL'
                    
                    self.logger.info(f"⚠️ Downgraded AI consensus from {consensus_result.recommendation} due to {disagreement_ratio:.1%} disagreement")
                    consensus_result.reasoning += f" [Downgraded due to {disagreement_ratio:.1%} AI disagreement]"
            
            return consensus_result
            
        except Exception as e:
            self.logger.error(f"Error in consensus analysis: {e}")
            return self._get_placeholder_result(analysis_type, f"Consensus error: {str(e)}")
    
    def _calculate_ai_disagreement(self, provider_results: List[Dict], consensus_recommendation: str) -> float:
        """
        Calculate disagreement ratio among AI providers (ChatGPT's suggestion).
        """
        try:
            if not provider_results:
                return 0.0
            
            total_providers = len(provider_results)
            disagreeing_providers = 0
            
            # Define what constitutes disagreement
            if consensus_recommendation in ['STRONG_BUY', 'BUY']:
                # Disagreement is SELL or STRONG_SELL
                disagreeing_signals = ['SELL', 'STRONG_SELL']
            elif consensus_recommendation in ['STRONG_SELL', 'SELL']:
                # Disagreement is BUY or STRONG_BUY
                disagreeing_signals = ['BUY', 'STRONG_BUY']
            else:
                # For HOLD, disagreement is any strong signal
                disagreeing_signals = ['STRONG_BUY', 'STRONG_SELL']
            
            for result in provider_results:
                if result['recommendation'] in disagreeing_signals:
                    disagreeing_providers += 1
            
            return disagreeing_providers / total_providers
            
        except Exception:
            return 0.0
    
    def _calculate_enhanced_consensus(self, provider_results: List[Dict], context: Dict, analysis_type: AIAnalysisType) -> AIAnalysisResult:
        """
        Calculate enhanced consensus from individual provider results.
        """
        symbol = context.get('symbol', 'N/A')
        self.logger.info(f"🚀 Starting enhanced consensus analysis for {symbol}")
        
        # Extract recommendations and confidences
        recommendations = [r['recommendation'] for r in provider_results]
        confidences = [r['confidence'] for r in provider_results]
        reasoning_parts = [r['reasoning'] for r in provider_results]
        
        # Determine the final recommendation based on the majority
        final_recommendation = max(set(recommendations), key=recommendations.count)
        
        # Calculate average confidence
        valid_confidences = [c for c in confidences if c is not None]
        avg_confidence = np.mean(valid_confidences) if valid_confidences else None
        
        # Combine reasoning from all providers
        final_reasoning = "Consensus from " + ", ".join([f"{r['provider']}: {r['recommendation']} (Conf: {r['confidence']:.2f})" for r in provider_results])
        
        # Add specific reasoning for the final recommendation
        if final_recommendation in ['STRONG_BUY', 'BUY']:
            final_reasoning += " [Strong Buy]"
        elif final_recommendation in ['STRONG_SELL', 'SELL']:
            final_reasoning += " [Strong Sell]"
        elif final_recommendation == 'HOLD':
            final_reasoning += " [Hold]"
        
        # Add meta-feature reasoning if applicable
        if final_recommendation in ['STRONG_BUY', 'STRONG_SELL']:
            disagreement_ratio = self._calculate_ai_disagreement(provider_results, final_recommendation)
            if disagreement_ratio > 0.6:
                final_reasoning += f" [Downgraded due to {disagreement_ratio:.1%} AI disagreement]"
        
        return AIAnalysisResult(
            analysis_type=analysis_type,
            confidence=avg_confidence,
            recommendation=final_recommendation,
            reasoning=final_reasoning,
            provider="ensemble_tier_1_only", # This is a placeholder, actual provider name will be set later
            model="multi-provider-tier-1", # This is a placeholder, actual model name will be set later
            metadata={
                "provider_count": len(provider_results),
                "individual_results": provider_results,
                "tier_2_escalation": {"would_escalate": False} # No escalation for enhanced consensus
            }
        )
    
    async def _get_consensus_analysis(self, prompt: str, context: Dict, analysis_type: AIAnalysisType) -> AIAnalysisResult:
        """Original consensus analysis method for backward compatibility."""
        symbol = context.get('symbol', 'N/A')
        self.logger.info(f"🚀 Starting consensus analysis for {symbol}")
        self.logger.info(f"📝 Sending prompt to Tier 1 providers: '{prompt[:200]}...'")
        self.logger.info(f"🔧 Available Tier 1 providers: {list(self.tier_1_providers.keys())}")
        self.logger.info(f"🔧 Available Tier 2 providers: {list(self.tier_2_providers.keys())}")
        
        # --- Tier 1 Analysis with Retry Logic ---
        self.logger.info(f"🔧 Creating {len(self.tier_1_providers)} Tier 1 analysis tasks")
        
        # Add delay between symbol analyses to avoid overwhelming providers
        await asyncio.sleep(random.uniform(0.1, 0.5))
        
        tier_1_tasks = [provider.analyze(prompt, context) for provider in self.tier_1_providers.values()]
        
        self.logger.info(f"⏳ Executing Tier 1 analysis tasks...")
        tier_1_results = await asyncio.gather(*tier_1_tasks, return_exceptions=True)
        
        self.logger.info(f"✅ Tier 1 analysis completed. Results: {len(tier_1_results)}")
        
        # Log each result with better error handling
        valid_tier_1_results = []
        failed_providers = []
        
        for i, result in enumerate(tier_1_results):
            provider_name = list(self.tier_1_providers.keys())[i]
            if isinstance(result, Exception):
                self.logger.error(f"❌ Tier 1 provider {provider_name} failed: {result}")
                failed_providers.append(provider_name)
            elif result is None:
                self.logger.warning(f"⚠️ Tier 1 provider {provider_name} returned None")
                failed_providers.append(provider_name)
            else:
                self.logger.info(f"✅ Tier 1 provider {provider_name}: {result.recommendation} (conf: {result.confidence:.2f})")
                valid_tier_1_results.append(result)
        
        self.logger.info(f"✅ Valid Tier 1 results: {len(valid_tier_1_results)}")
        if failed_providers:
            self.logger.warning(f"⚠️ Failed providers for {symbol}: {failed_providers}")
        
        # Log individual provider responses
        for result in valid_tier_1_results:
            self.logger.info(
                f"📊 {symbol}: Response from {result.provider} (Model: {result.model or 'default'}): "
                f"Reco='{result.recommendation}', Conf={result.confidence:.2f}, Reasoning='{result.reasoning[:100]}...'"
            )
            
        if not valid_tier_1_results:
            self.logger.warning(f"⚠️ No valid Tier 1 AI providers responded for {symbol}")
            return self._get_placeholder_result(analysis_type, f"No Tier 1 AI providers responded. Failed providers: {failed_providers}")

        # Calculate Tier 1 consensus
        tier_1_providers_used = [r.provider for r in valid_tier_1_results]
        self.logger.info(f"📊 {symbol}: Tier 1 analysis used providers: {tier_1_providers_used}")
        
        tier_1_recommendations = [r.recommendation for r in valid_tier_1_results]
        tier_1_confidences = [r.confidence for r in valid_tier_1_results]
        
        self.logger.info(f"📊 {symbol}: Recommendations: {tier_1_recommendations}")
        self.logger.info(f"📊 {symbol}: Confidences: {[f'{c:.2f}' for c in tier_1_confidences]}")
        
        if tier_1_recommendations:
            consensus_recommendation = max(set(tier_1_recommendations), key=tier_1_recommendations.count)
        else:
            consensus_recommendation = "(undefined)"
        
        # Filter out None confidence values for valid calculations
        valid_confidences = [conf for conf in tier_1_confidences if conf is not None]
        avg_confidence = np.mean(valid_confidences) if valid_confidences else None
        self.logger.info(f"📊 {symbol}: Consensus recommendation: {consensus_recommendation}")
        confidence_str = f"{avg_confidence:.2f}" if avg_confidence is not None else "N/A"
        self.logger.info(f"📊 {symbol}: Average confidence: {confidence_str}")
        
        # --- Tier 2 Handoff Logic ---
        tier_2_threshold = self.config.get('ai_analysis', {}).get('tier_2_threshold', 0.75)
        stop_at_tier_1 = self.config.get('ai_analysis', {}).get('stop_at_tier_1', False)
        
        self.logger.info(f"🔧 Tier 2 threshold: {tier_2_threshold}")
        self.logger.info(f"🔧 Stop at Tier 1: {stop_at_tier_1}")
        
        final_recommendation = consensus_recommendation
        final_confidence = avg_confidence
        final_reasoning = f"Consensus from {len(valid_tier_1_results)} Tier 1 providers. Votes: {dict(pd.Series(tier_1_recommendations).value_counts())}"

        # Check if Tier 2 escalation would be needed (only if we have valid confidence)
        would_escalate_to_tier_2 = (avg_confidence is not None and 
                                   avg_confidence > tier_2_threshold and 
                                   self.tier_2_providers)
        
        if would_escalate_to_tier_2:
            symbol = context.get('symbol', 'N/A')
            confidence_str = f"{avg_confidence:.2f}" if avg_confidence is not None else "N/A"
            self.logger.info(f"🔧 {symbol}: Confidence ({confidence_str}) > threshold ({tier_2_threshold})")
            
            if stop_at_tier_1:
                # Show what would be sent to Tier 2
                tier_2_prompt = f"""
                ENHANCED ANALYSIS REQUEST FOR TIER 2:
                
                Symbol: {symbol}
                Tier 1 Consensus: {consensus_recommendation}
                Tier 1 Average Confidence: {avg_confidence:.2f}
                Tier 1 Individual Results: {[f'{r.provider}: {r.recommendation} (conf: {r.confidence:.2f})' for r in valid_tier_1_results]}
                
                Original Prompt: {prompt}
                
                Original Context: {context}
                
                Please provide enhanced analysis with higher confidence and more detailed reasoning.
                """
                
                self.logger.info(f"🔍 STOP AT TIER 1: Would escalate to Tier 2 for {symbol}")
                self.logger.info(f"🔍 STOP AT TIER 1: Tier 2 prompt would be: {tier_2_prompt[:500]}...")
                self.logger.info(f"🔍 STOP AT TIER 1: Available Tier 2 providers: {list(self.tier_2_providers.keys())}")

        return AIAnalysisResult(
            analysis_type=analysis_type,
            confidence=final_confidence,
            recommendation=final_recommendation,
            reasoning=final_reasoning,
            provider="consensus",
            model="tier_1_consensus",
            metadata={
                "tier_1_providers_used": tier_1_providers_used,
                "tier_1_recommendations": tier_1_recommendations,
                "tier_1_confidences": tier_1_confidences,
                "failed_providers": failed_providers,
                "would_escalate_to_tier_2": would_escalate_to_tier_2
            }
        )
    
    def _get_placeholder_result(self, analysis_type: AIAnalysisType, message: str) -> AIAnalysisResult:
        return AIAnalysisResult(
            analysis_type=analysis_type, 
            confidence=None,  # N/A instead of fake 0.5
            recommendation="(undefined)", 
            reasoning=f"⚠️ FALLBACK: {message}", 
            provider="fallback", 
            model="none", 
            metadata={"fallback_reason": message}
        )
    
    async def async_close(self):
        """Clean up all AI provider sessions."""
        await self.client.aclose()
