"""
Configuration management system for AI-Nvestor.

This module provides:
- Configuration loading and validation
- Environment variable management
- Schema validation for trading parameters
- Secure credential handling
"""

import json
import os
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
from pydantic import BaseModel, Field, validator
from dotenv import load_dotenv
import jsonschema


class TimeframeConfig(BaseModel):
    """Configuration for timeframes and data intervals."""
    interval: str = Field(default="5m", description="Data interval")
    days_of_history: int = Field(default=30, ge=1, le=365, description="Days of historical data")
    backtest_days: int = Field(default=365, ge=30, le=3650, description="Days for backtesting")
    
    # Updated: Backtesting time windows focused on 6-12 months only
    backtest_windows: Dict[str, int] = Field(
        default={
            "primary": 365,      # 12 months - optimal for current market speed
            "short_term": 180,   # 6 months - quick validation
            "recent": 90         # 3 months - very recent market conditions
        },
        description="Backtesting time windows focused on 6-12 months for current market relevance"
    )
    
    # Market regime considerations for 6-12 month windows
    market_regime_weights: Dict[str, float] = Field(
        default={
            "bullish": 0.4,      # Weight for bullish market periods
            "bearish": 0.3,      # Weight for bearish market periods  
            "sideways": 0.2,     # Weight for sideways market periods
            "volatile": 0.1      # Weight for volatile market periods
        },
        description="Weights for different market regimes in backtesting"
    )


class IndicatorConfig(BaseModel):
    """Configuration for technical indicators."""
    rsi_period: int = Field(default=14, ge=5, le=50, description="RSI calculation period")
    rsi_overbought: float = Field(default=70, ge=60, le=90, description="RSI overbought threshold")
    rsi_oversold: float = Field(default=30, ge=10, le=40, description="RSI oversold threshold")
    
    macd: Dict[str, int] = Field(
        default={"fast_period": 12, "slow_period": 26, "signal_period": 9},
        description="MACD parameters"
    )
    
    bollinger_bands: Dict[str, Union[int, float]] = Field(
        default={"period": 20, "std_dev": 2},
        description="Bollinger Bands parameters"
    )
    
    stochastic: Dict[str, int] = Field(
        default={"k_period": 14, "d_period": 3},
        description="Stochastic oscillator parameters"
    )
    
    atr_period: int = Field(default=14, ge=5, le=50, description="ATR calculation period")


class RiskManagementConfig(BaseModel):
    """Configuration for risk management parameters."""
    max_position_size: float = Field(default=0.02, ge=0.001, le=0.1, description="Maximum position size as fraction of portfolio")
    stop_loss_percentage: float = Field(default=0.05, ge=0.01, le=0.20, description="Stop loss percentage")
    take_profit_percentage: float = Field(default=0.10, ge=0.02, le=0.50, description="Take profit percentage")
    max_daily_loss: float = Field(default=0.03, ge=0.01, le=0.10, description="Maximum daily loss")
    max_portfolio_risk: float = Field(default=0.15, ge=0.05, le=0.30, description="Maximum portfolio risk")
    position_sizing_method: str = Field(default="kelly_criterion", description="Position sizing method")


class TradingConfig(BaseModel):
    """Configuration for trading parameters."""
    paper_trading: bool = Field(default=True, description="Enable paper trading mode")
    real_time_enabled: bool = Field(default=True, description="Enable real-time data")
    auto_trading: bool = Field(default=False, description="Enable automated trading")
    max_concurrent_positions: int = Field(default=5, ge=1, le=20, description="Maximum concurrent positions")
    min_volume_threshold: int = Field(default=1000000, ge=100000, description="Minimum volume threshold")


class MarketHoursConfig(BaseModel):
    """Configuration for market hours."""
    start: str = Field(default="09:30", description="Market open time")
    end: str = Field(default="16:00", description="Market close time")
    pre_market_start: str = Field(default="06:30", description="Pre-market start time")
    after_hours_end: str = Field(default="20:00", description="After-hours end time")
    timezone: str = Field(default="America/New_York", description="Market timezone")


class DataSourcesConfig(BaseModel):
    """Configuration for data sources."""
    primary: str = Field(default="yfinance", description="Primary data source")
    backup: str = Field(default="alpha_vantage", description="Backup data source")
    real_time: str = Field(default="polygon", description="Real-time data source")
    cache_duration: int = Field(default=300, ge=60, le=3600, description="Cache duration in seconds")


class LoggingConfig(BaseModel):
    """Configuration for logging."""
    level: str = Field(default="INFO", description="Logging level")
    file: str = Field(default="logs/trading.log", description="Log file path")
    max_size: str = Field(default="10MB", description="Maximum log file size")
    backup_count: int = Field(default=5, ge=1, le=20, description="Number of backup files")


class NotificationsConfig(BaseModel):
    """Configuration for notifications."""
    email_enabled: bool = Field(default=False, description="Enable email notifications")
    slack_enabled: bool = Field(default=False, description="Enable Slack notifications")
    discord_enabled: bool = Field(default=False, description="Enable Discord notifications")
    webhook_url: str = Field(default="", description="Webhook URL for notifications")


class PerformanceConfig(BaseModel):
    """Configuration for performance metrics."""
    benchmark: str = Field(default="SPY", description="Benchmark symbol")
    risk_free_rate: float = Field(default=0.02, ge=0, le=0.10, description="Risk-free rate")
    min_sharpe_ratio: float = Field(default=1.0, ge=0.5, le=3.0, description="Minimum Sharpe ratio")
    max_drawdown: float = Field(default=0.20, ge=0.05, le=0.50, description="Maximum drawdown")


class AIProviderConfig(BaseModel):
    """Configuration for individual AI provider."""
    enabled: bool = Field(default=False, description="Enable this AI provider")
    model: str = Field(default="", description="Model name")
    max_tokens: int = Field(default=1000, ge=100, le=4000, description="Maximum tokens")
    temperature: float = Field(default=0.3, ge=0.0, le=1.0, description="Temperature for generation")
    cost_tier: str = Field(default="free", description="Cost tier (free/paid)")


class AIAnalysisTypesConfig(BaseModel):
    """Configuration for AI analysis types."""
    sentiment: bool = Field(default=True, description="Enable sentiment analysis")
    pattern_recognition: bool = Field(default=True, description="Enable pattern recognition")
    price_prediction: bool = Field(default=True, description="Enable price prediction")
    risk_assessment: bool = Field(default=True, description="Enable risk assessment")
    portfolio_optimization: bool = Field(default=True, description="Enable portfolio optimization")


class AIAnalysisConfig(BaseModel):
    """Configuration for AI analysis."""
    enabled: bool = Field(default=True, description="Enable AI analysis")
    model_type: str = Field(default="tiered", description="AI model type")
    confidence_threshold: float = Field(default=0.6, ge=0.5, le=0.95, description="Confidence threshold")
    update_frequency: str = Field(default="1h", description="Model update frequency")
    tier_1_threshold: float = Field(default=0.5, description="Threshold for Tier 1 AI")
    tier_2_threshold: float = Field(default=0.75, description="Threshold for Tier 2 AI")
    stop_at_tier_1: bool = Field(default=False, description="Stop analysis at Tier 1 and show what would be sent to Tier 2")
    
    # Tier 1 providers (free)
    tier_1_providers: Dict[str, AIProviderConfig] = Field(
        default_factory=lambda: {
            "google_ai": AIProviderConfig(enabled=True, model="gemini-pro", cost_tier="free"),
            "groq": AIProviderConfig(enabled=True, model="llama3-8b-8192", cost_tier="free"),
            "huggingface": AIProviderConfig(enabled=True, model="meta-llama/Llama-2-7b-chat-hf", cost_tier="free"),
            "cerebras": AIProviderConfig(enabled=True, model="llama3.1-8b", cost_tier="free"),
            "openrouter": AIProviderConfig(enabled=True, model="meta-llama/llama-3.1-8b-instruct:free", cost_tier="free")
        },
        description="Tier 1 (free) AI provider configurations"
    )
    
    # Tier 2 providers (paid)
    tier_2_providers: Dict[str, AIProviderConfig] = Field(
        default_factory=lambda: {
            "openai": AIProviderConfig(enabled=True, model="gpt-4o-mini", cost_tier="paid"),
            "anthropic": AIProviderConfig(enabled=True, model="claude-3-sonnet-20240229", cost_tier="paid")
        },
        description="Tier 2 (paid) AI provider configurations"
    )
    
    # Backwards compatibility
    providers: Dict[str, AIProviderConfig] = Field(
        default_factory=lambda: {
            "openai": AIProviderConfig(enabled=False, model="gpt-4"),
            "anthropic": AIProviderConfig(enabled=False, model="claude-3-sonnet-20240229"),
            "google_ai": AIProviderConfig(enabled=False, model="gemini-pro"),
            "cohere": AIProviderConfig(enabled=False, model="command"),
            "huggingface": AIProviderConfig(enabled=False, model="meta-llama/Llama-2-7b-chat-hf")
        },
        description="Legacy AI provider configurations"
    )
    
    analysis_types: AIAnalysisTypesConfig = Field(default_factory=AIAnalysisTypesConfig)


class DatabaseConfig(BaseModel):
    """Configuration for database."""
    type: str = Field(default="sqlite", description="Database type")
    path: str = Field(default="data/trading.db", description="Database path")
    backup_frequency: str = Field(default="daily", description="Backup frequency")


class APIKeysConfig(BaseModel):
    """Configuration for API keys."""
    alpha_vantage: str = Field(default="", description="Alpha Vantage API key")
    polygon: str = Field(default="", description="Polygon API key")
    quandl: str = Field(default="", description="Quandl API key")
    openai: str = Field(default="", description="OpenAI API key")
    anthropic: str = Field(default="", description="Anthropic API key")
    google_ai: str = Field(default="", description="Google AI API key")
    cohere: str = Field(default="", description="Cohere API key")
    huggingface: str = Field(default="", description="HuggingFace API key")
    # Tier 1 AI Providers
    groq: str = Field(default="", description="Groq API key")
    openrouter: str = Field(default="", description="OpenRouter API key")
    cerebras: str = Field(default="", description="Cerebras API key")
    # Twilio SMS
    twilio_account_sid: str = Field(default="", description="Twilio Account SID")
    twilio_auth_token: str = Field(default="", description="Twilio Auth Token")


class TradingConfigModel(BaseModel):
    """Complete trading configuration model."""
    watchlist: List[str] = Field(default=["AAPL", "GOOGL", "MSFT", "NVDA"], description="List of symbols to watch")
    timeframes: TimeframeConfig = Field(default_factory=TimeframeConfig)
    indicators: IndicatorConfig = Field(default_factory=IndicatorConfig)
    risk_management: RiskManagementConfig = Field(default_factory=RiskManagementConfig)
    trading: TradingConfig = Field(default_factory=TradingConfig)
    market_hours: MarketHoursConfig = Field(default_factory=MarketHoursConfig)
    data_sources: DataSourcesConfig = Field(default_factory=DataSourcesConfig)
    api_keys: APIKeysConfig = Field(default_factory=APIKeysConfig)
    logging: LoggingConfig = Field(default_factory=LoggingConfig)
    notifications: NotificationsConfig = Field(default_factory=NotificationsConfig)
    performance: PerformanceConfig = Field(default_factory=PerformanceConfig)
    ai_analysis: AIAnalysisConfig = Field(default_factory=AIAnalysisConfig)
    database: DatabaseConfig = Field(default_factory=DatabaseConfig)
    
    @validator('watchlist')
    def validate_watchlist(cls, v):
        """Validate watchlist symbols."""
        if not v:
            raise ValueError("Watchlist cannot be empty")
        if len(v) > 100:
            raise ValueError("Watchlist cannot exceed 100 symbols")
        return v
    
    @validator('timeframes')
    def validate_timeframes(cls, v):
        """Validate timeframe configuration."""
        valid_intervals = ['1m', '2m', '5m', '15m', '30m', '60m', '90m', '1h', '1d', '5d', '1wk', '1mo', '3mo']
        if v.interval not in valid_intervals:
            raise ValueError(f"Invalid interval: {v.interval}. Must be one of {valid_intervals}")
        return v


class ConfigManager:
    """
    Configuration manager for the trading platform.
    
    Handles:
    - Loading configuration from files
    - Environment variable overrides
    - Configuration validation
    - Secure credential management
    """
    
    def __init__(self, config_path: str = "config.json"):
        """
        Initialize the configuration manager.
        
        Args:
            config_path: Path to the configuration file
        """
        self.config_path = Path(config_path)
        self.config: Optional[TradingConfigModel] = None
        self._load_environment()
        
    def _load_environment(self):
        """Load environment variables."""
        load_dotenv()
        
    def load_config(self) -> TradingConfigModel:
        """
        Load and validate configuration.
        
        Returns:
            Validated configuration object
        """
        if not self.config_path.exists():
            raise FileNotFoundError(f"Configuration file not found: {self.config_path}")
            
        try:
            with open(self.config_path, 'r') as f:
                config_data = json.load(f)
                
            # Apply environment variable overrides
            config_data = self._apply_env_overrides(config_data)
            
            # Validate configuration
            self.config = TradingConfigModel(**config_data)
            
            return self.config
            
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON in configuration file: {e}")
        except Exception as e:
            raise ValueError(f"Configuration validation failed: {e}")
    
    def _apply_env_overrides(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """Apply environment variable overrides to configuration."""
        env_mappings = {
            'TRADING_PAPER_TRADING': ('trading', 'paper_trading'),
            'TRADING_AUTO_TRADING': ('trading', 'auto_trading'),
            'TRADING_REAL_TIME': ('trading', 'real_time_enabled'),
            'LOGGING_LEVEL': ('logging', 'level'),
            'AI_ANALYSIS_ENABLED': ('ai_analysis', 'enabled'),
            'RISK_MAX_POSITION_SIZE': ('risk_management', 'max_position_size'),
            'RISK_STOP_LOSS': ('risk_management', 'stop_loss_percentage'),
        }
        
        for env_var, config_path in env_mappings.items():
            env_value = os.getenv(env_var)
            if env_value is not None:
                # Navigate to the nested config path
                current = config_data
                for key in config_path[:-1]:
                    if key not in current:
                        current[key] = {}
                    current = current[key]
                
                # Convert string values to appropriate types
                if env_value.lower() in ('true', 'false'):
                    current[config_path[-1]] = env_value.lower() == 'true'
                elif env_value.replace('.', '').isdigit():
                    current[config_path[-1]] = float(env_value)
                else:
                    current[config_path[-1]] = env_value
        
        # Handle API key environment variable substitution
        if 'api_keys' in config_data:
            api_keys = config_data['api_keys']
            for key_name, key_value in api_keys.items():
                if isinstance(key_value, str) and key_value.startswith('${') and key_value.endswith('}'):
                    env_var_name = key_value[2:-1]  # Remove ${ and }
                    env_value = os.getenv(env_var_name)
                    if env_value is not None:
                        api_keys[key_name] = env_value
                    else:
                        # If environment variable not set, use empty string
                        api_keys[key_name] = ""
        
        return config_data
    
    def get_config(self) -> TradingConfigModel:
        """Get the current configuration."""
        if self.config is None:
            self.config = self.load_config()
        return self.config
    
    def save_config(self, config: TradingConfigModel):
        """Save configuration to file."""
        config_dict = config.model_dump()
        
        # Create backup of existing config
        if self.config_path.exists():
            backup_path = self.config_path.with_suffix('.backup.json')
            self.config_path.rename(backup_path)
        
        # Save new config
        with open(self.config_path, 'w') as f:
            json.dump(config_dict, f, indent=4)
    
    def validate_symbols(self, symbols: List[str]) -> List[str]:
        """
        Validate and clean symbol list.
        
        Args:
            symbols: List of symbols to validate
            
        Returns:
            Cleaned list of valid symbols
        """
        valid_symbols = []
        for symbol in symbols:
            # Basic symbol validation
            if symbol and len(symbol) <= 10 and symbol.isalnum():
                valid_symbols.append(symbol.upper())
        
        return valid_symbols


# Global config manager instance
config_manager: Optional[ConfigManager] = None


def get_config() -> TradingConfigModel:
    """Get the global configuration."""
    global config_manager
    if config_manager is None:
        config_manager = ConfigManager()
    return config_manager.get_config()


def setup_config(config_path: str = "config.json") -> ConfigManager:
    """Setup the global configuration manager."""
    global config_manager
    config_manager = ConfigManager(config_path)
    return config_manager
