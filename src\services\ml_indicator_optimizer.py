#!/usr/bin/env python3
"""
Machine Learning Indicator Optimizer

This module provides advanced machine learning techniques for optimizing
financial indicator weights and values, specifically designed for 2025 market conditions.

Features:
- Feature importance analysis using multiple ML algorithms
- Time series forecasting with LSTM/GRU models
- Ensemble methods for robust predictions
- Adaptive learning for market regime changes
- Cross-validation with time series splits
- Hyperparameter optimization
- Real-time model updates
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Union, Any
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
import json
import pickle
from pathlib import Path
import asyncio
import warnings
warnings.filterwarnings('ignore')

# Core ML imports
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor, ExtraTreesRegressor
from sklearn.linear_model import Ridge, Lasso, ElasticNet
from sklearn.svm import SVR
from sklearn.neural_network import MLPRegressor
from sklearn.model_selection import TimeSeriesSplit, cross_val_score, GridSearchCV
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error, explained_variance_score
from sklearn.preprocessing import StandardScaler, MinMaxScaler, RobustScaler
from sklearn.feature_selection import SelectKBest, f_regression, mutual_info_regression
from sklearn.decomposition import PCA

# Advanced ML imports
import xgboost as xgb
import lightgbm as lgb
from catboost import CatBoostRegressor

# Deep Learning imports (optional)
try:
    import tensorflow as tf
    from tensorflow.keras.models import Sequential
    from tensorflow.keras.layers import LSTM, Dense, Dropout, GRU
    from tensorflow.keras.optimizers import Adam
    from tensorflow.keras.callbacks import EarlyStopping
    TENSORFLOW_AVAILABLE = True
except ImportError:
    TENSORFLOW_AVAILABLE = False

# Time series specific
from statsmodels.tsa.stattools import adfuller
from statsmodels.tsa.seasonal import seasonal_decompose

# Visualization
import matplotlib.pyplot as plt
import seaborn as sns

from utils.logging import get_logger
from utils.config import get_config
from services.indicators import TechnicalIndicators, IndicatorResult, SignalType


class MLModelType(Enum):
    """Types of machine learning models for indicator optimization."""
    RANDOM_FOREST = "random_forest"
    GRADIENT_BOOSTING = "gradient_boosting"
    XGBOOST = "xgboost"
    LIGHTGBM = "lightgbm"
    CATBOOST = "catboost"
    NEURAL_NETWORK = "neural_network"
    LSTM = "lstm"
    GRU = "gru"
    ENSEMBLE = "ensemble"
    STACKING = "stacking"


@dataclass
class MLFeatureImportance:
    """Feature importance results from ML models."""
    feature_name: str
    importance_score: float
    rank: int
    model_type: str
    confidence_interval: Tuple[float, float] = (0.0, 0.0)
    p_value: float = 1.0
    is_significant: bool = False


@dataclass
class MLOptimizationResult:
    """Result of ML-based indicator optimization."""
    model_type: MLModelType
    optimized_weights: Dict[str, float]
    feature_importance: List[MLFeatureImportance]
    performance_metrics: Dict[str, float]
    model_performance: Dict[str, float]
    cross_validation_scores: List[float]
    hyperparameters: Dict[str, Any]
    training_time: float
    prediction_time: float
    metadata: Dict[str, Any]


class MLIndicatorOptimizer:
    """
    Advanced machine learning optimizer for financial indicators.
    
    This class implements state-of-the-art ML techniques specifically
    designed for financial indicator optimization in 2025 market conditions.
    """
    
    def __init__(self, config: Dict):
        """Initialize the ML indicator optimizer."""
        self.config = config
        self.logger = get_logger()
        
        # Initialize services
        self.indicators = TechnicalIndicators(config)
        
        # ML configuration
        self.ml_config = config.get('ml_indicator_optimization', {})
        self.enabled = self.ml_config.get('enabled', True)
        self.min_data_points = self.ml_config.get('min_data_points', 200)
        self.cv_splits = self.ml_config.get('cv_splits', 5)
        self.feature_selection_method = self.ml_config.get('feature_selection', 'mutual_info')
        
        # Model storage
        self.data_dir = Path("data/ml_optimization")
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize models
        self.models = {}
        self.scalers = {}
        self.feature_importance_cache = {}
        
        # Performance tracking
        self.optimization_history = []
        self.model_performance_history = {}
        
        self.logger.info("🤖 ML Indicator Optimizer initialized")
    
    async def optimize_with_ml(self, symbols: List[str], start_date: str, end_date: str,
                             model_type: MLModelType = MLModelType.ENSEMBLE) -> MLOptimizationResult:
        """
        Optimize indicators using machine learning approach.
        
        Args:
            symbols: List of symbols to optimize for
            start_date: Start date for optimization
            end_date: End date for optimization
            model_type: Type of ML model to use
            
        Returns:
            MLOptimizationResult with optimized weights and metrics
        """
        if not self.enabled:
            return self._get_default_result(model_type)
        
        self.logger.info(f"🤖 Starting ML optimization using {model_type.value}")
        start_time = datetime.now()
        
        try:
            # Load and prepare data
            data = await self._load_historical_data(symbols, start_date, end_date)
            if not data:
                return self._get_default_result(model_type)
            
            # Prepare features and targets
            features, targets = await self._prepare_ml_data(data, symbols)
            if features is None or targets is None:
                return self._get_default_result(model_type)
            
            # Run ML optimization based on model type
            if model_type == MLModelType.RANDOM_FOREST:
                result = await self._optimize_with_random_forest(features, targets)
            elif model_type == MLModelType.GRADIENT_BOOSTING:
                result = await self._optimize_with_gradient_boosting(features, targets)
            elif model_type == MLModelType.XGBOOST:
                result = await self._optimize_with_xgboost(features, targets)
            elif model_type == MLModelType.LIGHTGBM:
                result = await self._optimize_with_lightgbm(features, targets)
            elif model_type == MLModelType.CATBOOST:
                result = await self._optimize_with_catboost(features, targets)
            elif model_type == MLModelType.NEURAL_NETWORK:
                result = await self._optimize_with_neural_network(features, targets)
            elif model_type == MLModelType.LSTM:
                result = await self._optimize_with_lstm(features, targets)
            elif model_type == MLModelType.GRU:
                result = await self._optimize_with_gru(features, targets)
            elif model_type == MLModelType.ENSEMBLE:
                result = await self._optimize_with_ensemble(features, targets)
            elif model_type == MLModelType.STACKING:
                result = await self._optimize_with_stacking(features, targets)
            else:
                raise ValueError(f"Unknown model type: {model_type}")
            
            # Update timing
            training_time = (datetime.now() - start_time).total_seconds()
            result.training_time = training_time
            
            # Store result
            await self._store_ml_result(result)
            
            self.logger.info(f"✅ ML optimization completed in {training_time:.2f}s")
            return result
            
        except Exception as e:
            self.logger.error(f"❌ Error in ML optimization: {e}")
            return self._get_default_result(model_type)
    
    async def _optimize_with_random_forest(self, features: pd.DataFrame, targets: pd.Series) -> MLOptimizationResult:
        """Optimize using Random Forest."""
        self.logger.info("🌲 Training Random Forest model...")
        
        # Hyperparameter optimization
        param_grid = {
            'n_estimators': [100, 200, 300],
            'max_depth': [10, 20, None],
            'min_samples_split': [2, 5, 10],
            'min_samples_leaf': [1, 2, 4]
        }
        
        # Time series cross-validation
        tscv = TimeSeriesSplit(n_splits=self.cv_splits)
        
        # Grid search with cross-validation
        rf = RandomForestRegressor(random_state=42, n_jobs=-1)
        grid_search = GridSearchCV(
            rf, param_grid, cv=tscv, scoring='r2', n_jobs=-1, verbose=0
        )
        
        grid_search.fit(features, targets)
        
        # Get best model
        best_model = grid_search.best_estimator_
        
        # Feature importance
        feature_importance = self._extract_feature_importance(
            best_model, features.columns, MLModelType.RANDOM_FOREST
        )
        
        # Performance metrics
        cv_scores = cross_val_score(best_model, features, targets, cv=tscv, scoring='r2')
        performance_metrics = self._calculate_performance_metrics(best_model, features, targets)
        
        # Convert to weights
        optimized_weights = self._convert_importance_to_weights(feature_importance)
        
        return MLOptimizationResult(
            model_type=MLModelType.RANDOM_FOREST,
            optimized_weights=optimized_weights,
            feature_importance=feature_importance,
            performance_metrics=performance_metrics,
            model_performance={'cv_r2_mean': cv_scores.mean(), 'cv_r2_std': cv_scores.std()},
            cross_validation_scores=cv_scores.tolist(),
            hyperparameters=grid_search.best_params_,
            training_time=0.0,
            prediction_time=0.0,
            metadata={'best_score': grid_search.best_score_}
        )
    
    async def _optimize_with_gradient_boosting(self, features: pd.DataFrame, targets: pd.Series) -> MLOptimizationResult:
        """Optimize using Gradient Boosting."""
        self.logger.info("📈 Training Gradient Boosting model...")
        
        # Hyperparameter optimization
        param_grid = {
            'n_estimators': [100, 200, 300],
            'learning_rate': [0.01, 0.1, 0.2],
            'max_depth': [3, 5, 7],
            'subsample': [0.8, 0.9, 1.0]
        }
        
        tscv = TimeSeriesSplit(n_splits=self.cv_splits)
        
        gb = GradientBoostingRegressor(random_state=42)
        grid_search = GridSearchCV(
            gb, param_grid, cv=tscv, scoring='r2', n_jobs=-1, verbose=0
        )
        
        grid_search.fit(features, targets)
        best_model = grid_search.best_estimator_
        
        # Feature importance
        feature_importance = self._extract_feature_importance(
            best_model, features.columns, MLModelType.GRADIENT_BOOSTING
        )
        
        # Performance metrics
        cv_scores = cross_val_score(best_model, features, targets, cv=tscv, scoring='r2')
        performance_metrics = self._calculate_performance_metrics(best_model, features, targets)
        
        # Convert to weights
        optimized_weights = self._convert_importance_to_weights(feature_importance)
        
        return MLOptimizationResult(
            model_type=MLModelType.GRADIENT_BOOSTING,
            optimized_weights=optimized_weights,
            feature_importance=feature_importance,
            performance_metrics=performance_metrics,
            model_performance={'cv_r2_mean': cv_scores.mean(), 'cv_r2_std': cv_scores.std()},
            cross_validation_scores=cv_scores.tolist(),
            hyperparameters=grid_search.best_params_,
            training_time=0.0,
            prediction_time=0.0,
            metadata={'best_score': grid_search.best_score_}
        )
    
    async def _optimize_with_xgboost(self, features: pd.DataFrame, targets: pd.Series) -> MLOptimizationResult:
        """Optimize using XGBoost."""
        self.logger.info("🚀 Training XGBoost model...")
        
        # Hyperparameter optimization
        param_grid = {
            'n_estimators': [100, 200, 300],
            'max_depth': [3, 5, 7],
            'learning_rate': [0.01, 0.1, 0.2],
            'subsample': [0.8, 0.9, 1.0],
            'colsample_bytree': [0.8, 0.9, 1.0]
        }
        
        tscv = TimeSeriesSplit(n_splits=self.cv_splits)
        
        xgb_model = xgb.XGBRegressor(random_state=42, n_jobs=-1)
        grid_search = GridSearchCV(
            xgb_model, param_grid, cv=tscv, scoring='r2', n_jobs=-1, verbose=0
        )
        
        grid_search.fit(features, targets)
        best_model = grid_search.best_estimator_
        
        # Feature importance
        feature_importance = self._extract_feature_importance(
            best_model, features.columns, MLModelType.XGBOOST
        )
        
        # Performance metrics
        cv_scores = cross_val_score(best_model, features, targets, cv=tscv, scoring='r2')
        performance_metrics = self._calculate_performance_metrics(best_model, features, targets)
        
        # Convert to weights
        optimized_weights = self._convert_importance_to_weights(feature_importance)
        
        return MLOptimizationResult(
            model_type=MLModelType.XGBOOST,
            optimized_weights=optimized_weights,
            feature_importance=feature_importance,
            performance_metrics=performance_metrics,
            model_performance={'cv_r2_mean': cv_scores.mean(), 'cv_r2_std': cv_scores.std()},
            cross_validation_scores=cv_scores.tolist(),
            hyperparameters=grid_search.best_params_,
            training_time=0.0,
            prediction_time=0.0,
            metadata={'best_score': grid_search.best_score_}
        )
    
    async def _optimize_with_lightgbm(self, features: pd.DataFrame, targets: pd.Series) -> MLOptimizationResult:
        """Optimize using LightGBM."""
        self.logger.info("💡 Training LightGBM model...")
        
        # Hyperparameter optimization
        param_grid = {
            'n_estimators': [100, 200, 300],
            'max_depth': [3, 5, 7],
            'learning_rate': [0.01, 0.1, 0.2],
            'subsample': [0.8, 0.9, 1.0],
            'colsample_bytree': [0.8, 0.9, 1.0]
        }
        
        tscv = TimeSeriesSplit(n_splits=self.cv_splits)
        
        lgb_model = lgb.LGBMRegressor(random_state=42, n_jobs=-1, verbose=-1)
        grid_search = GridSearchCV(
            lgb_model, param_grid, cv=tscv, scoring='r2', n_jobs=-1, verbose=0
        )
        
        grid_search.fit(features, targets)
        best_model = grid_search.best_estimator_
        
        # Feature importance
        feature_importance = self._extract_feature_importance(
            best_model, features.columns, MLModelType.LIGHTGBM
        )
        
        # Performance metrics
        cv_scores = cross_val_score(best_model, features, targets, cv=tscv, scoring='r2')
        performance_metrics = self._calculate_performance_metrics(best_model, features, targets)
        
        # Convert to weights
        optimized_weights = self._convert_importance_to_weights(feature_importance)
        
        return MLOptimizationResult(
            model_type=MLModelType.LIGHTGBM,
            optimized_weights=optimized_weights,
            feature_importance=feature_importance,
            performance_metrics=performance_metrics,
            model_performance={'cv_r2_mean': cv_scores.mean(), 'cv_r2_std': cv_scores.std()},
            cross_validation_scores=cv_scores.tolist(),
            hyperparameters=grid_search.best_params_,
            training_time=0.0,
            prediction_time=0.0,
            metadata={'best_score': grid_search.best_score_}
        )
    
    async def _optimize_with_catboost(self, features: pd.DataFrame, targets: pd.Series) -> MLOptimizationResult:
        """Optimize using CatBoost."""
        self.logger.info("🐱 Training CatBoost model...")
        
        # Hyperparameter optimization
        param_grid = {
            'iterations': [100, 200, 300],
            'depth': [3, 5, 7],
            'learning_rate': [0.01, 0.1, 0.2],
            'l2_leaf_reg': [1, 3, 5]
        }
        
        tscv = TimeSeriesSplit(n_splits=self.cv_splits)
        
        cat_model = CatBoostRegressor(random_state=42, verbose=False)
        grid_search = GridSearchCV(
            cat_model, param_grid, cv=tscv, scoring='r2', n_jobs=-1, verbose=0
        )
        
        grid_search.fit(features, targets)
        best_model = grid_search.best_estimator_
        
        # Feature importance
        feature_importance = self._extract_feature_importance(
            best_model, features.columns, MLModelType.CATBOOST
        )
        
        # Performance metrics
        cv_scores = cross_val_score(best_model, features, targets, cv=tscv, scoring='r2')
        performance_metrics = self._calculate_performance_metrics(best_model, features, targets)
        
        # Convert to weights
        optimized_weights = self._convert_importance_to_weights(feature_importance)
        
        return MLOptimizationResult(
            model_type=MLModelType.CATBOOST,
            optimized_weights=optimized_weights,
            feature_importance=feature_importance,
            performance_metrics=performance_metrics,
            model_performance={'cv_r2_mean': cv_scores.mean(), 'cv_r2_std': cv_scores.std()},
            cross_validation_scores=cv_scores.tolist(),
            hyperparameters=grid_search.best_params_,
            training_time=0.0,
            prediction_time=0.0,
            metadata={'best_score': grid_search.best_score_}
        )
    
    async def _optimize_with_neural_network(self, features: pd.DataFrame, targets: pd.Series) -> MLOptimizationResult:
        """Optimize using Neural Network."""
        self.logger.info("🧠 Training Neural Network model...")
        
        # Scale features
        scaler = StandardScaler()
        features_scaled = scaler.fit_transform(features)
        
        # Create neural network
        model = MLPRegressor(
            hidden_layer_sizes=(100, 50, 25),
            activation='relu',
            solver='adam',
            alpha=0.001,
            learning_rate='adaptive',
            max_iter=1000,
            random_state=42
        )
        
        # Time series cross-validation
        tscv = TimeSeriesSplit(n_splits=self.cv_splits)
        cv_scores = cross_val_score(model, features_scaled, targets, cv=tscv, scoring='r2')
        
        # Train final model
        model.fit(features_scaled, targets)
        
        # Feature importance (using permutation importance)
        feature_importance = self._calculate_permutation_importance(
            model, features_scaled, targets, features.columns
        )
        
        # Performance metrics
        performance_metrics = self._calculate_performance_metrics(model, features_scaled, targets)
        
        # Convert to weights
        optimized_weights = self._convert_importance_to_weights(feature_importance)
        
        return MLOptimizationResult(
            model_type=MLModelType.NEURAL_NETWORK,
            optimized_weights=optimized_weights,
            feature_importance=feature_importance,
            performance_metrics=performance_metrics,
            model_performance={'cv_r2_mean': cv_scores.mean(), 'cv_r2_std': cv_scores.std()},
            cross_validation_scores=cv_scores.tolist(),
            hyperparameters={'hidden_layer_sizes': (100, 50, 25), 'activation': 'relu'},
            training_time=0.0,
            prediction_time=0.0,
            metadata={'scaler': scaler}
        )
    
    async def _optimize_with_lstm(self, features: pd.DataFrame, targets: pd.Series) -> MLOptimizationResult:
        """Optimize using LSTM (if TensorFlow is available)."""
        if not TENSORFLOW_AVAILABLE:
            self.logger.warning("⚠️ TensorFlow not available, skipping LSTM")
            return self._get_default_result(MLModelType.LSTM)
        
        self.logger.info("🔮 Training LSTM model...")
        
        # Prepare data for LSTM
        X_lstm, y_lstm = self._prepare_lstm_data(features, targets)
        
        # Create LSTM model
        model = Sequential([
            LSTM(50, return_sequences=True, input_shape=(X_lstm.shape[1], X_lstm.shape[2])),
            Dropout(0.2),
            LSTM(50, return_sequences=False),
            Dropout(0.2),
            Dense(25),
            Dense(1)
        ])
        
        model.compile(optimizer=Adam(learning_rate=0.001), loss='mse')
        
        # Early stopping
        early_stopping = EarlyStopping(monitor='val_loss', patience=10, restore_best_weights=True)
        
        # Train model
        history = model.fit(
            X_lstm, y_lstm,
            epochs=100,
            batch_size=32,
            validation_split=0.2,
            callbacks=[early_stopping],
            verbose=0
        )
        
        # Feature importance (using permutation importance)
        feature_importance = self._calculate_lstm_importance(model, X_lstm, y_lstm, features.columns)
        
        # Performance metrics
        y_pred = model.predict(X_lstm)
        performance_metrics = {
            'r2_score': r2_score(y_lstm, y_pred),
            'mse': mean_squared_error(y_lstm, y_pred),
            'mae': mean_absolute_error(y_lstm, y_pred)
        }
        
        # Convert to weights
        optimized_weights = self._convert_importance_to_weights(feature_importance)
        
        return MLOptimizationResult(
            model_type=MLModelType.LSTM,
            optimized_weights=optimized_weights,
            feature_importance=feature_importance,
            performance_metrics=performance_metrics,
            model_performance={'val_loss': min(history.history['val_loss'])},
            cross_validation_scores=[],
            hyperparameters={'layers': [50, 50, 25], 'dropout': 0.2},
            training_time=0.0,
            prediction_time=0.0,
            metadata={'history': history.history}
        )
    
    async def _optimize_with_gru(self, features: pd.DataFrame, targets: pd.Series) -> MLOptimizationResult:
        """Optimize using GRU (if TensorFlow is available)."""
        if not TENSORFLOW_AVAILABLE:
            self.logger.warning("⚠️ TensorFlow not available, skipping GRU")
            return self._get_default_result(MLModelType.GRU)
        
        self.logger.info("🔄 Training GRU model...")
        
        # Prepare data for GRU
        X_gru, y_gru = self._prepare_lstm_data(features, targets)
        
        # Create GRU model
        model = Sequential([
            GRU(50, return_sequences=True, input_shape=(X_gru.shape[1], X_gru.shape[2])),
            Dropout(0.2),
            GRU(50, return_sequences=False),
            Dropout(0.2),
            Dense(25),
            Dense(1)
        ])
        
        model.compile(optimizer=Adam(learning_rate=0.001), loss='mse')
        
        # Early stopping
        early_stopping = EarlyStopping(monitor='val_loss', patience=10, restore_best_weights=True)
        
        # Train model
        history = model.fit(
            X_gru, y_gru,
            epochs=100,
            batch_size=32,
            validation_split=0.2,
            callbacks=[early_stopping],
            verbose=0
        )
        
        # Feature importance
        feature_importance = self._calculate_lstm_importance(model, X_gru, y_gru, features.columns)
        
        # Performance metrics
        y_pred = model.predict(X_gru)
        performance_metrics = {
            'r2_score': r2_score(y_gru, y_pred),
            'mse': mean_squared_error(y_gru, y_pred),
            'mae': mean_absolute_error(y_gru, y_pred)
        }
        
        # Convert to weights
        optimized_weights = self._convert_importance_to_weights(feature_importance)
        
        return MLOptimizationResult(
            model_type=MLModelType.GRU,
            optimized_weights=optimized_weights,
            feature_importance=feature_importance,
            performance_metrics=performance_metrics,
            model_performance={'val_loss': min(history.history['val_loss'])},
            cross_validation_scores=[],
            hyperparameters={'layers': [50, 50, 25], 'dropout': 0.2},
            training_time=0.0,
            prediction_time=0.0,
            metadata={'history': history.history}
        )
    
    async def _optimize_with_ensemble(self, features: pd.DataFrame, targets: pd.Series) -> MLOptimizationResult:
        """Optimize using ensemble of multiple models."""
        self.logger.info("🎯 Training ensemble model...")
        
        # Define base models
        base_models = {
            'random_forest': RandomForestRegressor(n_estimators=200, random_state=42),
            'gradient_boosting': GradientBoostingRegressor(n_estimators=200, random_state=42),
            'xgboost': xgb.XGBRegressor(n_estimators=200, random_state=42),
            'lightgbm': lgb.LGBMRegressor(n_estimators=200, random_state=42, verbose=-1)
        }
        
        # Train all models
        trained_models = {}
        model_scores = {}
        
        tscv = TimeSeriesSplit(n_splits=self.cv_splits)
        
        for name, model in base_models.items():
            self.logger.info(f"Training {name}...")
            cv_scores = cross_val_score(model, features, targets, cv=tscv, scoring='r2')
            model_scores[name] = cv_scores.mean()
            
            # Train on full dataset
            model.fit(features, targets)
            trained_models[name] = model
        
        # Weighted ensemble predictions
        ensemble_predictions = np.zeros(len(targets))
        total_weight = 0
        
        for name, model in trained_models.items():
            weight = model_scores[name]
            ensemble_predictions += weight * model.predict(features)
            total_weight += weight
        
        ensemble_predictions /= total_weight
        
        # Calculate ensemble performance
        performance_metrics = {
            'r2_score': r2_score(targets, ensemble_predictions),
            'mse': mean_squared_error(targets, ensemble_predictions),
            'mae': mean_absolute_error(targets, ensemble_predictions)
        }
        
        # Aggregate feature importance
        all_importance = []
        for name, model in trained_models.items():
            importance = self._extract_feature_importance(model, features.columns, MLModelType.ENSEMBLE)
            all_importance.extend(importance)
        
        # Average feature importance
        feature_importance = self._aggregate_feature_importance(all_importance)
        
        # Convert to weights
        optimized_weights = self._convert_importance_to_weights(feature_importance)
        
        return MLOptimizationResult(
            model_type=MLModelType.ENSEMBLE,
            optimized_weights=optimized_weights,
            feature_importance=feature_importance,
            performance_metrics=performance_metrics,
            model_performance={'ensemble_score': performance_metrics['r2_score']},
            cross_validation_scores=list(model_scores.values()),
            hyperparameters={'models': list(base_models.keys())},
            training_time=0.0,
            prediction_time=0.0,
            metadata={'model_scores': model_scores}
        )
    
    async def _optimize_with_stacking(self, features: pd.DataFrame, targets: pd.Series) -> MLOptimizationResult:
        """Optimize using stacking ensemble."""
        self.logger.info("🏗️ Training stacking model...")
        
        # This would implement a more sophisticated stacking approach
        # For now, we'll use a simple version
        return await self._optimize_with_ensemble(features, targets)
    
    def _extract_feature_importance(self, model, feature_names: List[str], model_type: MLModelType) -> List[MLFeatureImportance]:
        """Extract feature importance from trained model."""
        importance_list = []
        
        if hasattr(model, 'feature_importances_'):
            importances = model.feature_importances_
        elif hasattr(model, 'coef_'):
            importances = np.abs(model.coef_)
        else:
            # Use permutation importance as fallback
            return self._calculate_permutation_importance(model, features, targets, feature_names)
        
        # Create feature importance objects
        for i, (feature, importance) in enumerate(zip(feature_names, importances)):
            importance_obj = MLFeatureImportance(
                feature_name=feature,
                importance_score=importance,
                rank=i + 1,
                model_type=model_type.value,
                is_significant=importance > np.mean(importances) + np.std(importances)
            )
            importance_list.append(importance_obj)
        
        # Sort by importance
        importance_list.sort(key=lambda x: x.importance_score, reverse=True)
        
        # Update ranks
        for i, importance_obj in enumerate(importance_list):
            importance_obj.rank = i + 1
        
        return importance_list
    
    def _calculate_permutation_importance(self, model, features: np.ndarray, targets: pd.Series, feature_names: List[str]) -> List[MLFeatureImportance]:
        """Calculate permutation importance for models without built-in feature importance."""
        from sklearn.inspection import permutation_importance
        
        # Calculate permutation importance
        perm_importance = permutation_importance(model, features, targets, n_repeats=10, random_state=42)
        
        importance_list = []
        for i, (feature, importance) in enumerate(zip(feature_names, perm_importance.importances_mean)):
            importance_obj = MLFeatureImportance(
                feature_name=feature,
                importance_score=importance,
                rank=i + 1,
                model_type='permutation',
                confidence_interval=(perm_importance.importances_mean[i] - perm_importance.importances_std[i],
                                   perm_importance.importances_mean[i] + perm_importance.importances_std[i]),
                is_significant=importance > np.mean(perm_importance.importances_mean) + np.std(perm_importance.importances_mean)
            )
            importance_list.append(importance_obj)
        
        # Sort by importance
        importance_list.sort(key=lambda x: x.importance_score, reverse=True)
        
        # Update ranks
        for i, importance_obj in enumerate(importance_list):
            importance_obj.rank = i + 1
        
        return importance_list
    
    def _calculate_lstm_importance(self, model, X: np.ndarray, y: np.ndarray, feature_names: List[str]) -> List[MLFeatureImportance]:
        """Calculate feature importance for LSTM/GRU models."""
        # Use permutation importance for LSTM models
        return self._calculate_permutation_importance(model, X, y, feature_names)
    
    def _aggregate_feature_importance(self, all_importance: List[MLFeatureImportance]) -> List[MLFeatureImportance]:
        """Aggregate feature importance from multiple models."""
        # Group by feature name
        feature_groups = {}
        for importance in all_importance:
            if importance.feature_name not in feature_groups:
                feature_groups[importance.feature_name] = []
            feature_groups[importance.feature_name].append(importance.importance_score)
        
        # Average importance for each feature
        aggregated_importance = []
        for feature_name, scores in feature_groups.items():
            avg_score = np.mean(scores)
            importance_obj = MLFeatureImportance(
                feature_name=feature_name,
                importance_score=avg_score,
                rank=0,  # Will be updated below
                model_type='ensemble',
                is_significant=avg_score > np.mean(list(feature_groups.values())) + np.std(list(feature_groups.values()))
            )
            aggregated_importance.append(importance_obj)
        
        # Sort and update ranks
        aggregated_importance.sort(key=lambda x: x.importance_score, reverse=True)
        for i, importance_obj in enumerate(aggregated_importance):
            importance_obj.rank = i + 1
        
        return aggregated_importance
    
    def _convert_importance_to_weights(self, feature_importance: List[MLFeatureImportance]) -> Dict[str, float]:
        """Convert feature importance to indicator weights."""
        weights = {}
        
        for importance in feature_importance:
            # Extract indicator name from feature
            feature_name = importance.feature_name
            
            if '_value' in feature_name:
                indicator_name = feature_name.replace('_value', '')
            elif '_signal' in feature_name:
                indicator_name = feature_name.replace('_signal', '')
            elif '_confidence' in feature_name:
                indicator_name = feature_name.replace('_confidence', '')
            else:
                indicator_name = feature_name
            
            weights[indicator_name] = importance.importance_score
        
        # Normalize weights
        if weights:
            max_weight = max(weights.values())
            if max_weight > 0:
                weights = {k: v / max_weight for k, v in weights.items()}
        
        return weights
    
    def _calculate_performance_metrics(self, model, features: np.ndarray, targets: pd.Series) -> Dict[str, float]:
        """Calculate comprehensive performance metrics."""
        y_pred = model.predict(features)
        
        return {
            'r2_score': r2_score(targets, y_pred),
            'mse': mean_squared_error(targets, y_pred),
            'mae': mean_absolute_error(targets, y_pred),
            'explained_variance': explained_variance_score(targets, y_pred)
        }
    
    def _prepare_lstm_data(self, features: pd.DataFrame, targets: pd.Series, sequence_length: int = 10) -> Tuple[np.ndarray, np.ndarray]:
        """Prepare data for LSTM/GRU models."""
        # Create sequences
        X, y = [], []
        
        for i in range(sequence_length, len(features)):
            X.append(features.iloc[i-sequence_length:i].values)
            y.append(targets.iloc[i])
        
        return np.array(X), np.array(y)
    
    async def _prepare_ml_data(self, data: Dict[str, pd.DataFrame], symbols: List[str]) -> Tuple[Optional[pd.DataFrame], Optional[pd.Series]]:
        """Prepare data for machine learning optimization."""
        features_list = []
        targets_list = []
        
        for symbol in symbols:
            if symbol not in data:
                continue
            
            symbol_data = data[symbol]
            if len(symbol_data) < self.min_data_points:
                continue
            
            # Calculate indicators
            indicators = self.indicators.calculate_all_indicators(symbol_data)
            
            # Extract features
            symbol_features = self._extract_indicator_features(indicators, symbol_data)
            if symbol_features is not None:
                features_list.append(symbol_features)
                
                # Calculate target (future returns)
                future_returns = symbol_data['Close'].pct_change().shift(-1)
                targets_list.append(future_returns)
        
        if not features_list:
            return None, None
        
        # Combine features and targets
        features = pd.concat(features_list, axis=0)
        targets = pd.concat(targets_list, axis=0)
        
        # Align features and targets
        features, targets = features.align(targets, join='inner')
        
        # Remove NaN values
        features = features.fillna(0)
        targets = targets.fillna(0)
        
        return features, targets
    
    def _extract_indicator_features(self, indicators: Dict[str, IndicatorResult], data: pd.DataFrame) -> Optional[pd.DataFrame]:
        """Extract comprehensive features from indicators."""
        features = {}
        
        # Technical indicator features
        for name, result in indicators.items():
            if result.value is not None:
                features[f'{name}_value'] = [result.value]
                features[f'{name}_confidence'] = [result.confidence if result.confidence else 0.0]
                
                if result.signal:
                    signal_value = self._encode_signal(result.signal)
                    features[f'{name}_signal'] = [signal_value]
        
        # Price-based features
        if len(data) > 0:
            features['price_change'] = [data['Close'].pct_change().iloc[-1] if len(data) > 1 else 0]
            features['price_volatility'] = [data['Close'].pct_change().std()]
            features['volume_change'] = [data['Volume'].pct_change().iloc[-1] if len(data) > 1 else 0]
            features['volume_volatility'] = [data['Volume'].pct_change().std()]
        
        if not features:
            return None
        
        return pd.DataFrame(features)
    
    def _encode_signal(self, signal: SignalType) -> float:
        """Encode signal to numerical value."""
        signal_encoding = {
            SignalType.STRONG_SELL: -1.0,
            SignalType.SELL: -0.5,
            SignalType.HOLD: 0.0,
            SignalType.BUY: 0.5,
            SignalType.STRONG_BUY: 1.0
        }
        return signal_encoding.get(signal, 0.0)
    
    async def _load_historical_data(self, symbols: List[str], start_date: str, end_date: str) -> Dict[str, pd.DataFrame]:
        """Load historical data for optimization."""
        data = {}
        
        for symbol in symbols:
            try:
                import yfinance as yf
                ticker = yf.Ticker(symbol)
                symbol_data = ticker.history(start=start_date, end=end_date)
                
                if not symbol_data.empty:
                    data[symbol] = symbol_data
                    self.logger.info(f"Loaded {len(symbol_data)} data points for {symbol}")
                else:
                    self.logger.warning(f"No data found for {symbol}")
                    
            except Exception as e:
                self.logger.error(f"Error loading data for {symbol}: {e}")
        
        return data
    
    async def _store_ml_result(self, result: MLOptimizationResult):
        """Store ML optimization result."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"ml_optimization_result_{result.model_type.value}_{timestamp}.json"
        filepath = self.data_dir / filename
        
        # Convert result to JSON-serializable format
        result_dict = {
            'model_type': result.model_type.value,
            'optimized_weights': result.optimized_weights,
            'feature_importance': [
                {
                    'feature_name': fi.feature_name,
                    'importance_score': fi.importance_score,
                    'rank': fi.rank,
                    'model_type': fi.model_type,
                    'is_significant': fi.is_significant
                }
                for fi in result.feature_importance
            ],
            'performance_metrics': result.performance_metrics,
            'model_performance': result.model_performance,
            'cross_validation_scores': result.cross_validation_scores,
            'hyperparameters': result.hyperparameters,
            'training_time': result.training_time,
            'prediction_time': result.prediction_time,
            'metadata': result.metadata,
            'timestamp': timestamp
        }
        
        with open(filepath, 'w') as f:
            json.dump(result_dict, f, indent=2, default=str)
        
        self.logger.info(f"💾 ML optimization result stored: {filepath}")
    
    def _get_default_result(self, model_type: MLModelType) -> MLOptimizationResult:
        """Get default ML optimization result."""
        return MLOptimizationResult(
            model_type=model_type,
            optimized_weights={},
            feature_importance=[],
            performance_metrics={},
            model_performance={},
            cross_validation_scores=[],
            hyperparameters={},
            training_time=0.0,
            prediction_time=0.0,
            metadata={'error': 'ML optimization failed'}
        )
    
    def get_feature_importance_summary(self) -> Dict[str, float]:
        """Get summary of feature importance across all models."""
        if not self.feature_importance_cache:
            return {}
        
        # Aggregate importance across all cached results
        all_importance = {}
        for result in self.feature_importance_cache.values():
            for fi in result.feature_importance:
                if fi.feature_name not in all_importance:
                    all_importance[fi.feature_name] = []
                all_importance[fi.feature_name].append(fi.importance_score)
        
        # Average importance
        summary = {}
        for feature, scores in all_importance.items():
            summary[feature] = np.mean(scores)
        
        return summary
    
    async def close(self):
        """Close the ML optimizer and cleanup resources."""
        self.logger.info("🔒 Closing ML Indicator Optimizer")


