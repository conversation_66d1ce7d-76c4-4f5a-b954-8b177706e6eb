#!/usr/bin/env python3
"""
Test script to debug signal generation
"""

import sys
import os
from datetime import datetime
import pandas as pd

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '..', 'src'))

from services.signals import SignalGenerator, SignalStrength
from services.indicators import TechnicalIndicators
from utils.config import get_config

def test_signal_generation():
    """Test signal generation for a single symbol."""
    print("🧪 Testing Signal Generation")
    print("=" * 40)
    
    # Get configuration
    config = get_config()
    config_dict = config.model_dump()
    
    # Initialize services
    signal_generator = SignalGenerator(config_dict)
    indicators = TechnicalIndicators(config_dict)
    
    # Test with AAPL data
    symbol = "AAPL"
    print(f"🔍 Testing signal generation for {symbol}")
    
    # Get some sample data (you would normally get this from your market data service)
    import yfinance as yf
    
    # Download data for the last 100 days
    data = yf.download(symbol, start="2022-01-01", end="2023-12-31", progress=False)
    
    if data.empty:
        print(f"❌ No data available for {symbol}")
        return
    
    print(f"📊 Data shape: {data.shape}")
    print(f"📈 Date range: {data.index[0]} to {data.index[-1]}")
    print(f"💰 Latest price: ${float(data['Close'].iloc[-1]):.2f}")
    
    # Check data validation manually
    print(f"\n🔍 Data Validation:")
    print(f"   Length: {len(data)} (need 50+)")
    print(f"   Columns: {list(data.columns)}")
    print(f"   Required columns: ['Open', 'High', 'Low', 'Close', 'Volume']")
    
    # Handle multi-level columns from yfinance
    if isinstance(data.columns, pd.MultiIndex):
        # Flatten the columns
        data.columns = data.columns.get_level_values(0)
        print(f"   Flattened columns: {list(data.columns)}")
    
    print(f"   Has required columns: {all(col in data.columns for col in ['Open', 'High', 'Low', 'Close', 'Volume'])}")
    print(f"   Has negative values: {(data['Open'] < 0).any() or (data['Close'] < 0).any()}")
    print(f"   Has NaN values: {data.isnull().any().any()}")
    print(f"   High < Low: {(data['High'] < data['Low']).any()}")
    
    try:
        # Test multiple dates to find MODERATE+ signals
        print(f"\n🔍 Testing multiple dates for MODERATE+ signals:")
        moderate_signals = 0
        total_signals = 0
        
        # Test every 30 days
        for i in range(50, len(data), 30):
            test_data = data.iloc[:i+1]
            if len(test_data) < 50:
                continue
                
            try:
                signal = signal_generator.generate_signal(symbol, test_data)
                if signal:
                    total_signals += 1
                    if signal.strength in [SignalStrength.MODERATE, SignalStrength.STRONG, SignalStrength.VERY_STRONG]:
                        moderate_signals += 1
                        print(f"   {test_data.index[-1].strftime('%Y-%m-%d')}: {signal.strength.value} {signal.signal_type.value} (confidence: {signal.confidence:.2f})")
            except Exception as e:
                continue
        
        print(f"\n📊 Signal Summary:")
        print(f"   Total signals tested: {total_signals}")
        print(f"   MODERATE+ signals found: {moderate_signals}")
        
        # Test the latest data
        print(f"\n🔍 Latest Signal:")
        signal = signal_generator.generate_signal(symbol, data)
        
        if signal:
            print(f"✅ Signal Generated:")
            print(f"   Symbol: {signal.symbol}")
            print(f"   Signal Type: {signal.signal_type.value}")
            print(f"   Strength: {signal.strength.value}")
            print(f"   Confidence: {signal.confidence:.2f}")
            print(f"   Price: ${signal.price:.2f}")
            print(f"   Market Condition: {signal.market_condition.value}")
            print(f"   Volume Support: {signal.volume_support}")
            print(f"   Trend Alignment: {signal.trend_alignment}")
            
            # Check if it's a MODERATE+ signal
            if signal.strength in [SignalStrength.MODERATE, SignalStrength.STRONG, SignalStrength.VERY_STRONG]:
                print(f"🎯 This is a {signal.strength.value} signal - would be processed!")
            else:
                print(f"⏭️ This is a {signal.strength.value} signal - too weak for processing")
        else:
            print(f"❌ No signal generated for {symbol}")
            
    except Exception as e:
        print(f"❌ Error generating signal: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_signal_generation() 