#!/usr/bin/env python3
"""
Test script to run analysis for all symbols.
"""

import json
import asyncio
from pathlib import Path

def test_config():
    """Test the configuration loading."""
    try:
        with open('config.json', 'r') as f:
            config = json.load(f)
        
        print("✅ Configuration loaded successfully")
        print(f"📊 Total symbols: {len(config['watchlist'])}")
        print(f"🔧 Stop at Tier 1: {config['ai_analysis']['stop_at_tier_1']}")
        print(f"🔧 Tier 2 threshold: {config['ai_analysis']['tier_2_threshold']}")
        
        # Show first 10 symbols
        print(f"📋 First 10 symbols: {config['watchlist'][:10]}")
        
        return True
    except Exception as e:
        print(f"❌ Error loading config: {e}")
        return False

def main():
    """Main function."""
    print("🚀 Testing configuration for all symbols analysis")
    print("=" * 50)
    
    if test_config():
        print("\n✅ Configuration is ready for all symbols analysis!")
        print("\n📊 Summary:")
        print("- 66 symbols in watchlist")
        print("- Stop at Tier 1: True (will show what would be sent to Tier 2)")
        print("- Tier 2 threshold: 0.75")
        print("- Free Tier 1 providers: Google AI, Groq, OpenRouter")
        print("- Cost: $0 (only uses free Tier 1 providers)")
        
        print("\n🔧 To run analysis for all symbols:")
        print("1. Fix the syntax errors in src/services/ai_advisor.py")
        print("2. Run: python analyze_manual.py --all --force-realtime --output txt")
        print("\n⏱️  Estimated time: 2-4 minutes for all 66 symbols")
        print("💾 Estimated output: ~11MB of JSON logs")
    else:
        print("❌ Configuration test failed")

if __name__ == "__main__":
    main() 