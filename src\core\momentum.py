import pandas as pd
import numpy as np
import logging

class MomentumCalculator:
    """
    Calculates technical indicators for momentum trading.
    
    Technical Implementation:
    - Uses vectorized operations for performance
    - Implements standard technical analysis formulas
    - Returns normalized indicator values
    
    Financial Theory:
    - Combines trend-following (MACD) with momentum (RSI) indicators
    - Helps identify potential reversals and trend continuations
    - Used for both entry and exit signals
    """

    @staticmethod
    def calculate_rsi(data: pd.DataFrame, period: int = 14) -> pd.Series:
        """
        Calculate Relative Strength Index.
        
        Technical Notes:
        - Uses exponential averages for smoother signals
        - Handles zero-division cases
        
        Financial Context:
        - RSI > 70: Potentially overbought
        - RSI < 30: Potentially oversold
        - Crossovers of 50 indicate momentum shifts
        """
        try:
            # Validate input data
            if data is None or data.empty:
                return pd.Series(dtype=float)
            
            if 'Close' not in data.columns:
                return pd.Series(dtype=float)
            
            # Check for sufficient data
            if len(data) < period + 1:
                return pd.Series(dtype=float)
            
            # Calculate price changes
            delta = data['Close'].diff()
            
            # Handle NaN values
            if delta.isnull().all():
                return pd.Series(dtype=float)
            
            # Calculate gains and losses
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            
            # Handle division by zero
            loss = loss.replace(0, np.nan)
            rs = gain / loss
            
            # Calculate RSI
            rsi = 100 - (100 / (1 + rs))
            
            # Handle infinite values
            rsi = rsi.replace([np.inf, -np.inf], np.nan)
            
            return rsi
            
        except Exception as e:
            logging.error(f"Error calculating RSI: {e}")
            return pd.Series(dtype=float)

    @staticmethod
    def calculate_macd(data: pd.DataFrame, fast=12, slow=26, signal=9) -> dict:
        """
        Calculate Moving Average Convergence Divergence.
        
        Technical Implementation:
        - Uses exponential moving averages (EMA)
        - Returns dictionary of MACD components
        
        Financial Analysis:
        - Fast EMA (12) captures recent price action
        - Slow EMA (26) identifies overall trend
        - Signal line (9) generates trading signals
        - Histogram shows momentum strength
        """
        try:
            # Validate input data
            if data is None or data.empty:
                return {'macd': pd.Series(dtype=float), 'signal': pd.Series(dtype=float), 'histogram': pd.Series(dtype=float)}
            
            if 'Close' not in data.columns:
                return {'macd': pd.Series(dtype=float), 'signal': pd.Series(dtype=float), 'histogram': pd.Series(dtype=float)}
            
            # Check for sufficient data
            min_required = max(slow, fast + signal) + 1
            if len(data) < min_required:
                return {'macd': pd.Series(dtype=float), 'signal': pd.Series(dtype=float), 'histogram': pd.Series(dtype=float)}
            
            # Validate parameters
            if fast <= 0 or slow <= 0 or signal <= 0:
                return {'macd': pd.Series(dtype=float), 'signal': pd.Series(dtype=float), 'histogram': pd.Series(dtype=float)}
            
            if fast >= slow:
                return {'macd': pd.Series(dtype=float), 'signal': pd.Series(dtype=float), 'histogram': pd.Series(dtype=float)}
            
            # Calculate EMAs
            exp1 = data['Close'].ewm(span=fast, adjust=False).mean()
            exp2 = data['Close'].ewm(span=slow, adjust=False).mean()
            
            # Handle NaN values
            if exp1.isnull().all() or exp2.isnull().all():
                return {'macd': pd.Series(dtype=float), 'signal': pd.Series(dtype=float), 'histogram': pd.Series(dtype=float)}
            
            # Calculate MACD line
            macd = exp1 - exp2
            
            # Handle infinite values
            macd = macd.replace([np.inf, -np.inf], np.nan)
            
            # Calculate signal line
            signal_line = macd.ewm(span=signal, adjust=False).mean()
            
            # Handle infinite values in signal line
            signal_line = signal_line.replace([np.inf, -np.inf], np.nan)
            
            # Calculate histogram
            histogram = macd - signal_line
            
            # Handle infinite values in histogram
            histogram = histogram.replace([np.inf, -np.inf], np.nan)
            
            return {
                'macd': macd,
                'signal': signal_line,
                'histogram': histogram
            }
            
        except Exception as e:
            logging.error(f"Error calculating MACD: {e}")
            return {'macd': pd.Series(dtype=float), 'signal': pd.Series(dtype=float), 'histogram': pd.Series(dtype=float)}

    @staticmethod
    def is_bullish_momentum(rsi: float, macd_data: dict) -> bool:
        """
        Identify bullish momentum conditions.
        
        Technical Check:
        - RSI above centerline (50)
        - MACD above signal line
        
        Financial Significance:
        - Indicates potential upward trend continuation
        - Stronger signal when both indicators align
        """
        try:
            # Validate RSI value
            if pd.isna(rsi) or np.isinf(rsi):
                return False
            
            # Validate MACD data
            if not isinstance(macd_data, dict):
                return False
            
            if 'macd' not in macd_data or 'signal' not in macd_data:
                return False
            
            macd_series = macd_data['macd']
            signal_series = macd_data['signal']
            
            # Check if series are empty or all NaN
            if macd_series.empty or signal_series.empty:
                return False
            
            if macd_series.isnull().all() or signal_series.isnull().all():
                return False
            
            # Get latest values
            latest_macd = macd_series.iloc[-1]
            latest_signal = signal_series.iloc[-1]
            
            # Handle NaN values
            if pd.isna(latest_macd) or pd.isna(latest_signal):
                return False
            
            # Check bullish conditions
            return (rsi > 50 and latest_macd > latest_signal)
            
        except Exception as e:
            logging.error(f"Error checking bullish momentum: {e}")
            return False

    @staticmethod
    def is_bearish_momentum(rsi: float, macd_data: dict) -> bool:
        """
        Identify bearish momentum conditions.
        
        Technical Check:
        - RSI below centerline (50)
        - MACD below signal line
        
        Financial Significance:
        - Indicates potential downward trend continuation
        - Risk increases when both indicators align bearish
        """
        try:
            # Validate RSI value
            if pd.isna(rsi) or np.isinf(rsi):
                return False
            
            # Validate MACD data
            if not isinstance(macd_data, dict):
                return False
            
            if 'macd' not in macd_data or 'signal' not in macd_data:
                return False
            
            macd_series = macd_data['macd']
            signal_series = macd_data['signal']
            
            # Check if series are empty or all NaN
            if macd_series.empty or signal_series.empty:
                return False
            
            if macd_series.isnull().all() or signal_series.isnull().all():
                return False
            
            # Get latest values
            latest_macd = macd_series.iloc[-1]
            latest_signal = signal_series.iloc[-1]
            
            # Handle NaN values
            if pd.isna(latest_macd) or pd.isna(latest_signal):
                return False
            
            # Check bearish conditions
            return (rsi < 50 and latest_macd < latest_signal)
            
        except Exception as e:
            logging.error(f"Error checking bearish momentum: {e}")
            return False 