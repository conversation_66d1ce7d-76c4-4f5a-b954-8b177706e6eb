#!/usr/bin/env python3
"""
AI-Nvestor Project Setup Script

This script sets up the complete trading platform environment including:
- Virtual environment creation
- Dependency installation
- Configuration setup
- Directory structure creation
- Database initialization
"""

import os
import sys
import subprocess
import json
from pathlib import Path
from typing import List, Dict, Optional


class ProjectSetup:
    """Professional project setup for AI-Nvestor."""
    
    def __init__(self):
        self.project_root = Path.cwd()
        self.venv_path = self.project_root / "venv"
        self.requirements_file = self.project_root / "requirements.txt"
        self.config_file = self.project_root / "config.json"
        
    def setup_environment(self):
        """Complete project setup."""
        print("🚀 Setting up AI-Nvestor Trading Platform...")
        print("="*60)
        
        # Check Python version
        self._check_python_version()
        
        # Create virtual environment
        self._create_virtual_environment()
        
        # Install dependencies
        self._install_dependencies()
        
        # Create directory structure
        self._create_directories()
        
        # Setup configuration
        self._setup_configuration()
        
        # Initialize database
        self._initialize_database()
        
        # Create environment file
        self._create_env_file()
        
        print("\n✅ Setup completed successfully!")
        print("\n📋 Next steps:")
        print("   1. Activate virtual environment:")
        print("      - Windows: venv\\Scripts\\activate")
        print("      - Unix/Mac: source venv/bin/activate")
        print("   2. Run the platform:")
        print("      python src/main.py --mode analysis")
        print("   3. For backtesting:")
        print("      python src/main.py --mode backtest")
        print("   4. For real-time monitoring:")
        print("      python src/main.py --mode monitor")
    
    def _check_python_version(self):
        """Check if Python version is compatible."""
        print("🔍 Checking Python version...")
        
        if sys.version_info < (3, 8):
            print("❌ Error: Python 3.8 or higher is required")
            sys.exit(1)
        
        print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    
    def _create_virtual_environment(self):
        """Create virtual environment."""
        print("\n🐍 Creating virtual environment...")
        
        if self.venv_path.exists():
            print("⚠️  Virtual environment already exists. Skipping creation.")
            return
        
        try:
            subprocess.run([sys.executable, "-m", "venv", str(self.venv_path)], 
                         check=True, capture_output=True)
            print("✅ Virtual environment created successfully")
        except subprocess.CalledProcessError as e:
            print(f"❌ Error creating virtual environment: {e}")
            sys.exit(1)
    
    def _install_dependencies(self):
        """Install project dependencies."""
        print("\n📦 Installing dependencies...")
        
        # Determine pip path
        if os.name == 'nt':  # Windows
            pip_path = self.venv_path / "Scripts" / "pip.exe"
        else:  # Unix/Linux/Mac
            pip_path = self.venv_path / "bin" / "pip"
        
        if not pip_path.exists():
            print("❌ Error: pip not found in virtual environment")
            sys.exit(1)
        
        try:
            # Upgrade pip first
            subprocess.run([str(pip_path), "install", "--upgrade", "pip"], 
                         check=True, capture_output=True)
            
            # Install requirements
            subprocess.run([str(pip_path), "install", "-r", str(self.requirements_file)], 
                         check=True, capture_output=True)
            
            print("✅ Dependencies installed successfully")
        except subprocess.CalledProcessError as e:
            print(f"❌ Error installing dependencies: {e}")
            print("💡 Try running: pip install -r requirements.txt manually")
            sys.exit(1)
    
    def _create_directories(self):
        """Create necessary directory structure."""
        print("\n📁 Creating directory structure...")
        
        directories = [
            "logs",
            "data",
            "reports",
            "backtests",
            "strategies",
            "models"
        ]
        
        for directory in directories:
            dir_path = self.project_root / directory
            dir_path.mkdir(exist_ok=True)
            print(f"   ✅ Created {directory}/")
    
    def _setup_configuration(self):
        """Setup configuration files."""
        print("\n⚙️  Setting up configuration...")
        
        # Create logs directory if it doesn't exist
        logs_dir = self.project_root / "logs"
        logs_dir.mkdir(exist_ok=True)
        
        # Create data directory if it doesn't exist
        data_dir = self.project_root / "data"
        data_dir.mkdir(exist_ok=True)
        
        print("✅ Configuration setup completed")
    
    def _initialize_database(self):
        """Initialize database."""
        print("\n🗄️  Initializing database...")
        
        # Create database directory
        db_dir = self.project_root / "data"
        db_dir.mkdir(exist_ok=True)
        
        # Create empty database file
        db_file = db_dir / "trading.db"
        if not db_file.exists():
            db_file.touch()
            print("✅ Database file created")
        else:
            print("✅ Database file already exists")
    
    def _create_env_file(self):
        """Create environment file template."""
        print("\n🔐 Creating environment file template...")
        
        env_file = self.project_root / ".env.example"
        
        env_content = """# AI-Nvestor Environment Configuration

# API Keys (Optional - for enhanced features)
OPENAI_API_KEY=your_openai_api_key_here
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key_here
POLYGON_API_KEY=your_polygon_api_key_here

# Trading Configuration
TRADING_PAPER_TRADING=true
TRADING_AUTO_TRADING=false
TRADING_REAL_TIME=true

# Risk Management
RISK_MAX_POSITION_SIZE=0.02
RISK_STOP_LOSS=0.05

# Logging
LOGGING_LEVEL=INFO

# AI Analysis
AI_ANALYSIS_ENABLED=true
"""
        
        with open(env_file, 'w') as f:
            f.write(env_content)
        
        print("✅ Environment file template created (.env.example)")
        print("💡 Copy .env.example to .env and configure your API keys")
    
    def run_tests(self):
        """Run basic tests to verify setup."""
        print("\n🧪 Running basic tests...")
        
        try:
            # Test imports
            test_script = """
import sys
sys.path.append('src')

try:
    from utils.config import get_config
    from utils.logging import setup_logging
    from services.indicators import TechnicalIndicators
    from core.stock_data import StockData
    print("✅ All imports successful")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)
"""
            
            result = subprocess.run([sys.executable, "-c", test_script], 
                                 capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ Basic tests passed")
            else:
                print("❌ Basic tests failed")
                print(result.stderr)
                
        except Exception as e:
            print(f"❌ Error running tests: {e}")
    
    def create_documentation(self):
        """Create basic documentation."""
        print("\n📚 Creating documentation...")
        
        docs_dir = self.project_root / "docs"
        docs_dir.mkdir(exist_ok=True)
        
        # Create README for docs
        docs_readme = docs_dir / "README.md"
        docs_content = """# AI-Nvestor Documentation

## Quick Start

1. **Setup**: Run `python setup_project.py`
2. **Activate Environment**: 
   - Windows: `venv\\Scripts\\activate`
   - Unix/Mac: `source venv/bin/activate`
3. **Run Analysis**: `python src/main.py --mode analysis`
4. **Run Backtest**: `python src/main.py --mode backtest`

## Configuration

Edit `config.json` to customize:
- Watchlist symbols
- Technical indicators
- Risk management parameters
- Market hours

## Features

- **Real-time Analysis**: Live market data analysis
- **Backtesting**: Historical strategy validation
- **Risk Management**: Position sizing and stop-loss
- **Performance Metrics**: Sharpe ratio, drawdown, etc.
- **Technical Indicators**: RSI, MACD, Bollinger Bands, etc.

## Architecture

```
src/
├── core/           # Core trading engine
├── services/       # External services
├── utils/          # Utilities
└── main.py         # Main entry point
```

## API Keys (Optional)

For enhanced features, add API keys to `.env`:
- OpenAI: AI-powered analysis
- Alpha Vantage: Market data
- Polygon: Real-time data
"""
        
        with open(docs_readme, 'w') as f:
            f.write(docs_content)
        
        print("✅ Documentation created")
    
    def cleanup(self):
        """Clean up temporary files."""
        print("\n🧹 Cleaning up...")
        
        # Remove any temporary files
        temp_files = [
            "*.pyc",
            "__pycache__",
            "*.log",
            ".pytest_cache"
        ]
        
        for pattern in temp_files:
            for file_path in self.project_root.rglob(pattern):
                if file_path.is_file():
                    file_path.unlink()
                elif file_path.is_dir():
                    import shutil
                    shutil.rmtree(file_path)
        
        print("✅ Cleanup completed")


def main():
    """Main setup function."""
    setup = ProjectSetup()
    
    try:
        setup.setup_environment()
        setup.run_tests()
        setup.create_documentation()
        setup.cleanup()
        
        print("\n🎉 AI-Nvestor is ready to use!")
        print("\n📖 For more information, see docs/README.md")
        
    except KeyboardInterrupt:
        print("\n🛑 Setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Setup failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
