"""
AI Caching Service for API Limit Management.
"""

import json
import hashlib
import time
from typing import Dict, Any, Optional, List
from collections import deque
from cachetools import LR<PERSON>ache, TTLCache

from utils.logging import get_logger

class AICache:
    """Intelligent in-memory caching system for AI responses."""
    
    def __init__(self, max_size: int = 1000, ttl_seconds: int = 3600 * 4):
        # Use a Time To Live (TTL) cache for automatic expiration
        self.cache = TTLCache(maxsize=max_size, ttl=ttl_seconds)
        self.logger = get_logger()
        self.stats = {"hits": 0, "misses": 0, "total_requests": 0}
    
    def _generate_cache_key(self, prompt: str, context: Dict[str, Any], provider: str) -> str:
        """Generate a unique and deterministic cache key."""
        context_str = json.dumps(context, sort_keys=True) if context else ""
        content = f"{provider}:{prompt}:{context_str}"
        return hashlib.md5(content.encode()).hexdigest()
    
    def get(self, prompt: str, context: Dict[str, Any], provider: str) -> Optional[Dict[str, Any]]:
        """Get cached AI response if available."""
        self.stats["total_requests"] += 1
        cache_key = self._generate_cache_key(prompt, context, provider)
        
        if cache_key in self.cache:
            self.stats["hits"] += 1
            self.logger.debug(f"AI cache HIT for {provider}")
            return self.cache[cache_key]
        
        self.stats["misses"] += 1
        return None
    
    def set(self, prompt: str, context: Dict[str, Any], provider: str, response: Dict[str, Any]):
        """Cache an AI response."""
        cache_key = self._generate_cache_key(prompt, context, provider)
        self.cache[cache_key] = response
        self.logger.debug(f"Cached AI response for {provider}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        hit_rate = self.stats["hits"] / max(self.stats["total_requests"], 1)
        return {**self.stats, "hit_rate": hit_rate, "current_size": len(self.cache)}

class RateLimiter:
    """Rate limiting for AI API calls using efficient deques."""
    
    def __init__(self, requests_per_minute: int = 60, requests_per_hour: int = 1000):
        self.requests_per_minute = requests_per_minute
        self.requests_per_hour = requests_per_hour
        # Use deques with maxlen for automatic, memory-efficient history tracking
        self.minute_requests = deque(maxlen=requests_per_minute)
        self.hour_requests = deque(maxlen=requests_per_hour)
        self.logger = get_logger()
    
    def can_make_request(self, provider: str) -> bool:
        """Check if a request can be made without hitting rate limits."""
        current_time = time.time()
        
        # Remove timestamps older than a minute/hour by checking the oldest element
        if self.minute_requests and current_time - self.minute_requests[0] > 60:
            self.minute_requests.popleft()
        if self.hour_requests and current_time - self.hour_requests[0] > 3600:
            self.hour_requests.popleft()
            
        if len(self.minute_requests) >= self.requests_per_minute:
            self.logger.warning(f"Rate limit approaching for {provider} (minute limit)")
            return False
        
        if len(self.hour_requests) >= self.requests_per_hour:
            self.logger.warning(f"Rate limit approaching for {provider} (hour limit)")
            return False
        
        return True
    
    def record_request(self, provider: str):
        """Record a successful API request."""
        current_time = time.time()
        self.minute_requests.append(current_time)
        self.hour_requests.append(current_time)
        self.logger.debug(f"Recorded API request for {provider}")
    
    def get_wait_time(self) -> float:
        """Get the time to wait before the next request can be made."""
        current_time = time.time()
        wait_minute = 0
        wait_hour = 0

        if len(self.minute_requests) >= self.requests_per_minute:
            wait_minute = (self.minute_requests[0] + 60) - current_time

        if len(self.hour_requests) >= self.requests_per_hour:
            wait_hour = (self.hour_requests[0] + 3600) - current_time
            
        return max(wait_minute, wait_hour, 0)
