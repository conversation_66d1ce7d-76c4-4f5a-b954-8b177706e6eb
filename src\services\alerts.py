"""
Alert system for AI-Nvestor.

This module provides:
- Multi-channel notifications (email, Slack, Discord)
- Customizable alert triggers
- Rate limiting and throttling
- Alert history and tracking
- Message templating
"""

import smtplib
import requests
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Union, Any
from dataclasses import dataclass
from enum import Enum
from pathlib import Path
import time
from email.mime.multipart import MIME<PERSON>ult<PERSON>art
from email.mime.text import MIMEText

# Twilio imports (optional dependency)
try:
    from twilio.rest import Client as TwilioClient
    TWILIO_AVAILABLE = True
except ImportError:
    TWILIO_AVAILABLE = False

from core.exceptions import NotificationError, get_error_code
from utils.logging import get_logger


class AlertType(Enum):
    """Alert types."""
    SIGNAL_GENERATED = "SIGNAL_GENERATED"
    PRICE_ALERT = "PRICE_ALERT"
    VOLUME_SPIKE = "VOLUME_SPIKE"
    TECHNICAL_BREAKOUT = "TECHNICAL_BREAKOUT"
    RISK_ALERT = "RISK_ALERT"
    SYSTEM_ALERT = "SYSTEM_ALERT"
    PERFORMANCE_ALERT = "PERFORMANCE_ALERT"


class AlertPriority(Enum):
    """Alert priority levels."""
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    CRITICAL = "CRITICAL"


@dataclass
class Alert:
    """Alert with metadata."""
    alert_type: AlertType
    priority: AlertPriority
    title: str
    message: str
    symbol: Optional[str] = None
    price: Optional[float] = None
    timestamp: datetime = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()
        if self.metadata is None:
            self.metadata = {}


class AlertSystem:
    """
    Comprehensive alert system.
    
    Features:
    - Multi-channel notifications
    - Customizable triggers
    - Message templating
    - Rate limiting
    - Alert history
    """
    
    def __init__(self, config: Dict):
        """
        Initialize the alert system.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.logger = get_logger()
        self.notifications_config = config.get('notifications', {})
        
        # Alert history
        self.alert_history = []
        self.alert_file = Path("data/alerts.json")
        self.alert_file.parent.mkdir(parents=True, exist_ok=True)
        
        # Rate limiting
        self.last_alert_time = {}
        self.min_alert_interval = 300  # 5 minutes
        
        # Load existing alerts
        self._load_alert_history()
    
    def send_alert(self, 
                   alert_type: AlertType,
                   priority: AlertPriority,
                   title: str,
                   message: str,
                   symbol: Optional[str] = None,
                   price: Optional[float] = None,
                   metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        Send an alert through all configured channels.
        
        Args:
            alert_type: Type of alert
            priority: Alert priority
            title: Alert title
            message: Alert message
            symbol: Stock symbol (optional)
            price: Current price (optional)
            metadata: Additional metadata
            
        Returns:
            True if alert was sent successfully
        """
        try:
            # Create alert
            alert = Alert(
                alert_type=alert_type,
                priority=priority,
                title=title,
                message=message,
                symbol=symbol,
                price=price,
                metadata=metadata or {}
            )
            
            # Check rate limiting
            if not self._should_send_alert(alert):
                return False
            
            # Send through all channels
            success = True
            
            if self.notifications_config.get('email_enabled', False):
                if not self._send_email_alert(alert):
                    success = False
            
            if self.notifications_config.get('slack_enabled', False):
                if not self._send_slack_alert(alert):
                    success = False
            
            if self.notifications_config.get('discord_enabled', False):
                if not self._send_discord_alert(alert):
                    success = False
            
            if self.notifications_config.get('sms_enabled', False):
                if not self._send_sms_alert(alert):
                    success = False
            
            # Store alert
            self._store_alert(alert)
            
            # Update rate limiting
            self.last_alert_time[alert_type.value] = datetime.now()
            
            return success
            
        except Exception as e:
            self.logger.log_error(e, {'operation': 'send_alert'})
            return False
    
    def send_signal_alert(self, symbol: str, signal_type: str, confidence: float, 
                         price: float, strength: str = "MODERATE") -> bool:
        """
        Send a signal alert.
        
        Args:
            symbol: Stock symbol
            signal_type: Type of signal (BUY, SELL, etc.)
            confidence: Signal confidence
            price: Current price
            strength: Signal strength
            
        Returns:
            True if alert was sent successfully
        """
        priority = AlertPriority.HIGH if strength in ["STRONG", "VERY_STRONG"] else AlertPriority.MEDIUM
        
        title = f"Trading Signal: {symbol}"
        message = f"""
🔔 {signal_type} Signal for {symbol}

📊 Signal Details:
• Type: {signal_type}
• Confidence: {confidence:.1%}
• Strength: {strength}
• Price: ${price:.2f}
• Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

Please review and take appropriate action.
        """.strip()
        
        return self.send_alert(
            AlertType.SIGNAL_GENERATED,
            priority,
            title,
            message,
            symbol=symbol,
            price=price,
            metadata={
                'signal_type': signal_type,
                'confidence': confidence,
                'strength': strength
            }
        )
    
    def send_price_alert(self, symbol: str, current_price: float, 
                        target_price: float, direction: str = "above") -> bool:
        """
        Send a price alert.
        
        Args:
            symbol: Stock symbol
            current_price: Current price
            target_price: Target price
            direction: "above" or "below"
            
        Returns:
            True if alert was sent successfully
        """
        title = f"Price Alert: {symbol}"
        message = f"""
💰 Price Alert for {symbol}

📈 Price Information:
• Current Price: ${current_price:.2f}
• Target Price: ${target_price:.2f}
• Direction: {direction.upper()}
• Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

The price has moved {direction} your target level.
        """.strip()
        
        return self.send_alert(
            AlertType.PRICE_ALERT,
            AlertPriority.MEDIUM,
            title,
            message,
            symbol=symbol,
            price=current_price,
            metadata={
                'target_price': target_price,
                'direction': direction
            }
        )
    
    def send_risk_alert(self, message: str, risk_level: str = "MEDIUM") -> bool:
        """
        Send a risk alert.
        
        Args:
            message: Risk alert message
            risk_level: Risk level (LOW, MEDIUM, HIGH, CRITICAL)
            
        Returns:
            True if alert was sent successfully
        """
        priority_map = {
            "LOW": AlertPriority.LOW,
            "MEDIUM": AlertPriority.MEDIUM,
            "HIGH": AlertPriority.HIGH,
            "CRITICAL": AlertPriority.CRITICAL
        }
        
        priority = priority_map.get(risk_level.upper(), AlertPriority.MEDIUM)
        
        title = f"Risk Alert: {risk_level.upper()}"
        formatted_message = f"""
⚠️ Risk Alert

🚨 Risk Level: {risk_level.upper()}
⏰ Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

{message}
        """.strip()
        
        return self.send_alert(
            AlertType.RISK_ALERT,
            priority,
            title,
            formatted_message,
            metadata={'risk_level': risk_level}
        )
    
    def send_performance_alert(self, metrics: Dict[str, float]) -> bool:
        """
        Send a performance alert.
        
        Args:
            metrics: Performance metrics
            
        Returns:
            True if alert was sent successfully
        """
        title = "Performance Alert"
        
        # Format metrics
        metrics_text = "\n".join([
            f"• {key.replace('_', ' ').title()}: {value:.2f}"
            for key, value in metrics.items()
        ])
        
        message = f"""
📊 Performance Update

⏰ Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

📈 Metrics:
{metrics_text}

Please review your trading performance.
        """.strip()
        
        return self.send_alert(
            AlertType.PERFORMANCE_ALERT,
            AlertPriority.MEDIUM,
            title,
            message,
            metadata=metrics
        )
    
    def _should_send_alert(self, alert: Alert) -> bool:
        """Check if alert should be sent based on rate limiting."""
        alert_key = f"{alert.alert_type.value}_{alert.symbol or 'general'}"
        
        if alert_key in self.last_alert_time:
            time_since_last = datetime.now() - self.last_alert_time[alert_key]
            if time_since_last.total_seconds() < self.min_alert_interval:
                return False
        
        return True
    
    def _send_email_alert(self, alert: Alert) -> bool:
        """Send alert via email."""
        try:
            email_config = self.notifications_config.get('email', {})
            
            if not email_config.get('enabled', False):
                return True
            
            # Create message
            msg = MIMEMultipart()
            msg['From'] = email_config.get('from_email')
            msg['To'] = email_config.get('to_email')
            msg['Subject'] = f"[{alert.priority.value}] {alert.title}"
            
            # Create HTML body
            html_body = self._format_email_html(alert)
            msg.attach(MIMEText(html_body, 'html'))
            
            # Send email
            server = smtplib.SMTP(email_config.get('smtp_server'), email_config.get('smtp_port', 587))
            server.starttls()
            server.login(email_config.get('username'), email_config.get('password'))
            server.send_message(msg)
            server.quit()
            
            return True
            
        except Exception as e:
            self.logger.log_error(e, {'operation': 'send_email_alert'})
            return False
    
    def _send_slack_alert(self, alert: Alert) -> bool:
        """Send alert via Slack."""
        try:
            webhook_url = self.notifications_config.get('slack_webhook_url')
            if not webhook_url:
                return True
            
            # Create Slack message
            slack_message = self._format_slack_message(alert)
            
            response = requests.post(webhook_url, json=slack_message)
            return response.status_code == 200
            
        except Exception as e:
            self.logger.log_error(e, {'operation': 'send_slack_alert'})
            return False
    
    def _send_discord_alert(self, alert: Alert) -> bool:
        """Send alert via Discord."""
        try:
            webhook_url = self.notifications_config.get('discord_webhook_url')
            if not webhook_url:
                return True
            
            # Create Discord message
            discord_message = self._format_discord_message(alert)
            
            response = requests.post(webhook_url, json=discord_message)
            return response.status_code == 200
            
        except Exception as e:
            self.logger.log_error(e, {'operation': 'send_discord_alert'})
            return False
    
    def _send_sms_alert(self, alert: Alert) -> bool:
        """Send alert via SMS (Twilio)."""
        try:
            if not TWILIO_AVAILABLE:
                self.logger.warning("Twilio not available - please install: pip install twilio")
                return True
            
            sms_config = self.notifications_config.get('sms', {})
            
            if not sms_config.get('enabled', False):
                return True
            
            # Initialize Twilio client
            account_sid = sms_config.get('twilio_account_sid')
            auth_token = sms_config.get('twilio_auth_token')
            from_number = sms_config.get('from_number')
            to_number = sms_config.get('to_number')
            
            if not all([account_sid, auth_token, from_number, to_number]):
                self.logger.warning("SMS config incomplete - missing Twilio credentials")
                return True
            
            client = TwilioClient(account_sid, auth_token)
            
            # Format SMS message (keep it short)
            sms_message = self._format_sms_message(alert)
            
            # Send SMS
            message = client.messages.create(
                body=sms_message,
                from_=from_number,
                to=to_number
            )
            
            self.logger.info(f"SMS sent successfully: {message.sid}")
            return True
            
        except Exception as e:
            self.logger.log_error(e, {'operation': 'send_sms_alert'})
            return False
    
    def _format_email_html(self, alert: Alert) -> str:
        """Format alert as HTML email."""
        priority_colors = {
            AlertPriority.LOW: "#28a745",
            AlertPriority.MEDIUM: "#ffc107",
            AlertPriority.HIGH: "#fd7e14",
            AlertPriority.CRITICAL: "#dc3545"
        }
        
        color = priority_colors.get(alert.priority, "#6c757d")
        
        html = f"""
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; }}
                .alert {{ border-left: 4px solid {color}; padding: 10px; margin: 10px 0; }}
                .priority {{ color: {color}; font-weight: bold; }}
                .timestamp {{ color: #6c757d; font-size: 12px; }}
            </style>
        </head>
        <body>
            <div class="alert">
                <h2>{alert.title}</h2>
                <p class="priority">Priority: {alert.priority.value}</p>
                <p class="timestamp">{alert.timestamp.strftime('%Y-%m-%d %H:%M:%S')}</p>
                <hr>
                <pre style="white-space: pre-wrap; font-family: inherit;">{alert.message}</pre>
            </div>
        </body>
        </html>
        """
        
        return html
    
    def _format_slack_message(self, alert: Alert) -> Dict[str, Any]:
        """Format alert as Slack message."""
        priority_emoji = {
            AlertPriority.LOW: ":white_check_mark:",
            AlertPriority.MEDIUM: ":warning:",
            AlertPriority.HIGH: ":rotating_light:",
            AlertPriority.CRITICAL: ":fire:"
        }
        
        emoji = priority_emoji.get(alert.priority, ":bell:")
        
        return {
            "text": f"{emoji} *{alert.title}*",
            "attachments": [
                {
                    "color": self._get_priority_color(alert.priority),
                    "fields": [
                        {
                            "title": "Priority",
                            "value": alert.priority.value,
                            "short": True
                        },
                        {
                            "title": "Time",
                            "value": alert.timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                            "short": True
                        },
                        {
                            "title": "Message",
                            "value": alert.message,
                            "short": False
                        }
                    ]
                }
            ]
        }
    
    def _format_discord_message(self, alert: Alert) -> Dict[str, Any]:
        """Format alert as Discord message."""
        priority_emoji = {
            AlertPriority.LOW: "✅",
            AlertPriority.MEDIUM: "⚠️",
            AlertPriority.HIGH: "🚨",
            AlertPriority.CRITICAL: "🔥"
        }
        
        emoji = priority_emoji.get(alert.priority, "🔔")
        
        return {
            "embeds": [
                {
                    "title": f"{emoji} {alert.title}",
                    "description": alert.message,
                    "color": self._get_priority_color(alert.priority),
                    "fields": [
                        {
                            "name": "Priority",
                            "value": alert.priority.value,
                            "inline": True
                        },
                        {
                            "name": "Time",
                            "value": alert.timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                            "inline": True
                        }
                    ],
                    "timestamp": alert.timestamp.isoformat()
                }
            ]
        }
    
    def _get_priority_color(self, priority: AlertPriority) -> int:
        """Get color code for priority level."""
        color_map = {
            AlertPriority.LOW: 0x28a745,      # Green
            AlertPriority.MEDIUM: 0xffc107,   # Yellow
            AlertPriority.HIGH: 0xfd7e14,     # Orange
            AlertPriority.CRITICAL: 0xdc3545  # Red
        }
        return color_map.get(priority, 0x6c757d)
    
    def _format_sms_message(self, alert: Alert) -> str:
        """Format alert as SMS message (keep under 160 chars)."""
        priority_emoji = {
            AlertPriority.LOW: "✅",
            AlertPriority.MEDIUM: "⚠️", 
            AlertPriority.HIGH: "🚨",
            AlertPriority.CRITICAL: "🔥"
        }
        
        emoji = priority_emoji.get(alert.priority, "🔔")
        
        # Keep SMS short and actionable
        if alert.symbol and alert.price:
            return f"{emoji} {alert.symbol}: {alert.signal if hasattr(alert, 'signal') else alert.title} @ ${alert.price:.2f}"
        else:
            # Truncate message if too long
            short_message = alert.message[:100] + "..." if len(alert.message) > 100 else alert.message
            return f"{emoji} {alert.title}: {short_message}"
    
    def _store_alert(self, alert: Alert):
        """Store alert in history."""
        try:
            alert_data = {
                'alert_type': alert.alert_type.value,
                'priority': alert.priority.value,
                'title': alert.title,
                'message': alert.message,
                'symbol': alert.symbol,
                'price': alert.price,
                'timestamp': alert.timestamp.isoformat(),
                'metadata': alert.metadata
            }
            
            self.alert_history.append(alert_data)
            
            # Keep only last 1000 alerts
            if len(self.alert_history) > 1000:
                self.alert_history = self.alert_history[-1000:]
            
            # Save to file
            with open(self.alert_file, 'w') as f:
                json.dump(self.alert_history, f, indent=2)
                
        except Exception as e:
            self.logger.log_error(e, {'operation': 'store_alert'})
    
    def _load_alert_history(self):
        """Load alert history from file."""
        try:
            if self.alert_file.exists():
                with open(self.alert_file, 'r') as f:
                    self.alert_history = json.load(f)
            else:
                self.alert_history = []
        except Exception as e:
            self.logger.log_error(e, {'operation': 'load_alert_history'})
            self.alert_history = []
    
    def get_alert_history(self, alert_type: Optional[AlertType] = None, 
                         days: int = 30) -> List[Dict]:
        """
        Get alert history.
        
        Args:
            alert_type: Filter by alert type (optional)
            days: Number of days to look back
            
        Returns:
            List of historical alerts
        """
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            
            filtered_alerts = []
            for alert in self.alert_history:
                alert_date = datetime.fromisoformat(alert['timestamp'])
                if alert_date >= cutoff_date:
                    if alert_type is None or alert['alert_type'] == alert_type.value:
                        filtered_alerts.append(alert)
            
            return filtered_alerts
            
        except Exception as e:
            self.logger.log_error(e, {'operation': 'get_alert_history'})
            return []
    
    def get_alert_stats(self, days: int = 30) -> Dict[str, int]:
        """
        Get alert statistics.
        
        Args:
            days: Number of days to analyze
            
        Returns:
            Alert statistics
        """
        try:
            alerts = self.get_alert_history(days=days)
            
            stats = {
                'total_alerts': len(alerts),
                'by_type': {},
                'by_priority': {}
            }
            
            for alert in alerts:
                # Count by type
                alert_type = alert['alert_type']
                if alert_type not in stats['by_type']:
                    stats['by_type'][alert_type] = 0
                stats['by_type'][alert_type] += 1
                
                # Count by priority
                priority = alert['priority']
                if priority not in stats['by_priority']:
                    stats['by_priority'][priority] = 0
                stats['by_priority'][priority] += 1
            
            return stats
            
        except Exception as e:
            self.logger.log_error(e, {'operation': 'get_alert_stats'})
            return {}
