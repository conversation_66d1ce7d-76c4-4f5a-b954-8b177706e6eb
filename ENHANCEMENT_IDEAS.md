# ENHANCEMENT_IDEAS.md

This document provides a hyper-detailed technical roadmap for enhancing the AI-Nvestor platform. It is intended to be a direct guide for implementation, minimizing ambiguity for the engineering and quantitative teams.

---

## I. Quantitative & Financial Strategy Enhancements

### 1. Market Regime Detection

**The Why**: A static trading strategy is suboptimal. The platform must adapt its logic to the prevailing market environment.

**Hyper-Detailed Implementation Plan**:
1.  **Service**: Create `src/services/market_regime_service.py` with a `MarketRegimeService` class.
2.  **Model**: Use a 3-state Gaussian HMM from `hmmlearn` (`[0: Bear]`, `[1: Neutral]`, `[2: Bull]`)
3.  **Features**: Daily log returns and 21-day rolling annualized volatility of `SPY`.
4.  **Training**: Create `scripts/training/train_hmm_regime_model.py` to train the HMM on 10+ years of data and serialize it to `data/models/hmm_regime_model.pkl`.
5.  **Logic**: The service's `get_current_regime()` method loads the model, predicts on the latest 30 days of data, and caches the result for 1 hour.
6.  **Integration**: Register in `ServiceFactory` for use by `SignalsService` and `AIAdvisor` to dynamically adjust indicator parameters and AI prompts.

### 2. Advanced Backtesting Framework

**The Why**: Backtests must be brutally realistic to be trustworthy.

**Hyper-Detailed Implementation Plan**:
1.  **Library**: Integrate `backtesting.py`.
2.  **Strategy Class**: Create `src/backtesting/strategies/base_strategy.py` inheriting from `backtesting.Strategy`. The `next()` method will orchestrate the full signal generation and trading logic for each time step.
3.  **Cost Modeling**: Model slippage as a function of volatility and trade size. Model commissions based on a `config.json` parameter.
4.  **Optimization**: Implement walk-forward optimization in `scripts/run_walk_forward_backtest.py` to prevent overfitting by testing on out-of-sample data.

### 3. Portfolio-Level Risk Management

**The Why**: Move from isolated stock analysis to holistic portfolio management.

**Hyper-Detailed Implementation Plan**:
1.  **Services**: Create a `PortfolioManagerService` to track portfolio state and a `CorrelationService` to compute a 90-day rolling correlation matrix.
2.  **Risk Rules Engine**: In `PortfolioManagerService`, implement a rules engine based on `config.json` parameters (`max_portfolio_risk_per_trade`, `max_single_stock_exposure`, `max_sector_exposure`, `high_correlation_threshold`).
3.  **Position Sizing**: Before any trade, consult the rules engine to check for breaches. Veto or resize trades based on concentration and correlation limits.
4.  **Integration**: Use the `PortfolioManagerService` within the backtesting `BaseStrategy` to ensure realistic constraints.

---

## II. Data-Driven Decision Making & Alpha Generation

### 4. Systematic Alpha Research Framework

**The Why**: Create a systematic process for discovering, validating, and deploying new predictive signals.

**Hyper-Detailed Implementation Plan**:
1.  **Sandbox**: Create `notebooks/research/` for Jupyter notebooks dedicated to feature engineering.
2.  **Validation Pipeline**: Create `scripts/research/validate_alpha.py` that uses the `alphalens` library to test a new factor's predictive power.
3.  **Tear Sheet Analysis**: The script will generate a full `alphalens` tear sheet, and the team will analyze key metrics like Information Coefficient (IC), quantile returns, and turnover to judge the alpha's quality.
4.  **Path to Production**: Promising alphas are graduated from research to production features calculated by the Feature Store ingestion scripts.

### 5. Self-Adaptive AI Prompts

**The Why**: AI prompts should not be static; they should evolve based on what works.

**Hyper-Detailed Implementation Plan**:
1.  **Templating**: Use a `PromptTemplateService` to manage prompt templates stored in `src/prompts/`.
2.  **Variants**: Create multiple prompt variants for each task, differing by style (`concise`, `contrarian`) and focus (`technical`, `fundamental`).
3.  **Performance DB**: Create an `AIPerformanceTracker` service to log every AI signal and its subsequent multi-day return to a SQLite DB at `data/ai_performance.db`.
4.  **Selection Algorithm**: Use an Epsilon-Greedy multi-armed bandit in `AIAdvisor` to select prompts. It will **exploit** the best-performing historical prompt variant 90% of the time and **explore** a random one 10% of the time.

---

## III. Code Health & Refactoring Initiatives

### 6. Dead Code and Redundancy Elimination

**The Why**: Unused code and redundant logic increase complexity, slow down development, and can hide bugs. A clean codebase is easier and safer to maintain.

**Hyper-Detailed Implementation Plan**:
1.  **Static Analysis for Dead Code**: 
    - **Tool**: Use `vulture` (`pip install vulture`).
    - **Process**: Create a dedicated CI job in `ci-cd.yml` called `find-dead-code`.
    - **Command**: `vulture src/ --min-confidence 80 > vulture_report.txt`. This command analyzes the `src` directory and reports code that is likely unused with a confidence score of 80% or higher.
    - **Action**: The report will be reviewed by developers during code review. Confirmed dead code (old functions, unused imports, unreachable code blocks) must be removed.

2.  **Redundancy Analysis**:
    - **Tool**: Use `pmd`'s Copy/Paste Detector (CPD) for Python (`pip install pmd-cpd`).
    - **Process**: Add a step to the `lint-and-test` CI job.
    - **Command**: `cpd --files src --minimum-tokens 70 --language python`. This will detect duplicated code blocks of 70 tokens or more.
    - **Action**: Developers will be required to refactor duplicated logic into shared utility functions or base classes to adhere to the Don't Repeat Yourself (DRY) principle.

### 7. Performance Profiling and Optimization

**The Why**: To ensure the platform runs efficiently, especially during data-intensive backtesting and analysis, we must identify and fix performance bottlenecks.

**Hyper-Detailed Implementation Plan**:
1.  **Line-Level Profiling**: 
    - **Tool**: Use `line_profiler` (`pip install line_profiler`).
    - **Process**: For performance-critical sections (e.g., the `next` loop in the backtesting strategy, indicator calculation loops), developers will use the `@profile` decorator.
    - **Execution**: Run the profiler from the command line: `kernprof -l -v my_script_to_profile.py`.
    - **Action**: The output shows the time spent on each line of code. Developers will focus on optimizing the most time-consuming lines, for example by vectorizing pandas operations instead of using `.apply()` or loops.

2.  **Memory Profiling**:
    - **Tool**: Use `memory-profiler` (`pip install memory-profiler`).
    - **Process**: Similar to line profiling, use the `@profile` decorator on functions suspected of high memory usage.
    - **Execution**: `python -m memory_profiler my_script_to_profile.py`.
    - **Action**: The profiler will show memory usage per line. This is critical for finding memory leaks, where objects are not being released from memory. Common fixes include ensuring large objects (like pandas DataFrames) go out of scope and are garbage collected, or explicitly deleting them with `del`.

### 8. Refactoring Common Anti-Patterns

**The Why**: Anti-patterns are common solutions to problems that are ultimately counter-productive. Proactively identifying and refactoring them improves long-term code quality.

**Hyper-Detailed Implementation Plan**:
1.  **Anti-Pattern: Large, God-like Services**
    - **Identification**: Look for services in `src/services/` that have too many responsibilities (e.g., a single service handling data fetching, cleaning, analysis, and AI prompting).
    - **Refactoring**: Apply the Single Responsibility Principle (SRP). Break down large services into smaller, more focused ones. For example, a large `MarketDataService` could be split into `YahooFinanceService`, `PolygonService`, and a `MarketDataAggregatorService` that combines them.

2.  **Anti-Pattern: Configuration as Code**
    - **Identification**: Finding hard-coded values like API endpoints, file paths, or magic numbers (e.g., `lookback=14`) directly in the code instead of in `config.json`.
    - **Refactoring**: Move all such values into the Pydantic-validated `config.json`. This makes the system more flexible and easier to manage without code changes.

3.  **Anti-Pattern: Lack of Defensive Programming in Data Handling**
    - **Identification**: Code that implicitly trusts the structure and quality of API responses or data files (e.g., `data['close']` without checking if `data` or the `'close'` key exists).
    - **Refactoring**: Implement rigorous data validation at the point of ingestion. Use `try-except` blocks to handle missing keys or unexpected `None` values. When processing dataframes, check for `NaN` values and handle them explicitly (e.g., `df.fillna(0)` or `df.dropna()`). The goal is to fail fast and explicitly rather than propagating bad data through the system.

---

## IV. Architectural & Engineering Improvements

### 9. Job Orchestration & Scheduling System

**The Why**: The platform needs a robust, automated scheduling system for recurring tasks.

**Hyper-Detailed Implementation Plan**:
1.  **Library**: Integrate `APScheduler`.
2.  **Service**: Create `src/core/scheduler_service.py` to encapsulate the `BlockingScheduler`.
3.  **Job Definitions**: Create a `src/jobs/` directory to define job functions (e.g., `run_daily_market_data_ingestion`).
4.  **Scheduler Entrypoint**: Create `run_scheduler.py` to initialize the service and schedule the jobs with cron-like expressions. This will be the entry point for the long-running scheduler process.

### 10. Advanced CI/CD Pipeline

**The Why**: To automate not just testing, but also security scanning and deployment.

**Hyper-Detailed Implementation Plan**:
- **File**: `.github/workflows/ci-cd.yml`
- **Jobs**:
    1.  **`lint-and-test`**: Runs `ruff`, `black`, `pytest`.
    2.  **`security-scan`**: Runs `pip-audit` for dependency vulnerabilities and `bandit` for static code security analysis.
    3.  **`build-and-push-docker`**: On pushes to `main`, builds the Docker image and pushes it to a container registry.
    4.  **`deploy-to-staging`**: Manually triggered job to deploy the `latest` image to a staging environment for verification.

### 11. Configuration Validation & Management

**The Why**: The `config.json` file is a critical failure point and must be validated with extreme rigor.

**Hyper-Detailed Implementation Plan**:
1.  **Pydantic Models**: Define Pydantic models for the entire `config.json` structure in `src/utils/config.py`.
2.  **Strict Loading**: On startup, parse the config into the Pydantic model. On validation failure, exit with a precise error.
3.  **Environment Check**: Verify all required API keys are present in the environment.
4.  **CLI Validator**: Create `python run.py --mode config --validate` as a CI/CD step.