#!/usr/bin/env python3
"""
AI-Nvestor Backtesting Framework for AI-Powered Weights

This module provides several approaches to backtest our AI-powered signal generation
and weight optimization, addressing the fundamental challenge of AI LLMs not being
directly backtestable.

Approaches:
1. Technical Indicator Only Backtesting
2. AI Response Simulation Backtesting  
3. Hybrid Approach Backtesting
4. Weight Optimization Backtesting
5. Signal Conflict Resolution Backtesting
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Union, Callable
from dataclasses import dataclass
from enum import Enum
import yfinance as yf
from pathlib import Path
import json
import pickle
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
import matplotlib.pyplot as plt
import seaborn as sns

from utils.config import get_config
from utils.logging import get_logger
from services.indicators import TechnicalIndicators, SignalType, IndicatorResult
from services.signals import SignalGenerator


class BacktestApproach(Enum):
    """Different approaches for backtesting AI-powered weights."""
    TECHNICAL_ONLY = "technical_only"
    AI_SIMULATION = "ai_simulation"
    HYBRID = "hybrid"
    WEIGHT_OPTIMIZATION = "weight_optimization"
    CONFLICT_RESOLUTION = "conflict_resolution"


@dataclass
class WeightConfig:
    """Configuration for different weight schemes to test."""
    name: str
    trend_weight: float = 1.5
    momentum_weight: float = 1.0
    volatility_weight: float = 0.8
    conflict_reduction: float = 0.7
    momentum_cap: float = 0.4
    trend_threshold: float = 0.1


@dataclass
class BacktestMetrics:
    """Comprehensive metrics for backtesting results."""
    # Performance metrics
    total_return: float
    annualized_return: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    profit_factor: float
    
    # Signal accuracy metrics
    signal_accuracy: float
    precision: float
    recall: float
    f1_score: float
    
    # Trade metrics
    total_trades: int
    winning_trades: int
    losing_trades: int
    avg_win: float
    avg_loss: float
    avg_trade_duration: float
    
    # Risk metrics
    volatility: float
    sortino_ratio: float
    calmar_ratio: float
    max_consecutive_losses: int
    
    # AI-specific metrics
    ai_agreement_rate: float
    conflict_resolution_effectiveness: float
    weight_stability: float


class AIWeightBacktester:
    """
    Advanced backtesting engine specifically designed for AI-powered weight optimization.
    
    This addresses the fundamental challenge of backtesting AI LLMs by:
    1. Testing technical indicators in isolation
    2. Simulating AI responses based on historical patterns
    3. Optimizing weights based on historical performance
    4. Testing conflict resolution strategies
    """
    
    def __init__(self, config: Dict, initial_capital: float = 100000):
        """Initialize the AI weight backtester."""
        self.config = config
        self.initial_capital = initial_capital
        self.logger = get_logger()
        self.indicators = TechnicalIndicators(config)
        self.signal_service = SignalGenerator(config)
        
        # Historical AI response patterns (for simulation)
        self.ai_response_patterns = {}
        
        # Weight configurations to test
        self.weight_configs = self._create_weight_configs()
        
    def _create_weight_configs(self) -> List[WeightConfig]:
        """Create different weight configurations to test."""
        configs = [
            WeightConfig(
                name="Current Weights",
                trend_weight=1.5,
                momentum_weight=1.0,
                volatility_weight=0.8,
                conflict_reduction=0.7,
                momentum_cap=0.4,
                trend_threshold=0.1
            ),
            WeightConfig(
                name="Conservative Weights",
                trend_weight=2.0,
                momentum_weight=0.8,
                volatility_weight=0.6,
                conflict_reduction=0.5,
                momentum_cap=0.3,
                trend_threshold=0.2
            ),
            WeightConfig(
                name="Aggressive Weights",
                trend_weight=1.2,
                momentum_weight=1.2,
                volatility_weight=1.0,
                conflict_reduction=0.8,
                momentum_cap=0.5,
                trend_threshold=0.05
            ),
            WeightConfig(
                name="Balanced Weights",
                trend_weight=1.5,
                momentum_weight=1.0,
                volatility_weight=0.8,
                conflict_reduction=0.6,
                momentum_cap=0.4,
                trend_threshold=0.15
            )
        ]
        return configs
    
    def run_comprehensive_backtest(self, symbols: List[str], start_date: str, end_date: str) -> Dict[str, BacktestMetrics]:
        """
        Run comprehensive backtesting across all approaches and weight configurations.
        
        Args:
            symbols: List of symbols to backtest
            start_date: Start date for backtesting
            end_date: End date for backtesting
            
        Returns:
            Dictionary of results for each approach and weight configuration
        """
        self.logger.info(f"🚀 Starting comprehensive AI weight backtesting for {len(symbols)} symbols")
        
        # Load historical data
        data = self._load_historical_data(symbols, start_date, end_date)
        if not data:
            self.logger.error("❌ Failed to load historical data")
            return {}
        
        results = {}
        
        # Test each weight configuration
        for weight_config in self.weight_configs:
            self.logger.info(f"🔧 Testing weight configuration: {weight_config.name}")
            
            # Test technical-only approach
            tech_results = self._backtest_technical_only(data, weight_config)
            results[f"{weight_config.name}_technical"] = tech_results
            
            # Test AI simulation approach
            ai_results = self._backtest_ai_simulation(data, weight_config)
            results[f"{weight_config.name}_ai_simulation"] = ai_results
            
            # Test hybrid approach
            hybrid_results = self._backtest_hybrid(data, weight_config)
            results[f"{weight_config.name}_hybrid"] = hybrid_results
        
        # Generate comprehensive report
        self._generate_backtest_report(results, symbols, start_date, end_date)
        
        return results
    
    def _backtest_technical_only(self, data: Dict[str, pd.DataFrame], weight_config: WeightConfig) -> BacktestMetrics:
        """
        Backtest using only technical indicators (no AI).
        
        This is the most reliable approach since we can fully control the signal generation.
        """
        self.logger.info(f"📊 Running technical-only backtest with {weight_config.name}")
        
        trades = []
        equity_curve = []
        current_capital = self.initial_capital
        positions = {}
        
        # Get all unique dates
        all_dates = set()
        for symbol_data in data.values():
            all_dates.update(symbol_data.index)
        dates = sorted(list(all_dates))
        
        for date in dates:
            daily_pnl = 0
            
            for symbol, symbol_data in data.items():
                if date not in symbol_data.index:
                    continue
                
                # Get data up to current date
                current_data = symbol_data.loc[:date]
                if len(current_data) < 50:
                    continue
                
                # Calculate indicators with custom weights
                indicators = self._calculate_indicators_with_weights(current_data, weight_config)
                
                # Generate signal with custom weights
                signal, confidence = self._generate_signal_with_weights(indicators, weight_config)
                
                # Execute signal
                current_price = current_data.iloc[-1]['Close']
                trade_pnl = self._execute_signal_with_weights(
                    symbol, date, signal, current_price, confidence, 
                    positions, current_capital, weight_config
                )
                daily_pnl += trade_pnl
                
                if trade_pnl != 0:
                    trades.append({
                        'symbol': symbol,
                        'date': date,
                        'signal': signal.value if signal else 'HOLD',
                        'price': current_price,
                        'confidence': confidence,
                        'pnl': trade_pnl
                    })
            
            # Update equity curve
            current_capital += daily_pnl
            equity_curve.append({
                'date': date,
                'equity': current_capital,
                'return': (current_capital - self.initial_capital) / self.initial_capital
            })
        
        return self._calculate_metrics(trades, equity_curve, weight_config.name)
    
    def _backtest_ai_simulation(self, data: Dict[str, pd.DataFrame], weight_config: WeightConfig) -> BacktestMetrics:
        """
        Backtest using simulated AI responses based on historical patterns.
        
        This approach simulates what AI would have recommended based on:
        1. Historical AI response patterns
        2. Technical indicator correlations
        3. Market condition analysis
        """
        self.logger.info(f"🤖 Running AI simulation backtest with {weight_config.name}")
        
        # Load or create AI response patterns
        self._load_ai_response_patterns()
        
        trades = []
        equity_curve = []
        current_capital = self.initial_capital
        positions = {}
        
        # Get all unique dates
        all_dates = set()
        for symbol_data in data.values():
            all_dates.update(symbol_data.index)
        dates = sorted(list(all_dates))
        
        for date in dates:
            daily_pnl = 0
            
            for symbol, symbol_data in data.items():
                if date not in symbol_data.index:
                    continue
                
                # Get data up to current date
                current_data = symbol_data.loc[:date]
                if len(current_data) < 50:
                    continue
                
                # Calculate indicators
                indicators = self._calculate_indicators_with_weights(current_data, weight_config)
                
                # Simulate AI response
                ai_signal, ai_confidence = self._simulate_ai_response(symbol, indicators, date)
                
                # Combine technical and AI signals
                final_signal, final_confidence = self._combine_signals(
                    indicators, ai_signal, ai_confidence, weight_config
                )
                
                # Execute signal
                current_price = current_data.iloc[-1]['Close']
                trade_pnl = self._execute_signal_with_weights(
                    symbol, date, final_signal, current_price, final_confidence,
                    positions, current_capital, weight_config
                )
                daily_pnl += trade_pnl
                
                if trade_pnl != 0:
                    trades.append({
                        'symbol': symbol,
                        'date': date,
                        'signal': final_signal.value if final_signal else 'HOLD',
                        'price': current_price,
                        'confidence': final_confidence,
                        'ai_signal': ai_signal.value if ai_signal else 'HOLD',
                        'ai_confidence': ai_confidence,
                        'pnl': trade_pnl
                    })
            
            # Update equity curve
            current_capital += daily_pnl
            equity_curve.append({
                'date': date,
                'equity': current_capital,
                'return': (current_capital - self.initial_capital) / self.initial_capital
            })
        
        return self._calculate_metrics(trades, equity_curve, f"{weight_config.name}_ai_simulation")
    
    def _backtest_hybrid(self, data: Dict[str, pd.DataFrame], weight_config: WeightConfig) -> BacktestMetrics:
        """
        Backtest using a hybrid approach that combines technical and AI signals.
        
        This approach:
        1. Uses technical indicators for signal generation
        2. Applies AI-inspired conflict resolution
        3. Uses historical AI response patterns for validation
        """
        self.logger.info(f"🔀 Running hybrid backtest with {weight_config.name}")
        
        trades = []
        equity_curve = []
        current_capital = self.initial_capital
        positions = {}
        
        # Get all unique dates
        all_dates = set()
        for symbol_data in data.values():
            all_dates.update(symbol_data.index)
        dates = sorted(list(all_dates))
        
        for date in dates:
            daily_pnl = 0
            
            for symbol, symbol_data in data.items():
                if date not in symbol_data.index:
                    continue
                
                # Get data up to current date
                current_data = symbol_data.loc[:date]
                if len(current_data) < 50:
                    continue
                
                # Calculate indicators
                indicators = self._calculate_indicators_with_weights(current_data, weight_config)
                
                # Generate technical signal
                tech_signal, tech_confidence = self._generate_signal_with_weights(indicators, weight_config)
                
                # Apply AI-inspired conflict resolution
                final_signal, final_confidence = self._apply_ai_conflict_resolution(
                    indicators, tech_signal, tech_confidence, weight_config
                )
                
                # Execute signal
                current_price = current_data.iloc[-1]['Close']
                trade_pnl = self._execute_signal_with_weights(
                    symbol, date, final_signal, current_price, final_confidence,
                    positions, current_capital, weight_config
                )
                daily_pnl += trade_pnl
                
                if trade_pnl != 0:
                    trades.append({
                        'symbol': symbol,
                        'date': date,
                        'signal': final_signal.value if final_signal else 'HOLD',
                        'price': current_price,
                        'confidence': final_confidence,
                        'tech_signal': tech_signal.value if tech_signal else 'HOLD',
                        'tech_confidence': tech_confidence,
                        'pnl': trade_pnl
                    })
            
            # Update equity curve
            current_capital += daily_pnl
            equity_curve.append({
                'date': date,
                'equity': current_capital,
                'return': (current_capital - self.initial_capital) / self.initial_capital
            })
        
        return self._calculate_metrics(trades, equity_curve, f"{weight_config.name}_hybrid")
    
    def _calculate_indicators_with_weights(self, data: pd.DataFrame, weight_config: WeightConfig) -> Dict[str, IndicatorResult]:
        """Calculate indicators with custom weights."""
        # Use the existing indicators calculation
        indicators = self.indicators.calculate_all_indicators(data)
        
        # Apply custom weights
        weighted_indicators = {}
        for name, result in indicators.items():
            if result.confidence is None:
                continue
                
            # Apply weight based on indicator type
            if name in ['sma', 'ema', 'macd', 'vwap']:
                adjusted_confidence = result.confidence * weight_config.trend_weight
            elif name in ['rsi', 'stochastic', 'williams_r', 'cci', 'money_flow_index']:
                adjusted_confidence = result.confidence * weight_config.momentum_weight
            else:
                adjusted_confidence = result.confidence * weight_config.volatility_weight
            
            # Cap momentum indicators if needed
            if name in ['rsi', 'stochastic', 'williams_r', 'cci']:
                adjusted_confidence = min(adjusted_confidence, weight_config.momentum_cap)
            
            weighted_indicators[name] = IndicatorResult(
                value=result.value,
                signal=result.signal,
                confidence=adjusted_confidence,
                metadata=result.metadata
            )
        
        return weighted_indicators
    
    def _generate_signal_with_weights(self, indicators: Dict[str, IndicatorResult], weight_config: WeightConfig) -> Tuple[SignalType, float]:
        """Generate signal with custom weights."""
        if not indicators:
            return None, None
        
        buy_signals = 0
        sell_signals = 0
        total_confidence = 0
        signal_count = 0
        
        # Track signal categories
        trend_signals = {'buy': 0, 'sell': 0}
        momentum_signals = {'buy': 0, 'sell': 0}
        volatility_signals = {'buy': 0, 'sell': 0}
        
        for indicator_name, result in indicators.items():
            if result.confidence is None:
                continue
            
            # Categorize and weight indicators
            if indicator_name in ['sma', 'ema', 'macd', 'vwap']:
                if result.signal in [SignalType.BUY, SignalType.STRONG_BUY]:
                    trend_signals['buy'] += result.confidence * weight_config.trend_weight
                    buy_signals += result.confidence * weight_config.trend_weight
                elif result.signal in [SignalType.SELL, SignalType.STRONG_SELL]:
                    trend_signals['sell'] += result.confidence * weight_config.trend_weight
                    sell_signals += result.confidence * weight_config.trend_weight
            elif indicator_name in ['rsi', 'stochastic', 'williams_r', 'cci', 'money_flow_index']:
                if result.signal in [SignalType.BUY, SignalType.STRONG_BUY]:
                    momentum_signals['buy'] += result.confidence
                    buy_signals += result.confidence
                elif result.signal in [SignalType.SELL, SignalType.STRONG_SELL]:
                    momentum_signals['sell'] += result.confidence
                    sell_signals += result.confidence
            else:
                if result.signal in [SignalType.BUY, SignalType.STRONG_BUY]:
                    volatility_signals['buy'] += result.confidence * weight_config.volatility_weight
                    buy_signals += result.confidence * weight_config.volatility_weight
                elif result.signal in [SignalType.SELL, SignalType.STRONG_SELL]:
                    volatility_signals['sell'] += result.confidence * weight_config.volatility_weight
                    sell_signals += result.confidence * weight_config.volatility_weight
            
            total_confidence += result.confidence
            signal_count += 1
        
        if signal_count == 0:
            return None, None
        
        avg_confidence = total_confidence / signal_count
        
        # Check for conflicts
        trend_bullish = trend_signals['buy'] > trend_signals['sell']
        momentum_bullish = momentum_signals['buy'] > momentum_signals['sell']
        signal_conflict = trend_bullish != momentum_bullish
        
        # Apply conflict resolution
        if signal_conflict:
            avg_confidence *= weight_config.conflict_reduction
            # Reduce momentum influence when conflicting with trend
            momentum_signals['buy'] *= weight_config.conflict_reduction
            momentum_signals['sell'] *= weight_config.conflict_reduction
            # Recalculate totals
            buy_signals = trend_signals['buy'] + momentum_signals['buy'] + volatility_signals['buy']
            sell_signals = trend_signals['sell'] + momentum_signals['sell'] + volatility_signals['sell']
        
        # Determine final signal
        if buy_signals > sell_signals:
            if buy_signals > 2.5 and trend_bullish:
                signal = SignalType.STRONG_BUY
            elif buy_signals > 1.5:
                signal = SignalType.BUY
            else:
                signal = SignalType.HOLD
        elif sell_signals > buy_signals:
            if sell_signals > 2.5 and not trend_bullish:
                signal = SignalType.STRONG_SELL
            elif sell_signals > 1.5:
                signal = SignalType.SELL
            else:
                signal = SignalType.HOLD
        else:
            signal = SignalType.HOLD
        
        return signal, avg_confidence
    
    def _simulate_ai_response(self, symbol: str, indicators: Dict[str, IndicatorResult], date: datetime) -> Tuple[SignalType, float]:
        """Simulate AI response based on historical patterns and current indicators."""
        # This is a simplified simulation - in practice, you'd want to:
        # 1. Load historical AI responses for similar market conditions
        # 2. Use ML models to predict AI responses
        # 3. Consider market sentiment and news
        
        # For now, simulate based on indicator consensus
        buy_count = 0
        sell_count = 0
        total_confidence = 0
        
        for result in indicators.values():
            if result.confidence is None:
                continue
            
            if result.signal in [SignalType.BUY, SignalType.STRONG_BUY]:
                buy_count += 1
            elif result.signal in [SignalType.SELL, SignalType.STRONG_SELL]:
                sell_count += 1
            
            total_confidence += result.confidence
        
        if total_confidence == 0:
            return SignalType.HOLD, 0.5
        
        avg_confidence = total_confidence / len(indicators)
        
        # Simple simulation logic
        if buy_count > sell_count * 1.5:
            return SignalType.BUY, avg_confidence
        elif sell_count > buy_count * 1.5:
            return SignalType.SELL, avg_confidence
        else:
            return SignalType.HOLD, avg_confidence
    
    def _combine_signals(self, indicators: Dict[str, IndicatorResult], ai_signal: SignalType, ai_confidence: float, weight_config: WeightConfig) -> Tuple[SignalType, float]:
        """Combine technical and AI signals."""
        # Get technical signal
        tech_signal, tech_confidence = self._generate_signal_with_weights(indicators, weight_config)
        
        if tech_signal is None:
            return ai_signal, ai_confidence
        
        # Combine signals (simple average for now)
        if tech_signal == ai_signal:
            # Agreement - boost confidence
            final_confidence = (tech_confidence + ai_confidence) / 2 * 1.2
        else:
            # Disagreement - reduce confidence
            final_confidence = (tech_confidence + ai_confidence) / 2 * 0.8
        
        # Determine final signal (prefer technical for now)
        return tech_signal, final_confidence
    
    def _apply_ai_conflict_resolution(self, indicators: Dict[str, IndicatorResult], tech_signal: SignalType, tech_confidence: float, weight_config: WeightConfig) -> Tuple[SignalType, float]:
        """Apply AI-inspired conflict resolution."""
        if tech_signal is None:
            return SignalType.HOLD, 0.5
        
        # Check for conflicts in indicators
        trend_signals = {'buy': 0, 'sell': 0}
        momentum_signals = {'buy': 0, 'sell': 0}
        
        for name, result in indicators.items():
            if result.confidence is None:
                continue
            
            if name in ['sma', 'ema', 'macd', 'vwap']:
                if result.signal in [SignalType.BUY, SignalType.STRONG_BUY]:
                    trend_signals['buy'] += result.confidence
                elif result.signal in [SignalType.SELL, SignalType.STRONG_SELL]:
                    trend_signals['sell'] += result.confidence
            elif name in ['rsi', 'stochastic', 'williams_r', 'cci']:
                if result.signal in [SignalType.BUY, SignalType.STRONG_BUY]:
                    momentum_signals['buy'] += result.confidence
                elif result.signal in [SignalType.SELL, SignalType.STRONG_SELL]:
                    momentum_signals['sell'] += result.confidence
        
        # Check for conflicts
        trend_bullish = trend_signals['buy'] > trend_signals['sell']
        momentum_bullish = momentum_signals['buy'] > momentum_signals['sell']
        signal_conflict = trend_bullish != momentum_bullish
        
        if signal_conflict:
            # Apply conflict resolution
            tech_confidence *= weight_config.conflict_reduction
            if tech_signal in [SignalType.STRONG_BUY, SignalType.STRONG_SELL]:
                # Downgrade strong signals when there's conflict
                if tech_signal == SignalType.STRONG_BUY:
                    tech_signal = SignalType.BUY
                elif tech_signal == SignalType.STRONG_SELL:
                    tech_signal = SignalType.SELL
        
        return tech_signal, tech_confidence
    
    def _execute_signal_with_weights(self, symbol: str, date: datetime, signal: SignalType, price: float, confidence: float, positions: Dict, current_capital: float, weight_config: WeightConfig) -> float:
        """Execute signal and return P&L."""
        if signal is None or signal == SignalType.HOLD:
            return 0.0
        
        # Simple position sizing based on confidence
        position_size = min(confidence, 0.1) * current_capital
        
        if signal in [SignalType.BUY, SignalType.STRONG_BUY]:
            if symbol not in positions:
                # Open position
                quantity = position_size / price
                positions[symbol] = {
                    'quantity': quantity,
                    'entry_price': price,
                    'entry_date': date
                }
                return -position_size  # Cost
        elif signal in [SignalType.SELL, SignalType.STRONG_SELL]:
            if symbol in positions:
                # Close position
                position = positions[symbol]
                quantity = position['quantity']
                entry_price = position['entry_price']
                pnl = quantity * (price - entry_price)
                del positions[symbol]
                return pnl
        
        return 0.0
    
    def _calculate_metrics(self, trades: List[Dict], equity_curve: List[Dict], config_name: str) -> BacktestMetrics:
        """Calculate comprehensive metrics for backtest results."""
        if not trades:
            return BacktestMetrics(
                total_return=0.0, annualized_return=0.0, sharpe_ratio=0.0, max_drawdown=0.0,
                win_rate=0.0, profit_factor=0.0, signal_accuracy=0.0, precision=0.0,
                recall=0.0, f1_score=0.0, total_trades=0, winning_trades=0, losing_trades=0,
                avg_win=0.0, avg_loss=0.0, avg_trade_duration=0.0, volatility=0.0,
                sortino_ratio=0.0, calmar_ratio=0.0, max_consecutive_losses=0,
                ai_agreement_rate=0.0, conflict_resolution_effectiveness=0.0, weight_stability=0.0
            )
        
        # Calculate basic metrics
        total_trades = len(trades)
        winning_trades = len([t for t in trades if t['pnl'] > 0])
        losing_trades = len([t for t in trades if t['pnl'] < 0])
        
        if total_trades > 0:
            win_rate = winning_trades / total_trades
        else:
            win_rate = 0.0
        
        # Calculate returns
        if equity_curve:
            initial_equity = self.initial_capital
            final_equity = equity_curve[-1]['equity']
            total_return = (final_equity - initial_equity) / initial_equity
        else:
            total_return = 0.0
        
        # Calculate other metrics
        avg_win = np.mean([t['pnl'] for t in trades if t['pnl'] > 0]) if winning_trades > 0 else 0.0
        avg_loss = np.mean([t['pnl'] for t in trades if t['pnl'] < 0]) if losing_trades > 0 else 0.0
        
        profit_factor = abs(avg_win * winning_trades / (avg_loss * losing_trades)) if losing_trades > 0 and avg_loss != 0 else 0.0
        
        # Calculate Sharpe ratio (simplified)
        if equity_curve and len(equity_curve) > 1:
            returns = [e['return'] for e in equity_curve]
            volatility = np.std(returns) if len(returns) > 1 else 0.0
            sharpe_ratio = np.mean(returns) / volatility if volatility > 0 else 0.0
        else:
            volatility = 0.0
            sharpe_ratio = 0.0
        
        # Calculate max drawdown
        if equity_curve:
            equity_values = [e['equity'] for e in equity_curve]
            peak = equity_values[0]
            max_drawdown = 0.0
            for equity in equity_values:
                if equity > peak:
                    peak = equity
                drawdown = (peak - equity) / peak
                max_drawdown = max(max_drawdown, drawdown)
        else:
            max_drawdown = 0.0
        
        return BacktestMetrics(
            total_return=total_return,
            annualized_return=total_return,  # Simplified
            sharpe_ratio=sharpe_ratio,
            max_drawdown=max_drawdown,
            win_rate=win_rate,
            profit_factor=profit_factor,
            signal_accuracy=0.0,  # Would need actual vs predicted
            precision=0.0,
            recall=0.0,
            f1_score=0.0,
            total_trades=total_trades,
            winning_trades=winning_trades,
            losing_trades=losing_trades,
            avg_win=avg_win,
            avg_loss=avg_loss,
            avg_trade_duration=0.0,
            volatility=volatility,
            sortino_ratio=0.0,
            calmar_ratio=0.0,
            max_consecutive_losses=0,
            ai_agreement_rate=0.0,
            conflict_resolution_effectiveness=0.0,
            weight_stability=0.0
        )
    
    def _load_historical_data(self, symbols: List[str], start_date: str, end_date: str) -> Dict[str, pd.DataFrame]:
        """Load historical data for backtesting."""
        data = {}
        
        for symbol in symbols:
            try:
                ticker = yf.Ticker(symbol)
                df = ticker.history(start=start_date, end=end_date, interval='1d')
                
                if not df.empty and len(df) >= 50:
                    data[symbol] = df
                    self.logger.info(f"✅ Loaded {len(df)} rows for {symbol}")
                else:
                    self.logger.warning(f"⚠️ Insufficient data for {symbol}: {len(df)} rows")
                    
            except Exception as e:
                self.logger.error(f"❌ Failed to load data for {symbol}: {e}")
        
        return data
    
    def _load_ai_response_patterns(self):
        """Load historical AI response patterns for simulation."""
        # This would load actual AI response patterns from historical data
        # For now, we'll use a simple simulation
        self.ai_response_patterns = {
            'bullish_momentum': {'BUY': 0.7, 'HOLD': 0.2, 'SELL': 0.1},
            'bearish_momentum': {'BUY': 0.1, 'HOLD': 0.2, 'SELL': 0.7},
            'neutral': {'BUY': 0.3, 'HOLD': 0.4, 'SELL': 0.3}
        }
    
    def _generate_backtest_report(self, results: Dict[str, BacktestMetrics], symbols: List[str], start_date: str, end_date: str):
        """Generate comprehensive backtest report."""
        report_path = f"backtest_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
        
        # Create HTML report
        html_content = f"""
        <html>
        <head>
            <title>AI Weight Backtest Report</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                table {{ border-collapse: collapse; width: 100%; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
                .metric {{ font-weight: bold; }}
                .positive {{ color: green; }}
                .negative {{ color: red; }}
            </style>
        </head>
        <body>
            <h1>AI Weight Backtest Report</h1>
            <p><strong>Symbols:</strong> {', '.join(symbols)}</p>
            <p><strong>Period:</strong> {start_date} to {end_date}</p>
            <p><strong>Generated:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            
            <h2>Results Summary</h2>
            <table>
                <tr>
                    <th>Configuration</th>
                    <th>Total Return</th>
                    <th>Sharpe Ratio</th>
                    <th>Max Drawdown</th>
                    <th>Win Rate</th>
                    <th>Total Trades</th>
                </tr>
        """
        
        for config_name, metrics in results.items():
            html_content += f"""
                <tr>
                    <td>{config_name}</td>
                    <td class="{'positive' if metrics.total_return > 0 else 'negative'}">{metrics.total_return:.2%}</td>
                    <td>{metrics.sharpe_ratio:.2f}</td>
                    <td class="negative">{metrics.max_drawdown:.2%}</td>
                    <td>{metrics.win_rate:.2%}</td>
                    <td>{metrics.total_trades}</td>
                </tr>
            """
        
        html_content += """
            </table>
        </body>
        </html>
        """
        
        with open(report_path, 'w') as f:
            f.write(html_content)
        
        self.logger.info(f"📊 Backtest report generated: {report_path}")
        
        # Also save results as JSON
        json_path = f"backtest_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        results_dict = {}
        for config_name, metrics in results.items():
            results_dict[config_name] = {
                'total_return': metrics.total_return,
                'sharpe_ratio': metrics.sharpe_ratio,
                'max_drawdown': metrics.max_drawdown,
                'win_rate': metrics.win_rate,
                'total_trades': metrics.total_trades,
                'profit_factor': metrics.profit_factor
            }
        
        with open(json_path, 'w') as f:
            json.dump(results_dict, f, indent=2)
        
        self.logger.info(f"📄 Backtest results saved: {json_path}")


def main():
    """Main function to run AI weight backtesting."""
    config = get_config()
    backtester = AIWeightBacktester(config.model_dump())
    
    # Test with a few symbols
    symbols = ['AAPL', 'MSFT', 'GOOGL']
    start_date = '2024-01-01'
    end_date = '2024-12-31'
    
    print("🚀 Starting AI weight backtesting...")
    results = backtester.run_comprehensive_backtest(symbols, start_date, end_date)
    
    print("\n📊 Backtest Results Summary:")
    print("=" * 80)
    
    for config_name, metrics in results.items():
        print(f"\n{config_name}:")
        print(f"  Total Return: {metrics.total_return:.2%}")
        print(f"  Sharpe Ratio: {metrics.sharpe_ratio:.2f}")
        print(f"  Max Drawdown: {metrics.max_drawdown:.2%}")
        print(f"  Win Rate: {metrics.win_rate:.2%}")
        print(f"  Total Trades: {metrics.total_trades}")


if __name__ == "__main__":
    main() 