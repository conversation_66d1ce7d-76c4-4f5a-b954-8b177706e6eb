"""
AI-Nvestor: Main Application Runner
"""

import sys
from pathlib import Path

# Add the 'src' directory to the Python path
# This allows for clean imports from the 'src' directory
# (e.g., `from core import ...` instead of `from src.core import ...`)
project_root = Path(__file__).resolve().parent
sys.path.insert(0, str(project_root / 'src'))

# Now that the path is set up, we can import from `src`
from main import main

if __name__ == "__main__":
    # Execute the main application logic
    main()
