"""
Comprehensive test suite for AI-Nvestor platform.

This module tests all major components:
- Configuration management
- Market data services
- Signal generation
- Alert system
- Exception handling
- Performance metrics
"""

import sys
import os
import json
from datetime import datetime, timedelta
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_basic_imports():
    """Test that all modules can be imported."""
    print("Testing basic imports...")
    
    try:
        from utils.config import setup_config, get_config
        from utils.logging import setup_logging, get_logger
        from services.indicators import TechnicalIndicators, SignalType
        from services.market_data import MarketDataService
        from services.signals import SignalGenerator
        from services.alerts import AlertSystem, AlertType, AlertPriority
        from core.exceptions import TradingPlatformError, MarketDataError
        from core.base_client import BaseAPIClient
        print("✅ All imports successful")
        return True
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False

def test_configuration():
    """Test configuration management."""
    print("\nTesting configuration...")
    
    try:
        from utils.config import setup_config
        
        # Test config loading
        config_manager = setup_config("config.json")
        config = config_manager.get_config()
        
        # Verify key sections exist
        assert hasattr(config, 'watchlist')
        assert hasattr(config, 'timeframes')
        assert hasattr(config, 'indicators')
        assert hasattr(config, 'risk_management')
        
        print("✅ Configuration loaded successfully")
        print(f"   Watchlist: {len(config.watchlist)} symbols")
        print(f"   Timeframe: {config.timeframes.interval}")
        print(f"   RSI Period: {config.indicators.rsi_period}")
        
        return True
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False

def test_logging():
    """Test logging system."""
    print("\nTesting logging...")
    
    try:
        from utils.logging import setup_logging
        from utils.config import get_config
        
        config = get_config()
        logger = setup_logging(config.logging.model_dump())
        
        # Test different log types
        logger.log_startup()
        logger.log_performance({'event': 'test', 'value': 100})
        logger.log_signal('AAPL', 'BUY', 0.8, {'rsi': 30})
        
        print("✅ Logging system working")
        return True
    except Exception as e:
        print(f"❌ Logging error: {e}")
        return False

def test_stock_data():
    """Test stock data retrieval."""
    print("\nTesting stock data...")
    
    try:
        from services.market_data import MarketDataService
        from utils.config import get_config
        from utils.logging import setup_logging
        
        config = get_config()
        logger = setup_logging(config.logging.model_dump())
        market_data = MarketDataService(config.model_dump())
        
        # Test data retrieval
        data = market_data.get_stock_data('AAPL', interval='1d', days=5)
        
        if data is not None and not data.empty:
            print(f"✅ Stock data retrieved successfully")
            print(f"   Data points: {len(data)}")
            print(f"   Columns: {list(data.columns)}")
            return True
        else:
            print("❌ No data retrieved")
            return False
            
    except Exception as e:
        print(f"❌ Stock data error: {e}")
        return False

def test_indicators():
    """Test technical indicators."""
    print("\nTesting technical indicators...")
    
    try:
        from services.indicators import TechnicalIndicators
        from services.market_data import MarketDataService
        from utils.config import get_config
        from utils.logging import setup_logging
        
        config = get_config()
        logger = setup_logging(config.logging.model_dump())
        market_data = MarketDataService(config.model_dump())
        indicators = TechnicalIndicators(config.model_dump())
        
        # Get sample data
        data = market_data.get_stock_data('AAPL', interval='1d', days=30)
        
        if data is not None and not data.empty:
            # Calculate indicators
            results = indicators.calculate_all_indicators(data)
            
            print(f"✅ Technical indicators calculated successfully")
            print(f"   Indicators calculated: {len(results)}")
            print(f"   Available indicators: {list(results.keys())}")
            return True
        else:
            print("❌ No data available for indicator calculation")
            return False
            
    except Exception as e:
        print(f"❌ Indicators error: {e}")
        return False

def test_backtesting():
    """Test backtesting functionality."""
    print("\nTesting backtesting...")
    
    try:
        from backtest import run_backtest
        from utils.config import get_config
        from utils.logging import setup_logging
        
        config = get_config()
        logger = setup_logging(config.logging.model_dump())
        
        # Run a simple backtest
        symbols = ['AAPL']
        start_date = '2024-01-01'
        end_date = '2024-01-31'
        
        results = run_backtest(symbols, start_date, end_date, config.model_dump())
        
        print(f"✅ Backtesting completed successfully")
        print(f"   Total return: {results.total_return:.2%}")
        print(f"   Sharpe ratio: {results.sharpe_ratio:.2f}")
        print(f"   Total trades: {results.total_trades}")
        return True
        
    except Exception as e:
        print(f"❌ Backtesting error: {e}")
        return False

def test_platform_integration():
    """Test full platform integration."""
    print("\nTesting platform integration...")
    
    try:
        from main import TradingPlatform
        
        # Initialize platform
        platform = TradingPlatform("config.json")
        
        # Test analysis
        results = platform.run_analysis(['AAPL'])
        
        if results and 'AAPL' in results:
            result = results['AAPL']
            if 'error' not in result:
                print(f"✅ Platform integration successful")
                print(f"   Signal: {result.get('signal', 'N/A')}")
                print(f"   Confidence: {result.get('confidence', 0):.2f}")
                return True
            else:
                print(f"❌ Analysis error: {result.get('error', 'Unknown error')}")
                return False
        else:
            print("❌ No analysis results")
            return False
            
    except Exception as e:
        print(f"❌ Platform integration error: {e}")
        return False

def run_performance_benchmark():
    """Run performance benchmark."""
    print("\nRunning performance benchmark...")
    
    try:
        from utils.config import get_config
        from utils.logging import setup_logging
        from services.market_data import MarketDataService
        from services.indicators import TechnicalIndicators
        import time
        
        config = get_config()
        logger = setup_logging(config.logging.model_dump())
        
        # Benchmark data retrieval
        market_data = MarketDataService(config.model_dump())
        start_time = time.time()
        
        data = market_data.get_stock_data('AAPL', interval='1d', days=30)
        data_time = time.time() - start_time
        
        if data is not None and not data.empty:
            # Benchmark indicator calculation
            indicators = TechnicalIndicators(config.model_dump())
            start_time = time.time()
            
            results = indicators.calculate_all_indicators(data)
            indicator_time = time.time() - start_time
            
            print(f"✅ Performance benchmark completed")
            print(f"   Data retrieval: {data_time:.3f}s")
            print(f"   Indicator calculation: {indicator_time:.3f}s")
            print(f"   Total time: {data_time + indicator_time:.3f}s")
            return True
        else:
            print("❌ No data available for benchmark")
            return False
            
    except Exception as e:
        print(f"❌ Performance benchmark error: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 AI-Nvestor Platform Test Suite")
    print("=" * 50)
    
    tests = [
        ("Basic Imports", test_basic_imports),
        ("Configuration", test_configuration),
        ("Logging", test_logging),
        ("Stock Data", test_stock_data),
        ("Technical Indicators", test_indicators),
        ("Backtesting", test_backtesting),
        ("Platform Integration", test_platform_integration),
        ("Performance Benchmark", run_performance_benchmark)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} failed")
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Platform is ready to use.")
        return 0
    else:
        print("⚠️ Some tests failed. Please check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 