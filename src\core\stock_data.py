import yfinance as yf
import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
from typing import Optional

class StockData:
    """
    Handles stock data retrieval and basic preprocessing.
    
    Technical Notes:
    - Uses yfinance for reliable, free market data
    - Implements error handling for API failures
    - Returns pandas DataFrame for efficient data manipulation
    
    Financial Notes:
    - Fetches OHLCV (Open, High, Low, Close, Volume) data
    - Intraday data helps identify short-term momentum shifts
    - Volume data confirms price movement strength
    """

    def __init__(self, symbol: str):
        """
        Initialize stock data handler.
        
        Args:
            symbol: Stock symbol (e.g., 'AAPL', 'MSFT')
        """
        try:
            if not symbol or not isinstance(symbol, str):
                raise ValueError("Symbol must be a non-empty string")
            
            self.symbol = symbol.upper().strip()
            self.ticker = yf.Ticker(self.symbol)
            
        except Exception as e:
            logging.error(f"Error initializing StockData for {symbol}: {e}")
            raise

    def get_data(self, interval='5m', days=1) -> Optional[pd.DataFrame]:
        """
        Fetch recent market data for analysis.
        
        Technical Parameters:
        - interval: Data granularity (5m = 5 minutes)
        - days: Amount of historical data to fetch
        
        Financial Context:
        - 5-minute intervals capture intraday momentum
        - One day of history provides recent market context
        - Pre-market and after-hours data included for gap analysis
        """
        try:
            # Validate parameters
            if not self._validate_parameters(interval, days):
                return None
            
            # Calculate time range
            end = datetime.now()
            start = end - timedelta(days=days)
            
            # Fetch data with timeout protection
            df = self._fetch_data_with_timeout(start, end, interval)
            
            if df is None or df.empty:
                logging.warning(f"No data retrieved for {self.symbol}")
                return None
            
            # Validate and clean data
            df = self._validate_and_clean_data(df)
            
            if df is None or df.empty:
                logging.warning(f"Data validation failed for {self.symbol}")
                return None
                
            return df
            
        except Exception as e:
            logging.error(f"Error fetching data for {self.symbol}: {str(e)}")
            return None
    
    def _validate_parameters(self, interval: str, days: int) -> bool:
        """Validate input parameters."""
        try:
            # Validate interval
            valid_intervals = ['1m', '2m', '5m', '15m', '30m', '60m', '90m', '1h', '1d', '5d', '1wk', '1mo', '3mo']
            if interval not in valid_intervals:
                logging.error(f"Invalid interval '{interval}' for {self.symbol}")
                return False
            
            # Validate days
            if not isinstance(days, (int, float)) or days <= 0 or days > 365:
                logging.error(f"Invalid days parameter '{days}' for {self.symbol}")
                return False
            
            return True
            
        except Exception as e:
            logging.error(f"Error validating parameters for {self.symbol}: {e}")
            return False
    
    def _fetch_data_with_timeout(self, start: datetime, end: datetime, interval: str) -> Optional[pd.DataFrame]:
        """Fetch data with timeout protection using cross-platform threading."""
        try:
            import concurrent.futures
            import threading
            
            # Use ThreadPoolExecutor for cross-platform timeout
            with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                # Submit the data fetching task
                future = executor.submit(
                    self.ticker.history,
                    start=start,
                    end=end,
                    interval=interval
                )
                
                try:
                    # Wait for result with 30 second timeout
                    df = future.result(timeout=30)
                    return df
                    
                except concurrent.futures.TimeoutError:
                    logging.error(f"Timeout fetching data for {self.symbol}")
                    return None
                    
        except Exception as e:
            logging.error(f"Error fetching data with timeout for {self.symbol}: {e}")
            return None
    
    def _validate_and_clean_data(self, df: pd.DataFrame) -> Optional[pd.DataFrame]:
        """Validate and clean the fetched data."""
        try:
            if df is None or df.empty:
                return None
            
            # Check for required columns
            required_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
            if not all(col in df.columns for col in required_columns):
                logging.error(f"Missing required columns for {self.symbol}")
                return None
            
            # Remove rows with all NaN values
            df = df.dropna(how='all')
            
            if df.empty:
                logging.warning(f"All data rows were NaN for {self.symbol}")
                return None
            
            # Check for reasonable price values
            if (df['High'] < df['Low']).any():
                logging.warning(f"Invalid price data (High < Low) for {self.symbol}")
                return None
            
            if (df['Open'] < 0).any() or (df['Close'] < 0).any():
                logging.warning(f"Negative price values for {self.symbol}")
                return None
            
            # Check for excessive price changes (potential data errors)
            price_changes = df['Close'].pct_change().abs()
            if (price_changes > 0.5).any():  # 50% price change
                logging.warning(f"Excessive price changes detected for {self.symbol}")
                return None
            
            # Handle NaN values in critical columns using modern pandas syntax
            df['Close'] = df['Close'].ffill().bfill()
            df['Volume'] = df['Volume'].fillna(0)
            
            # Ensure data types are correct
            for col in ['Open', 'High', 'Low', 'Close']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            df['Volume'] = pd.to_numeric(df['Volume'], errors='coerce').fillna(0)
            
            return df
            
        except Exception as e:
            logging.error(f"Error validating and cleaning data for {self.symbol}: {e}")
            return None
    
    def get_stock_info(self) -> Optional[dict]:
        """
        Get additional stock information.
        
        Returns:
            Dictionary with stock information or None if error
        """
        try:
            info = self.ticker.info
            
            if not info or not isinstance(info, dict):
                logging.warning(f"No stock info available for {self.symbol}")
                return None
            
            # Extract key information
            stock_info = {
                'symbol': self.symbol,
                'name': info.get('longName', 'Unknown'),
                'sector': info.get('sector', 'Unknown'),
                'industry': info.get('industry', 'Unknown'),
                'market_cap': info.get('marketCap', 0),
                'pe_ratio': info.get('trailingPE', 0),
                'dividend_yield': info.get('dividendYield', 0),
                'beta': info.get('beta', 0),
                'volume_avg': info.get('averageVolume', 0)
            }
            
            return stock_info
            
        except Exception as e:
            logging.error(f"Error getting stock info for {self.symbol}: {e}")
            return None
    
    def get_current_price(self) -> Optional[float]:
        """
        Get current stock price.
        
        Returns:
            Current price or None if error
        """
        try:
            # Get the most recent data
            df = self.get_data(interval='1m', days=1)
            
            if df is None or df.empty:
                return None
            
            current_price = df['Close'].iloc[-1]
            
            if pd.isna(current_price) or np.isinf(current_price):
                return None
            
            return float(current_price)
            
        except Exception as e:
            logging.error(f"Error getting current price for {self.symbol}: {e}")
            return None 