"""
Apify service for historical Reddit sentiment data.

This module provides:
- Historical Reddit sentiment data scraping via Apify
- Backtesting support with historical sentiment data
- Iterative learning capabilities
- Data storage and retrieval for analysis
"""

import asyncio
import httpx
import json
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
import time
from dataclasses import dataclass

from utils.logging import get_logger
from services.sentiment_data import SentimentData, SentimentSource


@dataclass
class ApifyRedditData:
    """Structure for Apify Reddit data."""
    post_id: str
    title: str
    text: str
    score: int
    upvote_ratio: float
    num_comments: int
    created_utc: int
    subreddit: str
    url: str
    author: str
    comments: List[Dict[str, Any]]


class ApifyService:
    """
    Apify service for historical Reddit sentiment data.
    
    Features:
    - Historical Reddit data scraping
    - Backtesting support
    - Data storage and retrieval
    - Iterative learning capabilities
    """
    
    def __init__(self, config: Dict):
        self.config = config
        self.logger = get_logger()
        self.client = httpx.AsyncClient(timeout=60.0)
        
        # Apify configuration
        self.apify_config = config.get('sentiment_data', {}).get('apify', {})
        self.api_token = self.apify_config.get('api_token', '')
        self.base_url = "https://api.apify.com/v2"
        
        # Reddit scraper configuration
        self.reddit_scraper_id = self.apify_config.get('reddit_scraper_id', '')
        self.max_posts = self.apify_config.get('max_posts', 1000)
        self.max_comments = self.apify_config.get('max_comments', 100)
        
        # Storage
        self.data_dir = Path("data/historical/reddit")
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
    async def get_historical_reddit_data(self, symbol: str, start_date: str, end_date: str) -> List[SentimentData]:
        """
        Get historical Reddit data for a symbol using Apify.
        
        Args:
            symbol: Stock symbol
            start_date: Start date (YYYY-MM-DD)
            end_date: End date (YYYY-MM-DD)
            
        Returns:
            List of sentiment data points
        """
        try:
            # Check if we have cached data
            cached_data = self._get_cached_historical_data(symbol, start_date, end_date)
            if cached_data:
                self.logger.info(f"📊 Using cached historical Reddit data for {symbol}")
                return cached_data
            
            self.logger.info(f"🔍 Fetching historical Reddit data for {symbol} from {start_date} to {end_date}")
            
            # Run Apify scraper
            run_id = await self._run_reddit_scraper(symbol, start_date, end_date)
            if not run_id:
                self.logger.error(f"❌ Failed to start Apify scraper for {symbol}")
                return []
            
            # Wait for completion and get results
            results = await self._get_scraper_results(run_id)
            if not results:
                self.logger.error(f"❌ No results from Apify scraper for {symbol}")
                return []
            
            # Convert to sentiment data
            sentiment_data = self._convert_to_sentiment_data(results, symbol)
            
            # Cache the results
            if sentiment_data:
                self._cache_historical_data(symbol, start_date, end_date, sentiment_data)
            
            self.logger.info(f"✅ Found {len(sentiment_data)} historical Reddit data points for {symbol}")
            return sentiment_data
            
        except Exception as e:
            self.logger.error(f"❌ Error fetching historical Reddit data for {symbol}: {e}")
            return []
    
    async def _run_reddit_scraper(self, symbol: str, start_date: str, end_date: str) -> Optional[str]:
        """
        Run the Reddit scraper on Apify.
        
        Args:
            symbol: Stock symbol
            start_date: Start date
            end_date: End date
            
        Returns:
            Run ID if successful, None otherwise
        """
        try:
            # Prepare scraper input
            scraper_input = {
                "subreddits": [
                    "stocks",
                    "investing", 
                    "wallstreetbets",
                    "stockmarket",
                    "options",
                    "daytrading",
                    "investments",
                    "financialindependence"
                ],
                "searchTerms": [symbol],
                "maxRequestRetries": 3,
                "maxConcurrency": 10,
                "maxPostsPerSubreddit": self.max_posts,
                "maxCommentsPerPost": self.max_comments,
                "startDate": start_date,
                "endDate": end_date,
                "sortType": "new"
            }
            
            # Start the scraper
            url = f"{self.base_url}/acts/{self.reddit_scraper_id}/runs"
            headers = {
                "Authorization": f"Bearer {self.api_token}",
                "Content-Type": "application/json"
            }
            
            response = await self.client.post(
                url,
                json=scraper_input,
                headers=headers
            )
            response.raise_for_status()
            
            data = response.json()
            run_id = data.get('data', {}).get('id')
            
            if run_id:
                self.logger.info(f"🚀 Started Apify scraper run: {run_id}")
                return run_id
            else:
                self.logger.error("❌ No run ID returned from Apify")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ Error starting Apify scraper: {e}")
            return None
    
    async def _get_scraper_results(self, run_id: str) -> Optional[List[Dict]]:
        """
        Get results from a completed Apify scraper run.
        
        Args:
            run_id: Apify run ID
            
        Returns:
            List of scraped data or None if failed
        """
        try:
            # Wait for completion
            max_wait_time = 3600  # 1 hour
            wait_interval = 30  # 30 seconds
            waited_time = 0
            
            while waited_time < max_wait_time:
                # Check run status
                status_url = f"{self.base_url}/acts/runs/{run_id}"
                headers = {"Authorization": f"Bearer {self.api_token}"}
                
                response = await self.client.get(status_url, headers=headers)
                response.raise_for_status()
                
                data = response.json()
                status = data.get('data', {}).get('status')
                
                if status == 'SUCCEEDED':
                    self.logger.info(f"✅ Apify scraper completed: {run_id}")
                    break
                elif status in ['FAILED', 'ABORTED']:
                    self.logger.error(f"❌ Apify scraper failed: {status}")
                    return None
                
                # Wait and check again
                await asyncio.sleep(wait_interval)
                waited_time += wait_interval
                self.logger.info(f"⏳ Waiting for Apify scraper completion... ({waited_time}s)")
            
            if waited_time >= max_wait_time:
                self.logger.error("❌ Apify scraper timed out")
                return None
            
            # Get results
            results_url = f"{self.base_url}/acts/runs/{run_id}/dataset/items"
            response = await self.client.get(results_url, headers=headers)
            response.raise_for_status()
            
            results = response.json()
            self.logger.info(f"📊 Retrieved {len(results)} items from Apify")
            return results
            
        except Exception as e:
            self.logger.error(f"❌ Error getting Apify results: {e}")
            return None
    
    def _convert_to_sentiment_data(self, results: List[Dict], symbol: str) -> List[SentimentData]:
        """
        Convert Apify results to sentiment data.
        
        Args:
            results: Raw Apify results
            symbol: Stock symbol
            
        Returns:
            List of sentiment data points
        """
        sentiment_data = []
        
        for item in results:
            try:
                # Extract post data
                post_data = ApifyRedditData(
                    post_id=item.get('id', ''),
                    title=item.get('title', ''),
                    text=item.get('text', ''),
                    score=item.get('score', 0),
                    upvote_ratio=item.get('upvoteRatio', 0.0),
                    num_comments=item.get('numComments', 0),
                    created_utc=item.get('createdUtc', 0),
                    subreddit=item.get('subreddit', ''),
                    url=item.get('url', ''),
                    author=item.get('author', ''),
                    comments=item.get('comments', [])
                )
                
                # Analyze sentiment for post
                post_text = f"{post_data.title} {post_data.text}"
                sentiment_score, confidence = self._analyze_sentiment(post_text)
                
                if sentiment_score != 0:  # Only include if sentiment detected
                    sentiment_data.append(SentimentData(
                        symbol=symbol,
                        source=SentimentSource.REDDIT,
                        sentiment_score=sentiment_score,
                        confidence=confidence,
                        timestamp=datetime.fromtimestamp(post_data.created_utc),
                        text=post_text[:200],
                        metadata={
                            'type': 'post',
                            'subreddit': post_data.subreddit,
                            'post_id': post_data.post_id,
                            'score': post_data.score,
                            'upvote_ratio': post_data.upvote_ratio,
                            'num_comments': post_data.num_comments,
                            'url': post_data.url,
                            'author': post_data.author
                        }
                    ))
                
                # Analyze comments
                for comment in post_data.comments:
                    comment_text = comment.get('text', '')
                    if len(comment_text.strip()) > 10:
                        comment_sentiment, comment_confidence = self._analyze_sentiment(comment_text)
                        
                        if comment_sentiment != 0:  # Only include if sentiment detected
                            sentiment_data.append(SentimentData(
                                symbol=symbol,
                                source=SentimentSource.REDDIT,
                                sentiment_score=comment_sentiment,
                                confidence=comment_confidence,
                                timestamp=datetime.fromtimestamp(comment.get('createdUtc', post_data.created_utc)),
                                text=comment_text[:200],
                                metadata={
                                    'type': 'comment',
                                    'subreddit': post_data.subreddit,
                                    'post_id': post_data.post_id,
                                    'comment_id': comment.get('id', ''),
                                    'score': comment.get('score', 0),
                                    'url': comment.get('url', ''),
                                    'author': comment.get('author', '')
                                }
                            ))
                
            except Exception as e:
                self.logger.warning(f"⚠️ Error processing Apify result: {e}")
                continue
        
        return sentiment_data
    
    def _analyze_sentiment(self, text: str) -> Tuple[float, float]:
        """
        Analyze sentiment of text using keyword-based scoring.
        
        Args:
            text: Text to analyze
            
        Returns:
            Tuple of (sentiment_score, confidence)
        """
        if not text or len(text.strip()) < 10:
            return 0.0, 0.0
        
        # Sentiment keywords (same as in RedditSentimentService)
        positive_keywords = {
            'bullish', 'moon', 'rocket', 'pump', 'buy', 'long', 'hold', 'diamond', 'hands',
            'tendies', 'gains', 'profit', 'win', 'success', 'growth', 'up', 'rise', 'surge',
            'breakout', 'rally', 'soar', 'jump', 'climb', 'gain', 'positive', 'good', 'great',
            'amazing', 'incredible', 'fantastic', 'excellent', 'outstanding', 'perfect'
        }
        
        negative_keywords = {
            'bearish', 'dump', 'sell', 'short', 'crash', 'drop', 'fall', 'decline', 'loss',
            'paper', 'hands', 'bagholder', 'rekt', 'fomo', 'panic', 'fear', 'down', 'fall',
            'plunge', 'tank', 'crash', 'dump', 'sell', 'negative', 'bad', 'terrible', 'awful',
            'horrible', 'disaster', 'catastrophe', 'failure', 'lose', 'losing', 'lost'
        }
        
        # Convert to lowercase for analysis
        text_lower = text.lower()
        
        # Count positive and negative keywords
        positive_count = sum(1 for keyword in positive_keywords if keyword in text_lower)
        negative_count = sum(1 for keyword in negative_keywords if keyword in text_lower)
        
        # Calculate sentiment score (-1.0 to 1.0)
        total_keywords = positive_count + negative_count
        if total_keywords == 0:
            return 0.0, 0.0
        
        # Normalize score
        sentiment_score = (positive_count - negative_count) / total_keywords
        
        # Calculate confidence based on keyword density and text length
        keyword_density = total_keywords / len(text_lower.split())
        confidence = min(1.0, keyword_density * 50)  # Scale by density
        
        # Boost confidence for longer texts with more keywords
        if len(text_lower.split()) > 20 and total_keywords > 2:
            confidence = min(1.0, confidence * 1.5)
        
        return sentiment_score, confidence
    
    def _get_cached_historical_data(self, symbol: str, start_date: str, end_date: str) -> Optional[List[SentimentData]]:
        """Get cached historical Reddit data."""
        try:
            cache_file = self.data_dir / f"apify_{symbol}_{start_date}_{end_date}.json"
            
            if cache_file.exists():
                # Check if cache is still valid (24 hours)
                cache_age = time.time() - cache_file.stat().st_mtime
                if cache_age < (24 * 60 * 60):  # 24 hours
                    with open(cache_file, 'r', encoding='utf-8') as f:
                        cached_data = json.load(f)
                    
                    # Convert back to SentimentData objects
                    sentiment_data = []
                    for item in cached_data:
                        sentiment_data.append(SentimentData(
                            symbol=item['symbol'],
                            source=SentimentSource.REDDIT,
                            sentiment_score=item['sentiment_score'],
                            confidence=item['confidence'],
                            timestamp=datetime.fromisoformat(item['timestamp']),
                            text=item['text'],
                            metadata=item['metadata']
                        ))
                    
                    return sentiment_data
            
            return None
            
        except Exception as e:
            self.logger.error(f"❌ Error reading cached historical data: {e}")
            return None
    
    def _cache_historical_data(self, symbol: str, start_date: str, end_date: str, sentiment_data: List[SentimentData]):
        """Cache historical Reddit data."""
        try:
            cache_file = self.data_dir / f"apify_{symbol}_{start_date}_{end_date}.json"
            
            # Convert to JSON-serializable format
            cache_data = []
            for item in sentiment_data:
                cache_data.append({
                    'symbol': item.symbol,
                    'source': item.source.value,
                    'sentiment_score': item.sentiment_score,
                    'confidence': item.confidence,
                    'timestamp': item.timestamp.isoformat(),
                    'text': item.text,
                    'metadata': item.metadata
                })
            
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"💾 Cached historical Reddit data for {symbol}")
            
        except Exception as e:
            self.logger.error(f"❌ Error caching historical data: {e}")
    
    async def get_backtesting_data(self, symbol: str, start_date: str, end_date: str) -> pd.DataFrame:
        """
        Get historical Reddit data formatted for backtesting.
        
        Args:
            symbol: Stock symbol
            start_date: Start date (YYYY-MM-DD)
            end_date: End date (YYYY-MM-DD)
            
        Returns:
            DataFrame with historical sentiment data
        """
        sentiment_data = await self.get_historical_reddit_data(symbol, start_date, end_date)
        
        if not sentiment_data:
            return pd.DataFrame()
        
        # Convert to DataFrame
        df_data = []
        for item in sentiment_data:
            df_data.append({
                'date': item.timestamp,
                'sentiment_score': item.sentiment_score,
                'confidence': item.confidence,
                'text': item.text,
                'source': item.source.value,
                'type': item.metadata.get('type', ''),
                'subreddit': item.metadata.get('subreddit', ''),
                'score': item.metadata.get('score', 0),
                'url': item.metadata.get('url', '')
            })
        
        df = pd.DataFrame(df_data)
        if not df.empty:
            df.set_index('date', inplace=True)
            df.sort_index(inplace=True)
        
        return df
    
    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose() 