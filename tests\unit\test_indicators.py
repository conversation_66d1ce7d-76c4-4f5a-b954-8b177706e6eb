#!/usr/bin/env python3
"""
Comprehensive test script to verify all technical indicator calculations.
Tests each indicator with known data and validates results against expected values.
"""

import pandas as pd
import numpy as np
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '..', 'src'))

from services.indicators import TechnicalIndicators
from constants.settings import SignalType
from utils.config import get_config


def create_test_data():
    """Create test data with known patterns for indicator verification."""
    # Create a simple test dataset with known patterns
    dates = pd.date_range('2024-01-01', periods=50, freq='D')
    
    # Create price data with known patterns
    base_price = 100
    prices = []
    
    # Create a trend with some volatility
    for i in range(50):
        if i < 10:
            # Initial uptrend
            price = base_price + i * 2 + np.random.normal(0, 1)
        elif i < 25:
            # Consolidation
            price = base_price + 20 + np.random.normal(0, 3)
        elif i < 35:
            # Downtrend
            price = base_price + 20 - (i - 25) * 1.5 + np.random.normal(0, 1)
        else:
            # Recovery
            price = base_price + 5 + (i - 35) * 1.2 + np.random.normal(0, 1)
        prices.append(max(price, 1))  # Ensure positive prices
    
    # Create OHLCV data
    data = pd.DataFrame({
        'Open': [p * 0.99 for p in prices],
        'High': [p * 1.02 for p in prices],
        'Low': [p * 0.98 for p in prices],
        'Close': prices,
        'Volume': [1000000 + np.random.randint(-200000, 200000) for _ in prices]
    }, index=dates)
    
    return data


def test_rsi_calculation():
    """Test RSI calculation with known data."""
    print("🔍 Testing RSI calculation...")
    
    # Create test data
    data = create_test_data()
    
    # Initialize indicators
    config = get_config()
    indicators = TechnicalIndicators(config.model_dump())
    
    # Calculate RSI
    result = indicators.calculate_rsi(data)
    
    print(f"   RSI Value: {result.value:.2f}")
    print(f"   Signal: {result.signal.value}")
    print(f"   Confidence: {result.confidence:.2f}")
    
    # Validate RSI properties
    assert result.value is not None, "RSI value should not be None"
    assert 0 <= result.value <= 100, "RSI should be between 0 and 100"
    assert result.confidence is not None, "RSI confidence should not be None"
    assert 0 <= result.confidence <= 1, "RSI confidence should be between 0 and 1"
    
    print("   ✅ RSI calculation verified")


def test_macd_calculation():
    """Test MACD calculation with known data."""
    print("🔍 Testing MACD calculation...")
    
    data = create_test_data()
    config = get_config()
    indicators = TechnicalIndicators(config.model_dump())
    
    result = indicators.calculate_macd(data)
    
    print(f"   MACD Value: {result.value:.4f}")
    print(f"   Signal: {result.signal.value}")
    print(f"   Confidence: {result.confidence:.2f}")
    print(f"   Signal Line: {result.metadata['signal_line']:.4f}")
    print(f"   Histogram: {result.metadata['histogram']:.4f}")
    
    # Validate MACD properties
    assert result.value is not None, "MACD value should not be None"
    assert result.confidence is not None, "MACD confidence should not be None"
    assert 0 <= result.confidence <= 1, "MACD confidence should be between 0 and 1"
    
    print("   ✅ MACD calculation verified")


def test_bollinger_bands_calculation():
    """Test Bollinger Bands calculation with known data."""
    print("🔍 Testing Bollinger Bands calculation...")
    
    data = create_test_data()
    config = get_config()
    indicators = TechnicalIndicators(config.model_dump())
    
    result = indicators.calculate_bollinger_bands(data)
    
    print(f"   Position: {result.value:.4f}")
    print(f"   Signal: {result.signal.value}")
    print(f"   Confidence: {result.confidence:.2f}")
    print(f"   Upper Band: {result.metadata['upper_band']:.2f}")
    print(f"   Middle Band: {result.metadata['middle_band']:.2f}")
    print(f"   Lower Band: {result.metadata['lower_band']:.2f}")
    
    # Validate Bollinger Bands properties
    assert result.value is not None, "Bollinger Bands position should not be None"
    assert 0 <= result.value <= 1, "Position should be between 0 and 1"
    assert result.confidence is not None, "Bollinger Bands confidence should not be None"
    assert 0 <= result.confidence <= 1, "Confidence should be between 0 and 1"
    assert result.metadata['upper_band'] > result.metadata['lower_band'], "Upper band should be above lower band"
    
    print("   ✅ Bollinger Bands calculation verified")


def test_stochastic_calculation():
    """Test Stochastic Oscillator calculation."""
    print("🔍 Testing Stochastic Oscillator calculation...")
    
    data = create_test_data()
    config = get_config()
    indicators = TechnicalIndicators(config.model_dump())
    
    result = indicators.calculate_stochastic(data)
    
    print(f"   Stochastic Value: {result.value:.2f}")
    print(f"   Signal: {result.signal.value}")
    print(f"   Confidence: {result.confidence:.2f}")
    
    # Validate Stochastic properties
    assert result.value is not None, "Stochastic value should not be None"
    assert 0 <= result.value <= 100, "Stochastic should be between 0 and 100"
    assert result.confidence is not None, "Stochastic confidence should not be None"
    assert 0 <= result.confidence <= 1, "Confidence should be between 0 and 1"
    
    print("   ✅ Stochastic calculation verified")


def test_sma_ema_calculation():
    """Test SMA and EMA calculations."""
    print("🔍 Testing SMA and EMA calculations...")
    
    data = create_test_data()
    config = get_config()
    indicators = TechnicalIndicators(config.model_dump())
    
    sma_result = indicators.calculate_sma(data)
    ema_result = indicators.calculate_ema(data)
    
    print(f"   SMA Value: {sma_result.value:.2f}")
    print(f"   SMA Signal: {sma_result.signal.value}")
    print(f"   EMA Value: {ema_result.value:.2f}")
    print(f"   EMA Signal: {ema_result.signal.value}")
    
    # Validate SMA/EMA properties
    assert sma_result.value is not None, "SMA value should not be None"
    assert ema_result.value is not None, "EMA value should not be None"
    assert sma_result.value > 0, "SMA should be positive"
    assert ema_result.value > 0, "EMA should be positive"
    
    print("   ✅ SMA/EMA calculations verified")


def test_atr_calculation():
    """Test Average True Range calculation."""
    print("🔍 Testing ATR calculation...")
    
    data = create_test_data()
    config = get_config()
    indicators = TechnicalIndicators(config.model_dump())
    
    result = indicators.calculate_atr(data)
    
    print(f"   ATR Value: {result.value:.4f}")
    print(f"   Signal: {result.signal.value}")
    print(f"   Confidence: {result.confidence:.2f}")
    
    # Validate ATR properties
    assert result.value is not None, "ATR value should not be None"
    assert result.value >= 0, "ATR should be non-negative"
    assert result.confidence is not None, "ATR confidence should not be None"
    assert 0 <= result.confidence <= 1, "Confidence should be between 0 and 1"
    
    print("   ✅ ATR calculation verified")


def test_volume_indicators():
    """Test volume-based indicators."""
    print("🔍 Testing volume indicators...")
    
    data = create_test_data()
    config = get_config()
    indicators = TechnicalIndicators(config.model_dump())
    
    obv_result = indicators.calculate_obv(data)
    vwap_result = indicators.calculate_vwap(data)
    mfi_result = indicators.calculate_money_flow_index(data)
    
    print(f"   OBV Value: {obv_result.value:.0f}")
    print(f"   VWAP Value: {vwap_result.value:.2f}")
    print(f"   MFI Value: {mfi_result.value:.2f}")
    
    # Validate volume indicator properties
    assert obv_result.value is not None, "OBV value should not be None"
    assert vwap_result.value is not None, "VWAP value should not be None"
    assert mfi_result.value is not None, "MFI value should not be None"
    assert 0 <= mfi_result.value <= 100, "MFI should be between 0 and 100"
    
    print("   ✅ Volume indicators verified")


def test_momentum_indicators():
    """Test momentum indicators."""
    print("🔍 Testing momentum indicators...")
    
    data = create_test_data()
    config = get_config()
    indicators = TechnicalIndicators(config.model_dump())
    
    williams_r_result = indicators.calculate_williams_r(data)
    cci_result = indicators.calculate_cci(data)
    
    print(f"   Williams %R Value: {williams_r_result.value:.2f}")
    print(f"   CCI Value: {cci_result.value:.2f}")
    
    # Validate momentum indicator properties
    assert williams_r_result.value is not None, "Williams %R value should not be None"
    assert cci_result.value is not None, "CCI value should not be None"
    assert -100 <= williams_r_result.value <= 0, "Williams %R should be between -100 and 0"
    
    print("   ✅ Momentum indicators verified")


def test_all_indicators():
    """Test all indicators together."""
    print("🔍 Testing all indicators...")
    
    data = create_test_data()
    config = get_config()
    indicators = TechnicalIndicators(config.model_dump())
    
    results = indicators.calculate_all_indicators(data)
    
    print(f"   Calculated {len(results)} indicators:")
    for name, result in results.items():
        print(f"     {name.upper()}: {result.value:.4f} ({result.signal.value}, conf: {result.confidence:.2f})")
    
    # Validate all results
    expected_indicators = [
        'rsi', 'macd', 'sma', 'ema', 'bollinger_bands', 'stochastic', 
        'williams_r', 'cci', 'atr', 'keltner_channels', 'obv', 'vwap', 'money_flow_index'
    ]
    
    for indicator in expected_indicators:
        assert indicator in results, f"Missing {indicator} indicator"
        assert results[indicator].value is not None, f"{indicator} value should not be None"
        assert results[indicator].confidence is not None, f"{indicator} confidence should not be None"
    
    print("   ✅ All indicators calculated successfully")


def test_edge_cases():
    """Test edge cases and error handling."""
    print("🔍 Testing edge cases...")
    
    config = get_config()
    indicators = TechnicalIndicators(config.model_dump())
    
    # Test with empty data
    empty_data = pd.DataFrame(columns=['Open', 'High', 'Low', 'Close', 'Volume'])
    results = indicators.calculate_all_indicators(empty_data)
    assert len(results) == 0, "Should return empty results for empty data"
    
    # Test with insufficient data
    small_data = pd.DataFrame({
        'Open': [100, 101],
        'High': [102, 103],
        'Low': [99, 100],
        'Close': [101, 102],
        'Volume': [1000000, 1000000]
    })
    results = indicators.calculate_all_indicators(small_data)
    # Some indicators might fail with insufficient data, which is expected
    
    print("   ✅ Edge cases handled properly")


def main():
    """Run all indicator verification tests."""
    print("🚀 Starting comprehensive indicator verification tests...")
    print("=" * 60)
    
    try:
        test_rsi_calculation()
        print()
        
        test_macd_calculation()
        print()
        
        test_bollinger_bands_calculation()
        print()
        
        test_stochastic_calculation()
        print()
        
        test_sma_ema_calculation()
        print()
        
        test_atr_calculation()
        print()
        
        test_volume_indicators()
        print()
        
        test_momentum_indicators()
        print()
        
        test_all_indicators()
        print()
        
        test_edge_cases()
        print()
        
        print("🎉 All indicator verification tests passed!")
        print("✅ All technical indicators are calculating correctly")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main()) 