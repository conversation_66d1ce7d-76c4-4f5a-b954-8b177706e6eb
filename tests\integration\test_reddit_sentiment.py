#!/usr/bin/env python3
"""
Integration tests for Reddit sentiment analysis functionality.

This module tests the Reddit sentiment service integration with the main system.
"""

import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent.parent.parent / "src"))

import pytest
from services.sentiment_data import RedditSentimentService, SentimentDataService
from utils.config import setup_config


class TestRedditSentiment:
    """Test class for Reddit sentiment analysis."""
    
    @pytest.fixture
    async def reddit_service(self):
        """Fixture to create Reddit sentiment service."""
        config_manager = setup_config("config.json")
        config = config_manager.get_config()
        config_dict = config.model_dump()
        
        service = RedditSentimentService(config_dict)
        yield service
        await service.close()
    
    @pytest.fixture
    async def sentiment_service(self):
        """Fixture to create main sentiment service."""
        config_manager = setup_config("config.json")
        config = config_manager.get_config()
        config_dict = config.model_dump()
        
        service = SentimentDataService(config_dict)
        yield service
        await service.close()
    
    @pytest.mark.asyncio
    async def test_reddit_sentiment_fetching(self, reddit_service):
        """Test Reddit sentiment data fetching."""
        symbol = "AAPL"
        
        # Test sentiment fetching
        sentiment_data = await reddit_service.get_reddit_sentiment(symbol, hours=24)
        
        # Assertions
        assert isinstance(sentiment_data, list)
        
        if sentiment_data:  # If data is available
            assert len(sentiment_data) > 0
            
            # Check data structure
            for item in sentiment_data:
                assert hasattr(item, 'symbol')
                assert hasattr(item, 'sentiment_score')
                assert hasattr(item, 'confidence')
                assert hasattr(item, 'timestamp')
                assert hasattr(item, 'text')
                assert hasattr(item, 'metadata')
                
                assert item.symbol == symbol
                assert -1.0 <= item.sentiment_score <= 1.0
                assert 0.0 <= item.confidence <= 1.0
                assert isinstance(item.metadata, dict)
    
    @pytest.mark.asyncio
    async def test_reddit_sentiment_caching(self, reddit_service):
        """Test Reddit sentiment data caching."""
        symbol = "TSLA"
        
        # First fetch (should cache)
        sentiment_data1 = await reddit_service.get_reddit_sentiment(symbol, hours=24)
        
        # Second fetch (should use cache)
        sentiment_data2 = await reddit_service.get_reddit_sentiment(symbol, hours=24)
        
        # Both should return the same data
        assert len(sentiment_data1) == len(sentiment_data2)
    
    @pytest.mark.asyncio
    async def test_reddit_sentiment_analysis(self, reddit_service):
        """Test Reddit sentiment analysis accuracy."""
        # Test with known positive text
        positive_text = "AAPL is bullish and going to the moon! Great earnings and strong fundamentals."
        sentiment = reddit_service._analyze_text_sentiment(positive_text)
        
        assert sentiment['score'] > 0
        assert sentiment['confidence'] > 0
        
        # Test with known negative text
        negative_text = "AAPL is bearish and going to crash! Terrible earnings and weak fundamentals."
        sentiment = reddit_service._analyze_text_sentiment(negative_text)
        
        assert sentiment['score'] < 0
        assert sentiment['confidence'] > 0
    
    @pytest.mark.asyncio
    async def test_sentiment_service_integration(self, sentiment_service):
        """Test main sentiment service integration."""
        symbol = "NVDA"
        
        # Test sentiment data fetching
        sentiment_data = await sentiment_service.get_sentiment_data(symbol, hours=24)
        
        # Assertions
        assert isinstance(sentiment_data, list)
        
        if sentiment_data:  # If data is available
            # Test aggregation
            aggregated = sentiment_service.get_aggregated_sentiment(sentiment_data)
            
            assert 'overall_sentiment' in aggregated
            assert 'confidence' in aggregated
            assert 'volume' in aggregated
            assert 'positive_ratio' in aggregated
            assert 'negative_ratio' in aggregated
            assert 'neutral_ratio' in aggregated
            
            assert -1.0 <= aggregated['overall_sentiment'] <= 1.0
            assert 0.0 <= aggregated['confidence'] <= 1.0
            assert aggregated['volume'] >= 0
            assert 0.0 <= aggregated['positive_ratio'] <= 1.0
            assert 0.0 <= aggregated['negative_ratio'] <= 1.0
            assert 0.0 <= aggregated['neutral_ratio'] <= 1.0


async def test_reddit_sentiment_manual():
    """Manual test function for Reddit sentiment analysis."""
    print("🔴 Testing Reddit Sentiment Analysis (Integration Test)")
    print("=" * 60)
    
    # Load configuration
    config_manager = setup_config("config.json")
    config = config_manager.get_config()
    config_dict = config.model_dump()
    
    # Initialize Reddit service
    reddit_service = RedditSentimentService(config_dict)
    
    # Check if Reddit API is available
    if not reddit_service.reddit:
        print("⚠️ Reddit API not configured, using JSON API fallback")
        print("   For better results, consider setting up Reddit credentials:")
        print("   1. Create a Reddit app at https://www.reddit.com/prefs/apps")
        print("   2. Add credentials to your .env file:")
        print("      REDDIT_CLIENT_ID=your_client_id")
        print("      REDDIT_CLIENT_SECRET=your_client_secret")
        print("      REDDIT_USERNAME=your_username")
        print("      REDDIT_PASSWORD=your_password")
        print("   3. Install praw: pip install praw")
        print()
    
    # Test symbols
    symbols = ["AAPL", "TSLA", "NVDA"]
    
    print(f"📊 Testing Reddit sentiment for {len(symbols)} symbols...")
    
    for symbol in symbols:
        print(f"\n🔍 Analyzing {symbol} on Reddit...")
        
        try:
            # Get Reddit sentiment data
            sentiment_data = await reddit_service.get_reddit_sentiment(symbol, hours=24)
            
            if sentiment_data:
                print(f"   ✅ Found {len(sentiment_data)} Reddit sentiment data points")
                
                # Show sample data
                posts = [item for item in sentiment_data if item.metadata.get('type') == 'post']
                comments = [item for item in sentiment_data if item.metadata.get('type') == 'comment']
                
                print(f"   📝 Posts: {len(posts)}")
                print(f"   💬 Comments: {len(comments)}")
                
                # Calculate aggregated metrics
                positive_count = sum(1 for item in sentiment_data if item.sentiment_score > 0.1)
                negative_count = sum(1 for item in sentiment_data if item.sentiment_score < -0.1)
                neutral_count = len(sentiment_data) - positive_count - negative_count
                
                overall_sentiment = sum(item.sentiment_score for item in sentiment_data) / len(sentiment_data)
                
                print(f"   📈 Aggregated Metrics:")
                print(f"      Overall Sentiment: {overall_sentiment:.3f}")
                print(f"      Positive: {positive_count} ({positive_count/len(sentiment_data)*100:.1f}%)")
                print(f"      Negative: {negative_count} ({negative_count/len(sentiment_data)*100:.1f}%)")
                print(f"      Neutral: {neutral_count} ({neutral_count/len(sentiment_data)*100:.1f}%)")
                
            else:
                print(f"   ⚠️ No Reddit sentiment data available for {symbol}")
                
        except Exception as e:
            print(f"   ❌ Error analyzing {symbol}: {e}")
    
    # Cleanup
    await reddit_service.close()
    
    print(f"\n✅ Reddit sentiment integration test completed!")


if __name__ == "__main__":
    asyncio.run(test_reddit_sentiment_manual()) 