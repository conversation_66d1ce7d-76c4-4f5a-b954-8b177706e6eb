# 🤖 AI Integration Guide

## Overview

AI-Nvestor includes comprehensive AI integration with multiple leading AI providers for enhanced trading analysis and decision-making.

## 🎯 **Supported AI Providers**

### **OpenAI GPT-4**
- **Model**: `gpt-4`, `gpt-3.5-turbo`
- **Capabilities**: Advanced reasoning, market analysis, sentiment analysis
- **Best for**: Complex market analysis, detailed reasoning
- **API**: [OpenAI Platform](https://platform.openai.com/api-keys)

### **Anthropic Claude**
- **Model**: `claude-3-sonnet-20240229`, `claude-3-haiku-20240307`
- **Capabilities**: Safety-focused analysis, risk assessment
- **Best for**: Risk management, conservative analysis
- **API**: [Anthropic Console](https://console.anthropic.com/)

### **Google AI (Gemini)**
- **Model**: `gemini-pro`, `gemini-pro-vision`
- **Capabilities**: Multimodal analysis, real-time insights
- **Best for**: Visual pattern recognition, real-time analysis
- **API**: [Google AI Studio](https://makersuite.google.com/app/apikey)

### **Cohere**
- **Model**: `command`, `command-light`
- **Capabilities**: Text analysis, sentiment detection
- **Best for**: News sentiment, market sentiment analysis
- **API**: [Cohere Dashboard](https://dashboard.cohere.ai/api-keys)

### **HuggingFace**
- **Model**: `meta-llama/Llama-2-7b-chat-hf`, custom models
- **Capabilities**: Open-source models, custom fine-tuning
- **Best for**: Custom analysis, specialized models
- **API**: [HuggingFace Tokens](https://huggingface.co/settings/tokens)

## 🔧 **Configuration**

### **1. API Keys Setup**

Add your API keys to `config.json`:

```json
{
  "api_keys": {
    "openai": "sk-your-openai-key",
    "anthropic": "sk-ant-your-anthropic-key",
    "google_ai": "your-google-ai-key",
    "cohere": "your-cohere-key",
    "huggingface": "hf-your-huggingface-token"
  }
}
```

### **2. Enable AI Providers**

Configure which providers to use:

```json
{
  "ai_analysis": {
    "enabled": true,
    "providers": {
      "openai": {
        "enabled": true,
        "model": "gpt-4",
        "max_tokens": 1000,
        "temperature": 0.3
      },
      "anthropic": {
        "enabled": true,
        "model": "claude-3-sonnet-20240229",
        "max_tokens": 1000,
        "temperature": 0.3
      },
      "google_ai": {
        "enabled": false,
        "model": "gemini-pro",
        "max_tokens": 1000,
        "temperature": 0.3
      }
    }
  }
}
```

### **3. Analysis Types**

Configure which types of AI analysis to enable:

```json
{
  "ai_analysis": {
    "analysis_types": {
      "sentiment": true,
      "pattern_recognition": true,
      "price_prediction": true,
      "risk_assessment": true,
      "portfolio_optimization": true
    }
  }
}
```

## 📊 **AI Analysis Types**

### **1. Sentiment Analysis**
- **Purpose**: Analyze news sentiment and social media sentiment
- **Input**: News headlines, social media posts
- **Output**: Sentiment score, trading recommendation
- **Use Case**: News-driven trading decisions

### **2. Pattern Recognition**
- **Purpose**: Identify technical patterns in price data
- **Input**: OHLCV data, technical indicators
- **Output**: Pattern identification, support/resistance levels
- **Use Case**: Technical analysis enhancement

### **3. Price Prediction**
- **Purpose**: Predict future price movements
- **Input**: Historical data, technical indicators
- **Output**: Price direction, confidence level
- **Use Case**: Entry/exit timing

### **4. Risk Assessment**
- **Purpose**: Evaluate position risk and portfolio risk
- **Input**: Volatility metrics, portfolio data
- **Output**: Risk score, position sizing recommendations
- **Use Case**: Risk management

### **5. Portfolio Optimization**
- **Purpose**: Optimize portfolio allocation
- **Input**: Current portfolio, available symbols
- **Output**: Rebalancing recommendations
- **Use Case**: Portfolio management

## 🚀 **Usage Examples**

### **Complete Data Flow Example**

The system implements your desired data flow: `SYMBOL -> GET DATA -> SEND DATA TO AIs -> GET RESULTS -> ANALYZE -> ADVICE`

```python
from src.services.ai_advisor import AIAdvisor
from src.services.market_data import MarketDataService
from src.utils.config import load_config

def analyze_symbol_with_ai_consensus(symbol: str):
    """Complete analysis flow with AI consensus."""
    
    # Step 1: GET DATA
    config = load_config()
    market_data_service = MarketDataService(config)
    data = market_data_service.get_stock_data(symbol, interval='1d', days=60)
    
    # Step 2: Initialize AI Advisor
    ai_advisor = AIAdvisor(config)
    
    # Step 3: SEND DATA TO AIs and GET RESULTS
    analyses = [
        ("Pattern Recognition", ai_advisor.detect_patterns, data),
        ("Price Prediction", lambda: ai_advisor.predict_price_movement(symbol, data), None),
        ("Risk Assessment", lambda: ai_advisor.assess_risk(symbol, data), None)
    ]
    
    results = []
    for analysis_name, analysis_func, analysis_data in analyses:
        if analysis_data is not None:
            result = analysis_func(analysis_data)
        else:
            result = analysis_func()
        results.append(result)
    
    # Step 4: ANALYZE and yield advice
    recommendations = [r.recommendation for r in results]
    strong_buy_count = recommendations.count("STRONG_BUY")
    strong_sell_count = recommendations.count("STRONG_SELL")
    
    # Determine final consensus
    if strong_buy_count >= 2:
        final_recommendation = "STRONG_BUY"
    elif strong_sell_count >= 2:
        final_recommendation = "STRONG_SELL"
    # ... additional consensus logic
    
    return final_recommendation

# Usage
symbol = "AAPL"
advice = analyze_symbol_with_ai_consensus(symbol)
print(f"AI Consensus for {symbol}: {advice}")
```

### **Basic AI Analysis**

```python
from src.services.ai_advisor import AIAdvisor
from src.utils.config import load_config

# Initialize AI advisor
config = load_config()
ai_advisor = AIAdvisor(config)

# Analyze sentiment
news_data = [{"title": "Apple reports strong earnings"}, ...]
sentiment_result = ai_advisor.analyze_sentiment("AAPL", news_data)

print(f"Recommendation: {sentiment_result.recommendation}")
print(f"Confidence: {sentiment_result.confidence:.1%}")
print(f"Provider: {sentiment_result.provider}")
print(f"Reasoning: {sentiment_result.reasoning}")
```

### **Pattern Detection**

```python
# Detect patterns in price data
pattern_result = ai_advisor.detect_patterns(price_data)

print(f"Pattern: {pattern_result.recommendation}")
print(f"Confidence: {pattern_result.confidence:.1%}")
print(f"Analysis: {pattern_result.reasoning}")
```

### **Price Prediction**

```python
# Predict price movement
prediction_result = ai_advisor.predict_price_movement("AAPL", historical_data)

print(f"Prediction: {prediction_result.recommendation}")
print(f"Confidence: {prediction_result.confidence:.1%}")
print(f"Analysis: {prediction_result.reasoning}")
```

### **Risk Assessment**

```python
# Assess risk
risk_result = ai_advisor.assess_risk("AAPL", price_data, portfolio_context)

print(f"Risk Level: {risk_result.recommendation}")
print(f"Confidence: {risk_result.confidence:.1%}")
print(f"Analysis: {risk_result.reasoning}")
```

### **Portfolio Optimization**

```python
# Optimize portfolio
portfolio = {"AAPL": 0.4, "MSFT": 0.3, "GOOGL": 0.3}
optimization_result = ai_advisor.optimize_portfolio(
    portfolio, 
    available_symbols, 
    market_data
)

print(f"Recommendation: {optimization_result.recommendation}")
print(f"Confidence: {optimization_result.confidence:.1%}")
print(f"Analysis: {optimization_result.reasoning}")
```

## 🔄 **Consensus Analysis**

The AI advisor uses **ensemble analysis** when multiple providers are enabled:

### **Enhanced Consensus with STRONG_BUY/SELL**

The system now supports **STRONG_BUY** and **STRONG_SELL** recommendations for higher confidence signals:

```python
# Multiple providers give different opinions
providers = ["openai", "anthropic", "google_ai"]
results = []

for provider in providers:
    result = provider.analyze(prompt, context)
    results.append(result)

# Enhanced consensus logic with STRONG_BUY/SELL
strong_buy_count = sum(1 for r in results if r.recommendation == "STRONG_BUY")
strong_sell_count = sum(1 for r in results if r.recommendation == "STRONG_SELL")
buy_count = sum(1 for r in results if r.recommendation == "BUY")
sell_count = sum(1 for r in results if r.recommendation == "SELL")
hold_count = sum(1 for r in results if r.recommendation == "HOLD")

# Calculate total bullish and bearish signals
total_bullish = strong_buy_count + buy_count
total_bearish = strong_sell_count + sell_count

# Enhanced consensus logic with priority for strong signals
if strong_buy_count >= 2:  # At least 2 AI providers agree on STRONG_BUY
    consensus = "STRONG_BUY"
elif strong_sell_count >= 2:  # At least 2 AI providers agree on STRONG_SELL
    consensus = "STRONG_SELL"
elif total_bullish > total_bearish and total_bullish > hold_count:
    consensus = "BUY"
elif total_bearish > total_bullish and total_bearish > hold_count:
    consensus = "SELL"
else:
    consensus = "HOLD"
```

### **Recommendation Levels**

| Level | Confidence | Description | Use Case |
|-------|------------|-------------|----------|
| **STRONG_BUY** | 95% | Very high confidence in bullish outcome | Significant position increase |
| **BUY** | 80% | Moderate confidence in positive outcome | Moderate position increase |
| **HOLD** | 60% | Maintain current position | Wait for clearer signals |
| **SELL** | 80% | Moderate confidence in negative outcome | Reduce position |
| **STRONG_SELL** | 95% | Very high confidence in bearish outcome | Significant position reduction |

### **Enhanced Parsing**

The system recognizes specific phrases for strong recommendations:

**STRONG_BUY Triggers:**
- "strong buy"
- "very bullish" 
- "extremely bullish"
- "highly recommend buy"

**STRONG_SELL Triggers:**
- "strong sell"
- "very bearish"
- "extremely bearish" 
- "highly recommend sell"

### **Consensus Requirements**

- **STRONG_BUY/SELL**: Requires 2+ AI providers to agree
- **BUY/SELL**: Majority of providers recommend same direction
- **HOLD**: Default when no clear consensus
- **Detailed Breakdown**: Shows individual provider recommendations

## 📈 **Performance Monitoring**

### **AI Analysis Metrics**

```python
# Track AI analysis performance
ai_metrics = {
    "total_analyses": 150,
    "accuracy": 0.72,
    "provider_performance": {
        "openai": {"accuracy": 0.75, "response_time": 1.2},
        "anthropic": {"accuracy": 0.70, "response_time": 1.5},
        "google_ai": {"accuracy": 0.68, "response_time": 0.8}
    },
    "analysis_types": {
        "sentiment": {"accuracy": 0.78},
        "pattern_recognition": {"accuracy": 0.65},
        "price_prediction": {"accuracy": 0.60},
        "risk_assessment": {"accuracy": 0.82},
        "portfolio_optimization": {"accuracy": 0.75}
    }
}
```

## 🔒 **Security & Best Practices**

### **API Key Security**

1. **Environment Variables**: Store API keys in environment variables
   ```bash
   export OPENAI_API_KEY="sk-your-key"
   export ANTHROPIC_API_KEY="sk-ant-your-key"
   ```

2. **Config Validation**: Validate API keys on startup
   ```python
   # Validate API keys
   for provider, key in api_keys.items():
       if key and not validate_api_key(provider, key):
           logger.warning(f"Invalid API key for {provider}")
   ```

3. **Rate Limiting**: Implement rate limiting per provider
   ```python
   # Rate limiting
   rate_limits = {
       "openai": {"requests_per_minute": 60},
       "anthropic": {"requests_per_minute": 50},
       "google_ai": {"requests_per_minute": 100}
   }
   ```

### **Error Handling**

```python
try:
    result = ai_advisor.analyze_sentiment(symbol, news_data)
    if result and result.confidence > 0.7:
        # Use high-confidence analysis
        execute_trade(result.recommendation)
    else:
        # Fall back to technical analysis
        use_technical_signals()
except Exception as e:
    logger.error(f"AI analysis failed: {e}")
    # Fall back to traditional methods
    use_fallback_analysis()
```

## 💰 **Cost Optimization**

### **API Cost Management**

| Provider | Cost per 1K tokens | Monthly Budget | Use Case |
|----------|-------------------|----------------|----------|
| OpenAI GPT-4 | $0.03 | $50 | Complex analysis |
| Anthropic Claude | $0.015 | $30 | Risk assessment |
| Google AI | $0.0005 | $10 | High-volume analysis |
| Cohere | $0.001 | $15 | Sentiment analysis |
| HuggingFace | Free | $0 | Custom models |

### **Optimization Strategies**

1. **Cache Results**: Cache AI analysis results for 1-5 minutes
2. **Batch Requests**: Combine multiple analyses into single requests
3. **Provider Selection**: Use cheaper providers for routine analysis
4. **Token Optimization**: Limit max_tokens based on analysis type

## 🧪 **Testing AI Integration**

### **Demo Script**

Run the AI integration demo:

```bash
python scripts/demo_ai.py
```

### **Test Individual Providers**

```python
# Test OpenAI
config.ai_analysis.providers.openai.enabled = True
config.api_keys.openai = "your-key"
ai_advisor = AIAdvisor(config.model_dump())

# Test analysis
result = ai_advisor.analyze_sentiment("AAPL", sample_news)
print(f"OpenAI Result: {result.recommendation}")
```

### **Performance Testing**

```python
# Benchmark AI providers
providers = ["openai", "anthropic", "google_ai"]
results = {}

for provider in providers:
    start_time = time.time()
    result = ai_advisor.analyze_sentiment("AAPL", test_data)
    end_time = time.time()
    
    results[provider] = {
        "response_time": end_time - start_time,
        "confidence": result.confidence,
        "recommendation": result.recommendation
    }
```

## 🔮 **Future Enhancements**

### **Planned Features**

1. **Custom Model Training**: Train models on your trading data
2. **Real-time Streaming**: Real-time AI analysis during market hours
3. **Multi-modal Analysis**: Combine text, image, and numerical data
4. **Federated Learning**: Collaborative model training across users
5. **Explainable AI**: Detailed reasoning for all AI decisions

### **Advanced Integrations**

1. **News APIs**: Real-time news sentiment analysis
2. **Social Media**: Twitter, Reddit sentiment tracking
3. **Earnings Calls**: Audio analysis of earnings calls
4. **SEC Filings**: Document analysis of regulatory filings
5. **Economic Data**: Macroeconomic factor analysis

## 📞 **Support**

### **Getting Help**

1. **Documentation**: Check this guide and other docs
2. **Demo Script**: Run `python scripts/demo_ai.py` for examples
3. **Configuration**: Validate your `config.json` setup
4. **API Keys**: Ensure all API keys are valid and enabled

### **Common Issues**

1. **"No AI providers available"**: Add API keys and enable providers
2. **"Invalid API key"**: Check API key format and permissions
3. **"Rate limit exceeded"**: Implement rate limiting or upgrade plan
4. **"Model not found"**: Check model name in provider configuration

---

**🎯 Ready to enhance your trading with AI? Add your API keys and start analyzing!** 