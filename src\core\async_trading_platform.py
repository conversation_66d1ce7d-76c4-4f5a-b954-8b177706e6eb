"""
Async version of the trading platform for improved performance.

This module provides:
- Parallel processing of multiple symbols
- Async API calls for better throughput
- Non-blocking operations
- Improved resource utilization
"""

import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime
import concurrent.futures
from dataclasses import dataclass

from main import TradingPlatform
from core.error_handler import handle_trading_error
from constants.settings import SignalType


@dataclass
class AsyncAnalysisResult:
    """Result of async analysis operation."""
    symbol: str
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    execution_time: Optional[float] = None


class AsyncTradingPlatform(TradingPlatform):
    """
    Async version of the trading platform with parallel processing.
    
    Features:
    - Parallel symbol analysis
    - Non-blocking I/O operations
    - Batch processing
    - Improved throughput
    """
    
    def __init__(self, config_path: str = "config.json", max_workers: int = 10):
        """
        Initialize async trading platform.
        
        Args:
            config_path: Path to configuration file
            max_workers: Maximum number of worker threads
        """
        super().__init__(config_path)
        self.max_workers = max_workers
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=max_workers)
        self._semaphore = asyncio.Semaphore(max_workers)
    
    async def run_analysis_async(self, symbols: Optional[List[str]] = None) -> Dict[str, AsyncAnalysisResult]:
        """
        Run parallel analysis on multiple symbols.
        
        Args:
            symbols: List of symbols to analyze
            
        Returns:
            Analysis results for all symbols
        """
        if symbols is None:
            symbols = self.config.watchlist
        
        self.logger.log_performance({
            'event': 'async_analysis_started',
            'symbols': symbols,
            'timestamp': datetime.now(),
            'parallel_workers': self.max_workers
        })
        
        # Create analysis tasks
        tasks = [
            self._analyze_symbol_async(symbol) 
            for symbol in symbols
        ]
        
        # Execute tasks in parallel
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        analysis_results = {}
        for i, result in enumerate(results):
            symbol = symbols[i]
            if isinstance(result, Exception):
                analysis_results[symbol] = AsyncAnalysisResult(
                    symbol=symbol,
                    success=False,
                    error=str(result)
                )
            else:
                analysis_results[symbol] = result
        
        # Update cache
        successful_results = {
            symbol: result.data 
            for symbol, result in analysis_results.items() 
            if result.success and result.data
        }
        self._update_analysis_cache(successful_results)
        
        self.logger.log_performance({
            'event': 'async_analysis_completed',
            'symbols_analyzed': len([r for r in analysis_results.values() if r.success]),
            'errors': len([r for r in analysis_results.values() if not r.success]),
            'timestamp': datetime.now()
        })
        
        return analysis_results
    
    async def _analyze_symbol_async(self, symbol: str) -> AsyncAnalysisResult:
        """
        Analyze a single symbol asynchronously.
        
        Args:
            symbol: Stock symbol to analyze
            
        Returns:
            Analysis result for the symbol
        """
        start_time = datetime.now()
        
        async with self._semaphore:  # Limit concurrent operations
            try:
                # Run the analysis in thread pool to avoid blocking
                loop = asyncio.get_event_loop()
                result = await loop.run_in_executor(
                    self.executor, 
                    self._analyze_single_symbol, 
                    symbol
                )
                
                execution_time = (datetime.now() - start_time).total_seconds()
                
                return AsyncAnalysisResult(
                    symbol=symbol,
                    success=True,
                    data=result,
                    execution_time=execution_time
                )
                
            except Exception as e:
                execution_time = (datetime.now() - start_time).total_seconds()
                
                self.logger.warning(f"Async analysis failed for {symbol}: {e}")
                
                return AsyncAnalysisResult(
                    symbol=symbol,
                    success=False,
                    error=str(e),
                    execution_time=execution_time
                )
    
    def _analyze_single_symbol(self, symbol: str) -> Dict[str, Any]:
        """
        Analyze a single symbol (blocking operation for thread pool).
        
        Args:
            symbol: Stock symbol to analyze
            
        Returns:
            Analysis result dictionary
        """
        try:
            # Get market data
            data = self.market_data.get_stock_data(
                symbol=symbol,
                interval=self.config.timeframes.interval,
                days=self.config.timeframes.days_of_history
            )
            
            if data is None or data.empty:
                raise ValueError(f"No data available for {symbol}")
            
            # Calculate indicators
            indicators = self.indicators.calculate_all_indicators(data)
            
            # Generate consensus signal
            signal, confidence = self.indicators.generate_consensus_signal(indicators)
            
            # AI Analysis (using existing logic)
            ai_analysis = None
            enhanced_confidence = confidence
            ai_reasoning = "Technical analysis only"
            ai_tier_used = None
            
            if self.ai_advisor and signal:
                tier_1_threshold = self.config.ai_analysis.tier_1_threshold
                if confidence > tier_1_threshold and len(self.ai_advisor.tier_1_providers) > 0:
                    try:
                        ai_analysis = self.ai_advisor.detect_patterns(data)
                        ai_tier_used = "tier_1"
                        
                        if ai_analysis and ai_analysis.confidence > 0.6:
                            ai_weight = 0.2
                            enhanced_confidence = (confidence * 0.8) + (ai_analysis.confidence * ai_weight)
                            ai_reasoning = ai_analysis.reasoning
                            
                            # Tier 2 escalation
                            tier_2_threshold = self.config.ai_analysis.tier_2_threshold
                            if (enhanced_confidence > tier_2_threshold and 
                                signal.value in ['STRONG_BUY', 'STRONG_SELL'] and 
                                len(self.ai_advisor.tier_2_providers) > 0):
                                try:
                                    tier2_analysis = self.ai_advisor.assess_risk(symbol, data)
                                    ai_tier_used = "tier_2"
                                    
                                    if tier2_analysis and tier2_analysis.confidence > 0.7:
                                        final_weight = 0.3
                                        enhanced_confidence = (enhanced_confidence * 0.7) + (tier2_analysis.confidence * final_weight)
                                        ai_reasoning = f"T1: {ai_analysis.reasoning[:50]}... | T2: {tier2_analysis.reasoning[:50]}..."
                                        
                                except Exception as e:
                                    self.logger.warning(f"Tier 2 AI analysis failed for {symbol}: {e}")
                        
                    except Exception as e:
                        self.logger.warning(f"Tier 1 AI analysis failed for {symbol}: {e}")
            
            # Build result
            result = {
                'symbol': symbol,
                'current_price': data['Close'].iloc[-1],
                'signal': signal.value,
                'confidence': enhanced_confidence,
                'technical_confidence': confidence,
                'ai_analysis': {
                    'available': ai_analysis is not None,
                    'confidence': ai_analysis.confidence if ai_analysis else None,
                    'recommendation': ai_analysis.recommendation if ai_analysis else None,
                    'reasoning': ai_reasoning,
                    'tier_used': ai_tier_used,
                    'cost_tier': 'free' if ai_tier_used == 'tier_1' else 'paid' if ai_tier_used == 'tier_2' else None
                },
                'indicators': {
                    name: {
                        'value': result.value,
                        'signal': result.signal.value,
                        'confidence': result.confidence
                    }
                    for name, result in indicators.items()
                },
                'timestamp': datetime.now(),
                'data_points': len(data)
            }
            
            # Log signal
            self.logger.log_signal(
                symbol, 
                signal.value, 
                enhanced_confidence,
                {name: result.value for name, result in indicators.items()}
            )
            
            # Send alerts if needed
            if (self.alert_system and enhanced_confidence > 0.8 and 
                signal.value in ['STRONG_BUY', 'STRONG_SELL'] and ai_tier_used == 'tier_2'):
                
                alert_strength = "VERY_STRONG" if enhanced_confidence > 0.9 else "STRONG"
                self.alert_system.send_signal_alert(
                    symbol=symbol,
                    signal_type=signal.value,
                    confidence=enhanced_confidence,
                    price=data['Close'].iloc[-1],
                    strength=alert_strength
                )
            
            return result
            
        except Exception as e:
            self.logger.log_error(e, {'symbol': symbol})
            raise
    
    async def run_batch_analysis(self, symbol_batches: List[List[str]]) -> Dict[str, AsyncAnalysisResult]:
        """
        Run analysis in batches to control resource usage.
        
        Args:
            symbol_batches: List of symbol batches to process
            
        Returns:
            Combined analysis results
        """
        all_results = {}
        
        for i, batch in enumerate(symbol_batches):
            self.logger.info(f"Processing batch {i+1}/{len(symbol_batches)} with {len(batch)} symbols")
            
            batch_results = await self.run_analysis_async(batch)
            all_results.update(batch_results)
            
            # Small delay between batches to avoid overwhelming APIs
            if i < len(symbol_batches) - 1:
                await asyncio.sleep(1)
        
        return all_results
    
    def cleanup(self) -> None:
        """Clean up async resources."""
        try:
            self.executor.shutdown(wait=True)
            super().cleanup()
        except Exception as e:
            self.logger.warning(f"Error during async cleanup: {e}")


# Utility functions for batching
def create_symbol_batches(symbols: List[str], batch_size: int = 10) -> List[List[str]]:
    """Create batches of symbols for processing."""
    return [symbols[i:i + batch_size] for i in range(0, len(symbols), batch_size)]


async def run_async_analysis(symbols: List[str], config_path: str = "config.json") -> Dict[str, AsyncAnalysisResult]:
    """
    Convenience function to run async analysis.
    
    Args:
        symbols: Symbols to analyze
        config_path: Configuration file path
        
    Returns:
        Analysis results
    """
    platform = AsyncTradingPlatform(config_path)
    try:
        return await platform.run_analysis_async(symbols)
    finally:
        platform.cleanup()