#!/usr/bin/env python3
"""
Verify indicator calculations against real market data.
Tests indicators with actual AAPL data to ensure calculations are correct.
"""

import pandas as pd
import numpy as np
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'src'))

from services.indicators import TechnicalIndicators
from services.market_data import MarketDataService
from utils.config import get_config


def verify_with_real_data():
    """Verify indicators with real AAPL data."""
    print("🔍 Verifying indicators with real AAPL data...")
    
    # Get real AAPL data
    config = get_config()
    market_data = MarketDataService(config.model_dump())
    
    try:
        # Get AAPL data
        data = market_data.get_stock_data('AAPL', force_real_time=False)
        if data is None or data.empty:
            print("❌ Could not fetch AAPL data")
            return
        
        print(f"   📊 AAPL Data: {len(data)} days")
        print(f"   📈 Current Price: ${data['Close'].iloc[-1]:.2f}")
        print(f"   📅 Date Range: {data.index[0].date()} to {data.index[-1].date()}")
        
        # Calculate indicators
        indicators = TechnicalIndicators(config.model_dump())
        results = indicators.calculate_all_indicators(data)
        
        print("\n📊 Indicator Results for AAPL:")
        print("=" * 50)
        
        for name, result in results.items():
            if result.value is not None:
                print(f"   {name.upper():<20}: {result.value:>8.4f} ({result.signal.value:>12}, conf: {result.confidence:.2f})")
            else:
                print(f"   {name.upper():<20}: {'N/A':>8} ({result.signal.value:>12}, conf: {result.confidence})")
        
        # Validate key indicators
        print("\n🔍 Validating key indicators:")
        
        # RSI validation
        rsi = results.get('rsi')
        if rsi and rsi.value is not None:
            assert 0 <= rsi.value <= 100, f"RSI should be 0-100, got {rsi.value}"
            print(f"   ✅ RSI: {rsi.value:.2f} (valid range)")
        
        # MACD validation
        macd = results.get('macd')
        if macd and macd.value is not None:
            print(f"   ✅ MACD: {macd.value:.4f} (signal line: {macd.metadata['signal_line']:.4f})")
        
        # Bollinger Bands validation
        bb = results.get('bollinger_bands')
        if bb and bb.value is not None:
            assert 0 <= bb.value <= 1, f"BB position should be 0-1, got {bb.value}"
            print(f"   ✅ Bollinger Bands: {bb.value:.4f} (position within bands)")
        
        # Stochastic validation
        stoch = results.get('stochastic')
        if stoch and stoch.value is not None:
            assert 0 <= stoch.value <= 100, f"Stochastic should be 0-100, got {stoch.value}"
            print(f"   ✅ Stochastic: {stoch.value:.2f} (valid range)")
        
        # Volume indicators validation
        obv = results.get('obv')
        vwap = results.get('vwap')
        mfi = results.get('money_flow_index')
        
        if obv and obv.value is not None:
            print(f"   ✅ OBV: {obv.value:.0f}")
        if vwap and vwap.value is not None:
            print(f"   ✅ VWAP: {vwap.value:.2f}")
        if mfi and mfi.value is not None:
            assert 0 <= mfi.value <= 100, f"MFI should be 0-100, got {mfi.value}"
            print(f"   ✅ MFI: {mfi.value:.2f} (valid range)")
        
        print("\n🎉 All indicator calculations verified with real data!")
        
        # Test signal consensus
        print("\n📊 Signal Analysis:")
        buy_signals = 0
        sell_signals = 0
        hold_signals = 0
        
        for name, result in results.items():
            if result.signal.value in ['STRONG_BUY', 'BUY']:
                buy_signals += 1
            elif result.signal.value in ['STRONG_SELL', 'SELL']:
                sell_signals += 1
            else:
                hold_signals += 1
        
        print(f"   📈 Buy Signals: {buy_signals}")
        print(f"   📉 Sell Signals: {sell_signals}")
        print(f"   ⏸️ Hold Signals: {hold_signals}")
        
        # Calculate average confidence
        confidences = [r.confidence for r in results.values() if r.confidence is not None]
        if confidences:
            avg_confidence = np.mean(confidences)
            print(f"   🎯 Average Confidence: {avg_confidence:.2f}")
        
    except Exception as e:
        print(f"❌ Error verifying with real data: {e}")
        import traceback
        traceback.print_exc()


def verify_calculation_consistency():
    """Verify that calculations are consistent across multiple runs."""
    print("\n🔍 Verifying calculation consistency...")
    
    config = get_config()
    indicators = TechnicalIndicators(config.model_dump())
    
    # Create test data
    dates = pd.date_range('2024-01-01', periods=30, freq='D')
    test_data = pd.DataFrame({
        'Open': [100 + i * 0.1 + np.random.normal(0, 0.5) for i in range(30)],
        'High': [102 + i * 0.1 + np.random.normal(0, 0.5) for i in range(30)],
        'Low': [98 + i * 0.1 + np.random.normal(0, 0.5) for i in range(30)],
        'Close': [101 + i * 0.1 + np.random.normal(0, 0.5) for i in range(30)],
        'Volume': [1000000 + np.random.randint(-100000, 100000) for _ in range(30)]
    }, index=dates)
    
    # Run calculations multiple times
    results1 = indicators.calculate_all_indicators(test_data)
    results2 = indicators.calculate_all_indicators(test_data)
    
    # Compare results
    consistent = True
    for name in results1.keys():
        if name in results2:
            val1 = results1[name].value
            val2 = results2[name].value
            
            if val1 is not None and val2 is not None:
                if abs(val1 - val2) > 1e-10:  # Allow for floating point precision
                    print(f"   ⚠️ {name}: Inconsistent values {val1} vs {val2}")
                    consistent = False
            elif val1 != val2:
                print(f"   ⚠️ {name}: Inconsistent None values")
                consistent = False
    
    if consistent:
        print("   ✅ All calculations are consistent across multiple runs")
    else:
        print("   ❌ Some calculations are inconsistent")


def main():
    """Run verification tests."""
    print("🚀 Starting indicator verification with real data...")
    print("=" * 60)
    
    try:
        verify_with_real_data()
        verify_calculation_consistency()
        
        print("\n🎉 All verification tests completed!")
        print("✅ Technical indicators are calculating correctly and consistently")
        
    except Exception as e:
        print(f"❌ Verification failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main()) 