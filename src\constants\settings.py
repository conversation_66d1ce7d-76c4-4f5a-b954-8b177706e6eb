"""
Constants and settings for AI-Nvestor.

This module provides:
- Application constants
- Default values
- Configuration keys
- System limits
"""

from enum import Enum
from typing import Dict, List


class TradingMode(Enum):
    """Trading modes."""
    PAPER = "paper"
    LIVE = "live"
    DEMO = "demo"


class SignalType(Enum):
    """Signal types."""
    BUY = "BUY"
    SELL = "SELL"
    HOLD = "HOLD"
    STRONG_BUY = "STRONG_BUY"
    STRONG_SELL = "STRONG_SELL"


class DataSource(Enum):
    """Data sources."""
    YFINANCE = "yfinance"
    ALPHA_VANTAGE = "alpha_vantage"
    POLYGON = "polygon"
    QUANDL = "quandl"


class NotificationChannel(Enum):
    """Notification channels."""
    EMAIL = "email"
    SLACK = "slack"
    DISCORD = "discord"
    WEBHOOK = "webhook"


# Default configuration values
DEFAULT_CONFIG = {
    "watchlist": ["AAPL", "GOOGL", "MSFT", "NVDA"],
    "timeframes": {
        "interval": "5m",
        "days_of_history": 30,
        "backtest_days": 365
    },
    "indicators": {
        "rsi_period": 14,
        "rsi_overbought": 70,
        "rsi_oversold": 30,
        "macd": {
            "fast_period": 12,
            "slow_period": 26,
            "signal_period": 9
        },
        "bollinger_bands": {
            "period": 20,
            "std_dev": 2
        },
        "stochastic": {
            "k_period": 14,
            "d_period": 3
        },
        "atr_period": 14
    },
    "risk_management": {
        "max_position_size": 0.02,
        "stop_loss_percentage": 0.05,
        "take_profit_percentage": 0.10,
        "max_daily_loss": 0.03,
        "max_portfolio_risk": 0.15,
        "position_sizing_method": "kelly_criterion"
    },
    "trading": {
        "paper_trading": True,
        "real_time_enabled": True,
        "auto_trading": False,
        "max_concurrent_positions": 5,
        "min_volume_threshold": 1000000
    },
    "market_hours": {
        "start": "09:30",
        "end": "16:00",
        "pre_market_start": "06:30",
        "after_hours_end": "20:00",
        "timezone": "America/New_York"
    },
    "data_sources": {
        "primary": "yfinance",
        "backup": "alpha_vantage",
        "real_time": "polygon",
        "cache_duration": 300
    },
    "logging": {
        "level": "INFO",
        "file": "logs/trading.log",
        "max_size": "10MB",
        "backup_count": 5
    },
    "notifications": {
        "email_enabled": False,
        "slack_enabled": False,
        "discord_enabled": False,
        "webhook_url": ""
    },
    "performance": {
        "benchmark": "SPY",
        "risk_free_rate": 0.02,
        "min_sharpe_ratio": 1.0,
        "max_drawdown": 0.20
    },
    "ai_analysis": {
        "enabled": True,
        "model_type": "tiered",
        "confidence_threshold": 0.6,
        "tier_1_threshold": 0.5,
        "tier_2_threshold": 0.75,
        "update_frequency": "1h"
    },
    "database": {
        "type": "sqlite",
        "path": "data/trading.db",
        "backup_frequency": "daily"
    }
}

# System limits
MAX_CONCURRENT_POSITIONS = 20
MAX_WATCHLIST_SIZE = 100
MAX_BACKTEST_DAYS = 3650  # 10 years
MIN_DATA_POINTS = 30
MAX_API_RETRIES = 3
CACHE_DURATION = 300  # 5 minutes

# File paths
LOG_DIR = "logs"
DATA_DIR = "data"
CACHE_DIR = "data/cache"
BACKTEST_DIR = "backtests"
REPORTS_DIR = "reports"

# API rate limits
RATE_LIMITS = {
    "yfinance": {"requests_per_minute": 100, "requests_per_hour": 1000},
    "alpha_vantage": {"requests_per_minute": 5, "requests_per_hour": 500},
    "polygon": {"requests_per_minute": 5, "requests_per_hour": 1000}
}

# Error messages
ERROR_MESSAGES = {
    "DATA_RETRIEVAL_FAILED": "Failed to retrieve market data",
    "INVALID_SYMBOL": "Invalid stock symbol provided",
    "NO_DATA_AVAILABLE": "No data available for the specified period",
    "CONFIG_INVALID": "Invalid configuration provided",
    "INSUFFICIENT_FUNDS": "Insufficient funds for trade",
    "POSITION_LIMIT_EXCEEDED": "Position limit exceeded",
    "RISK_LIMIT_EXCEEDED": "Risk limit exceeded",
    "SIGNAL_GENERATION_FAILED": "Failed to generate trading signal",
    "NETWORK_TIMEOUT": "Network timeout occurred",
    "API_RATE_LIMIT": "API rate limit exceeded"
}

# Success messages
SUCCESS_MESSAGES = {
    "DATA_RETRIEVED": "Market data retrieved successfully",
    "SIGNAL_GENERATED": "Trading signal generated successfully",
    "TRADE_EXECUTED": "Trade executed successfully",
    "BACKTEST_COMPLETED": "Backtest completed successfully",
    "CONFIG_LOADED": "Configuration loaded successfully"
}
