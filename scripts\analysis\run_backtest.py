#!/usr/bin/env python3
"""
Enhanced Backtest Runner for AI-Nvestor

This script runs comprehensive backtests on specified symbols using the AI-Nvestor
backtesting engine with STRONG signal filtering and AI tier 1 integration.

Usage:
    python scripts/analysis/run_backtest.py --symbols AAPL MSFT GOOGL --start-date 2023-01-01 --end-date 2023-12-31
    python scripts/analysis/run_backtest.py --symbols AAPL --start-date 2024-01-01 --end-date 2024-12-31 --capital 50000
"""

import sys
import os
import argparse
import asyncio
from datetime import datetime, timedelta
from pathlib import Path
import json
from typing import Dict, List, Optional
import pandas as pd

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '..', 'src'))

from backtesting.backtest import BacktestEngine, run_backtest, BacktestResult
from backtesting.ai_weight_backtester import AIWeightBacktester
from services.ai_advisor import AIAdvisor
from services.signals import SignalGenerator, SignalStrength, SignalType
from services.indicators import TechnicalIndicators, IndicatorResult
from utils.config import get_config
from utils.logging import get_logger


class EnhancedBacktestEngine(BacktestEngine):
    """Enhanced backtest engine that only executes on STRONG signals and integrates AI tier 1 analysis."""
    
    def __init__(self, config: Dict, initial_capital: float = 100000):
        super().__init__(config, initial_capital)
        self.ai_advisor = AIAdvisor(config)
        self.signal_generator = SignalGenerator(config)
        self.enhanced_indicators = TechnicalIndicators(config)
        
    async def _process_daily_signals_with_ai(self, data: Dict[str, pd.DataFrame], date: datetime):
        """Process trading signals for a specific date with AI tier 1 integration."""
        for symbol, symbol_data in data.items():
            if date in symbol_data.index:
                # Get data up to current date
                current_data = symbol_data.loc[:date]
                
                if len(current_data) < 50:  # Need enough data for indicators
                    continue
                
                try:
                    # Calculate technical indicators
                    indicators = self.enhanced_indicators.calculate_all_indicators(current_data)
                    
                    # Generate signal using signal generator (includes strength calculation)
                    signal = self.signal_generator.generate_signal(symbol, current_data)
                    
                    # Show all signals for debugging
                    if signal:
                        print(f"🔍 {date.strftime('%Y-%m-%d')} | {symbol} | {signal.strength.value} Signal: {signal.signal_type.value} | Confidence: {signal.confidence:.2f}")
                        
                        # Only proceed if signal is MODERATE, STRONG, or VERY_STRONG
                        if signal.strength in [SignalStrength.MODERATE, SignalStrength.STRONG, SignalStrength.VERY_STRONG]:
                            print(f"🎯 Processing {signal.strength.value} signal for {symbol}")
                            
                            # Get AI tier 1 analysis
                            ai_result = await self.ai_advisor.detect_patterns(symbol, current_data, "backtest_analysis")
                            
                            if ai_result and ai_result.recommendation:
                                print(f"🤖 AI Analysis: {ai_result.recommendation} (Confidence: {ai_result.confidence:.2f})")
                                
                                # Check if AI agrees with technical signal
                                ai_agreement = self._check_ai_agreement(signal.signal_type.value, ai_result.recommendation)
                                
                                if ai_agreement:
                                    print(f"✅ AI Agreement: Executing {signal.signal_type.value} signal")
                                    current_price = current_data.iloc[-1]['Close']
                                    self._execute_signal(symbol, date, signal.signal_type, current_price, signal.confidence, indicators)
                                else:
                                    print(f"⚠️ AI Disagreement: Skipping {signal.signal_type.value} signal")
                            else:
                                print(f"⚠️ No AI analysis available, executing technical signal: {signal.signal_type.value}")
                                current_price = current_data.iloc[-1]['Close']
                                self._execute_signal(symbol, date, signal.signal_type, current_price, signal.confidence, indicators)
                        else:
                            print(f"⏭️ Skipping {signal.strength.value} signal (too weak)")
                    else:
                        print(f"❌ No signal generated for {symbol} on {date.strftime('%Y-%m-%d')}")
                        
                except Exception as e:
                    print(f"❌ Error processing {symbol} on {date}: {e}")
                    continue
    
    def _check_ai_agreement(self, technical_signal: str, ai_recommendation: str) -> bool:
        """Check if AI recommendation agrees with technical signal."""
        # Convert AI recommendation to signal type
        ai_recommendation_lower = ai_recommendation.lower()
        
        if technical_signal in ['STRONG_BUY', 'BUY']:
            return any(word in ai_recommendation_lower for word in ['buy', 'bullish', 'positive', 'upward', 'strong buy'])
        elif technical_signal in ['STRONG_SELL', 'SELL']:
            return any(word in ai_recommendation_lower for word in ['sell', 'bearish', 'negative', 'downward', 'strong sell'])
        
        return False


async def run_enhanced_backtest(symbols: list, start_date: str, end_date: str, initial_capital: float = 100000):
    """Run an enhanced backtest using MODERATE+ signals and AI tier 1 analysis."""
    print(f"🚀 Starting enhanced backtest for {symbols}")
    print(f"📅 Period: {start_date} to {end_date}")
    print(f"💰 Initial Capital: ${initial_capital:,.2f}")
    print(f"🎯 Signal Filter: MODERATE, STRONG, and VERY_STRONG only")
    print(f"🤖 AI Integration: Tier 1 analysis required")
    print("=" * 60)
    
    # Get configuration
    config = get_config()
    config_dict = config.model_dump()
    
    # Run enhanced backtest
    try:
        engine = EnhancedBacktestEngine(config_dict, initial_capital=initial_capital)
        
        # Load historical data with extended range for signal generation
        # We need to load more data to ensure we have enough for signal generation
        import yfinance as yf
        from datetime import datetime, timedelta
        
        # Calculate extended start date (add 60 days before the actual start date)
        start_dt = datetime.strptime(start_date, "%Y-%m-%d")
        extended_start = (start_dt - timedelta(days=60)).strftime("%Y-%m-%d")
        
        print(f"📊 Loading extended data from {extended_start} to {end_date}")
        
        # Load data manually to ensure we have enough for signal generation
        data = {}
        for symbol in symbols:
            try:
                # Download data with extended range
                df = yf.download(symbol, start=extended_start, end=end_date, progress=False)
                
                if not df.empty and len(df) >= 50:
                    # Handle multi-level columns
                    if isinstance(df.columns, pd.MultiIndex):
                        df.columns = df.columns.get_level_values(0)
                    
                    # Filter to the actual date range for backtesting
                    actual_start = datetime.strptime(start_date, "%Y-%m-%d")
                    actual_end = datetime.strptime(end_date, "%Y-%m-%d")
                    
                    # Keep all data for signal generation but mark the actual backtest period
                    data[symbol] = df
                    print(f"✅ Loaded {len(df)} days of data for {symbol} (extended range for signal generation)")
                else:
                    print(f"❌ Insufficient data for {symbol}: {len(df)} days")
                    
            except Exception as e:
                print(f"❌ Error loading data for {symbol}: {e}")
                continue
        
        if not data:
            print("❌ No data loaded for any symbols")
            return None
        
        # Process each day with AI integration
        start_dt = datetime.strptime(start_date, "%Y-%m-%d")
        end_dt = datetime.strptime(end_date, "%Y-%m-%d")
        
        current_date = start_dt
        while current_date <= end_dt:
            await engine._process_daily_signals_with_ai(data, current_date)
            engine._update_positions(data, current_date)
            engine._record_equity(current_date)
            current_date += timedelta(days=1)
        
        # Calculate results
        results = engine._calculate_results()
        
        # Print results
        print_results(results)
        
        return results
        
    except Exception as e:
        print(f"❌ Error running enhanced backtest: {e}")
        return None


def run_simple_backtest(symbols: list, start_date: str, end_date: str, initial_capital: float = 100000):
    """Run a simple backtest using the basic backtesting engine."""
    print(f"🚀 Starting simple backtest for {symbols}")
    print(f"📅 Period: {start_date} to {end_date}")
    print(f"💰 Initial Capital: ${initial_capital:,.2f}")
    print("=" * 60)
    
    # Get configuration
    config = get_config()
    config_dict = config.model_dump()
    
    # Run backtest
    try:
        engine = BacktestEngine(config_dict, initial_capital=initial_capital)
        results = engine.run_backtest(symbols, start_date, end_date)
        
        # Print results
        print_results(results)
        
        return results
        
    except Exception as e:
        print(f"❌ Error running backtest: {e}")
        return None


def run_ai_backtest(symbols: list, start_date: str, end_date: str, initial_capital: float = 100000):
    """Run an AI-powered backtest using the AI weight backtester."""
    print(f"🤖 Starting AI-powered backtest for {symbols}")
    print(f"📅 Period: {start_date} to {end_date}")
    print(f"💰 Initial Capital: ${initial_capital:,.2f}")
    print("=" * 60)
    
    # Get configuration
    config = get_config()
    config_dict = config.model_dump()
    
    # Run AI backtest
    try:
        backtester = AIWeightBacktester(config_dict, initial_capital=initial_capital)
        results = backtester.run_comprehensive_backtest(symbols, start_date, end_date)
        
        # Print AI results
        print_ai_results(results)
        
        return results
        
    except Exception as e:
        print(f"❌ Error running AI backtest: {e}")
        return None


def print_results(results):
    """Print backtest results in a formatted way."""
    print("\n📊 BACKTEST RESULTS")
    print("=" * 40)
    print(f"💰 Total Return: {results.total_return:.2%}")
    print(f"📈 Annualized Return: {results.annualized_return:.2%}")
    print(f"📊 Sharpe Ratio: {results.sharpe_ratio:.2f}")
    print(f"📉 Max Drawdown: {results.max_drawdown:.2%}")
    print(f"🎯 Win Rate: {results.win_rate:.2%}")
    print(f"📊 Profit Factor: {results.profit_factor:.2f}")
    print(f"🔄 Total Trades: {results.total_trades}")
    print(f"✅ Winning Trades: {results.winning_trades}")
    print(f"❌ Losing Trades: {results.losing_trades}")
    print(f"💰 Average Win: ${results.avg_win:,.2f}")
    print(f"💸 Average Loss: ${results.avg_loss:,.2f}")
    
    if results.trades:
        print(f"\n📋 Recent Trades:")
        for trade in results.trades[-5:]:  # Show last 5 trades
            print(f"   {trade.timestamp.strftime('%Y-%m-%d')} | {trade.symbol} | {trade.trade_type.value} | ${trade.price:.2f} | ${trade.value:,.2f}")


def print_ai_results(results):
    """Print AI backtest results in a formatted way."""
    print("\n🤖 AI BACKTEST RESULTS")
    print("=" * 40)
    
    for config_name, metrics in results.items():
        print(f"\n📊 {config_name.upper()}")
        print("-" * 30)
        print(f"💰 Total Return: {metrics.total_return:.2%}")
        print(f"📈 Annualized Return: {metrics.annualized_return:.2%}")
        print(f"📊 Sharpe Ratio: {metrics.sharpe_ratio:.2f}")
        print(f"📉 Max Drawdown: {metrics.max_drawdown:.2%}")
        print(f"🎯 Win Rate: {metrics.win_rate:.2%}")
        print(f"📊 Profit Factor: {metrics.profit_factor:.2f}")
        print(f"🔄 Total Trades: {metrics.total_trades}")
        print(f"✅ Winning Trades: {metrics.winning_trades}")
        print(f"❌ Losing Trades: {metrics.losing_trades}")
        print(f"💰 Average Win: ${metrics.avg_win:,.2f}")
        print(f"💸 Average Loss: ${metrics.avg_loss:,.2f}")
        print(f"🎯 Signal Accuracy: {metrics.signal_accuracy:.2%}")
        print(f"🤖 AI Agreement Rate: {metrics.ai_agreement_rate:.2%}")


def save_results(results, symbols: list, start_date: str, end_date: str, output_dir: str = "outputs/backtests"):
    """Save backtest results to a JSON file."""
    try:
        # Create output directory
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # Generate filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        symbols_str = "_".join(symbols)
        filename = f"backtest_{symbols_str}_{start_date}_{end_date}_{timestamp}.json"
        filepath = output_path / filename
        
        # Convert results to JSON-serializable format
        if hasattr(results, '__dict__'):
            # For BacktestResult objects
            results_dict = {
                'total_return': results.total_return,
                'annualized_return': results.annualized_return,
                'sharpe_ratio': results.sharpe_ratio,
                'max_drawdown': results.max_drawdown,
                'win_rate': results.win_rate,
                'profit_factor': results.profit_factor,
                'total_trades': results.total_trades,
                'winning_trades': results.winning_trades,
                'losing_trades': results.losing_trades,
                'avg_win': results.avg_win,
                'avg_loss': results.avg_loss,
                'trades': [
                    {
                        'symbol': trade.symbol,
                        'timestamp': trade.timestamp.isoformat(),
                        'trade_type': trade.trade_type.value,
                        'quantity': trade.quantity,
                        'price': trade.price,
                        'value': trade.value,
                        'signal_strength': trade.signal_strength
                    }
                    for trade in results.trades
                ] if hasattr(results, 'trades') else []
            }
        else:
            # For AI backtest results (dict)
            results_dict = results
        
        # Save to file
        with open(filepath, 'w') as f:
            json.dump(results_dict, f, indent=2, default=str)
        
        print(f"\n💾 Results saved to: {filepath}")
        
    except Exception as e:
        print(f"⚠️ Warning: Could not save results: {e}")


async def main():
    """Main function to run backtests."""
    parser = argparse.ArgumentParser(description="AI-Nvestor Enhanced Backtest Runner")
    parser.add_argument("--symbols", nargs="+", default=["AAPL", "MSFT", "GOOGL"], 
                       help="Symbols to backtest")
    parser.add_argument("--start-date", default="2023-01-01", 
                       help="Start date (YYYY-MM-DD)")
    parser.add_argument("--end-date", default="2023-12-31", 
                       help="End date (YYYY-MM-DD)")
    parser.add_argument("--capital", type=float, default=100000, 
                       help="Initial capital")
    parser.add_argument("--ai", action="store_true", 
                       help="Run AI-powered backtest")
    parser.add_argument("--enhanced", action="store_true", 
                       help="Run enhanced backtest with STRONG signals and AI tier 1")
    parser.add_argument("--save", action="store_true", 
                       help="Save results to file")
    
    args = parser.parse_args()
    
    print("🤖 AI-Nvestor Enhanced Backtest Runner")
    print("=" * 40)
    
    # Validate dates
    try:
        start_date = datetime.strptime(args.start_date, "%Y-%m-%d")
        end_date = datetime.strptime(args.end_date, "%Y-%m-%d")
        if start_date >= end_date:
            print("❌ Error: Start date must be before end date")
            return
    except ValueError:
        print("❌ Error: Invalid date format. Use YYYY-MM-DD")
        return
    
    # Run backtest
    if args.enhanced:
        results = await run_enhanced_backtest(args.symbols, args.start_date, args.end_date, args.capital)
    elif args.ai:
        results = run_ai_backtest(args.symbols, args.start_date, args.end_date, args.capital)
    else:
        results = run_simple_backtest(args.symbols, args.start_date, args.end_date, args.capital)
    
    # Save results if requested
    if results and args.save:
        save_results(results, args.symbols, args.start_date, args.end_date)
    
    print("\n✅ Backtest completed!")


if __name__ == "__main__":
    asyncio.run(main()) 