"""
Centralized error handling for AI-Nvestor.

This module provides:
- Standardized error handling patterns
- Error context and chaining
- Recovery strategies
- Error reporting and logging
"""

from typing import Dict, Any, Optional, Callable, Type
from functools import wraps
import traceback
from datetime import datetime

from utils.logging import get_logger
from core.exceptions import TradingPlatformError


class ErrorHandler:
    """Centralized error handling manager."""
    
    def __init__(self):
        self.logger = get_logger()
        self.error_counts = {}
        self.recovery_strategies = {}
    
    def handle_error(self, 
                    error: Exception, 
                    context: Dict[str, Any], 
                    operation: str,
                    recoverable: bool = True) -> Optional[Any]:
        """
        Handle an error with proper logging and context.
        
        Args:
            error: The exception that occurred
            context: Additional context about the error
            operation: Description of the operation that failed
            recoverable: Whether the error is recoverable
            
        Returns:
            Recovery result if available, None otherwise
        """
        error_id = f"{type(error).__name__}:{operation}"
        self.error_counts[error_id] = self.error_counts.get(error_id, 0) + 1
        
        # Create comprehensive error context
        error_context = {
            'error_type': type(error).__name__,
            'error_message': str(error),
            'operation': operation,
            'timestamp': datetime.now().isoformat(),
            'count': self.error_counts[error_id],
            'recoverable': recoverable,
            'traceback': traceback.format_exc(),
            **context
        }
        
        # Log error with full context
        self.logger.log_error(error, error_context)
        
        # Attempt recovery if applicable
        if recoverable and error_id in self.recovery_strategies:
            try:
                return self.recovery_strategies[error_id](error, error_context)
            except Exception as recovery_error:
                self.logger.error(f"Recovery failed for {operation}: {recovery_error}")
        
        return None
    
    def register_recovery_strategy(self, 
                                 error_type: Type[Exception], 
                                 operation: str,
                                 strategy: Callable) -> None:
        """Register a recovery strategy for specific error types."""
        error_id = f"{error_type.__name__}:{operation}"
        self.recovery_strategies[error_id] = strategy
    
    def with_error_handling(self, 
                          operation: str, 
                          context: Dict[str, Any] = None,
                          recoverable: bool = True,
                          default_return: Any = None):
        """Decorator for automatic error handling."""
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    result = self.handle_error(
                        e, 
                        context or {}, 
                        operation, 
                        recoverable
                    )
                    return result if result is not None else default_return
            return wrapper
        return decorator
    
    def get_error_summary(self) -> Dict[str, int]:
        """Get summary of error counts."""
        return self.error_counts.copy()
    
    def reset_error_counts(self) -> None:
        """Reset error counters."""
        self.error_counts.clear()


# Global error handler instance
global_error_handler = ErrorHandler()


def handle_trading_error(operation: str, context: Dict[str, Any] = None):
    """Convenience decorator for trading operations."""
    return global_error_handler.with_error_handling(
        operation=operation,
        context=context or {},
        recoverable=True
    )


def handle_critical_error(operation: str, context: Dict[str, Any] = None):
    """Decorator for critical operations that shouldn't be recovered."""
    return global_error_handler.with_error_handling(
        operation=operation,
        context=context or {},
        recoverable=False
    )