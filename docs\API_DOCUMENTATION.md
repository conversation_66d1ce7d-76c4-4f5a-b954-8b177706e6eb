# AI-nvestor API Documentation and Reference Guide

This comprehensive guide documents all the API integrations used in the AI-nvestor project, including pricing, rate limits, key features, and important links for future reference.

## Table of Contents

- [Overview](#overview)
- [Tier 1 APIs (Free/Low-Cost)](#tier-1-apis-freelow-cost)
- [Tier 2 APIs (Paid/Premium)](#tier-2-apis-paidpremium)
- [Market Data APIs](#market-data-apis)
- [Communication APIs](#communication-apis)
- [Quick Reference Tables](#quick-reference-tables)
- [Important Links](#important-links)
- [Environment Variables](#environment-variables)

## Overview

The AI-nvestor project implements a **tiered LLM orchestrator** system that combines multiple AI providers with market data and communication services. The system prioritizes cost optimization by using free/low-cost APIs for initial analysis and premium APIs for validation only.

### Architecture
- **Tier 1 (Free)**: Google AI, Groq, Cerebras, Hugging Face, OpenRouter
- **Tier 2 (Paid)**: OpenAI, Anthropic
- **Market Data**: Alpha Vantage, Yahoo Finance
- **Communications**: <PERSON>wilio SMS

---

## Tier 1 APIs (Free/Low-Cost)

### 1. Google AI (Gemini)

**Models Available:**
- Gemini 2.5 Pro (free tier available)
- Gemini 2.5 Flash (fastest multimodal)
- Gemini 2.0 Flash (balanced performance)
- Gemini 1.5 Flash (legacy but stable)

**Pricing (Free Tier):**
- All models: Free of charge
- Rate limits: Lower for testing purposes
- Context: Up to 1M tokens (varies by model)

**Rate Limits (Free):**
- Gemini 2.5 Pro: 5 RPM, 250k TPM, 100 RPD
- Gemini 2.5 Flash: 10 RPM, 250k TPM, 250 RPD
- Gemini 2.0 Flash: 15 RPM, 1M TPM, 200 RPD

**Key Features:**
- Multimodal (text, image, video, audio)
- Long context windows (up to 2M tokens)
- Structured outputs
- Function calling
- Grounding with Google Search (free tier: 500 RPD)

**Environment Variable:** `GOOGLE_AI_API_KEY`

**Documentation:** https://ai.google.dev/gemini-api/docs

---

### 2. Groq

**Models Available:**
- Llama 4 Scout 17B
- Llama 3.3 70B
- Qwen 3 32B
- DeepSeek R1 Distill Llama 70B
- Whisper Large v3 (speech-to-text)

**Pricing:**
- Free tier with generous limits
- Pay-as-you-go for higher usage
- Extremely fast inference (1,400+ tokens/sec)

**Rate Limits:**
- Free tier: Varies by model
- Enterprise options available for higher limits

**Key Features:**
- Ultra-fast inference
- OpenAI-compatible API
- Tool use and function calling
- Structured outputs
- Low latency optimized

**Environment Variable:** `GROQ_API_KEY`

**Documentation:** https://docs.api.groq.com/

---

### 3. Cerebras

**Models Available:**
- Llama 4 Scout 17B (16E Instruct)
- Llama 3.3 70B
- Qwen3 32B
- DeepSeek R1 Distill Llama 70B
- Llama 3.1 8B

**Pricing Tiers:**
- **Exploration (Pay-as-you-go)**: Free tier available
- **Growth**: $1,500/month subscription (300+ RPM)
- **Enterprise**: Custom pricing

**Rate Limits (Free):**
- Varies by model
- Higher priority with paid tiers

**Key Features:**
- Fastest inference speeds (1,400+ tokens/sec for some models)
- Long context support (up to 131K tokens)
- Integration with Hugging Face and OpenRouter
- FP8 weights for efficiency

**Environment Variable:** `CEREBRAS_API_KEY`

**Documentation:** https://inference-docs.cerebras.ai/

---

### 4. Hugging Face (Inference Providers)

**Models Available:**
- 200+ models from leading providers
- Access to Cerebras, Groq, Together AI, etc.
- Unified API across multiple providers

**Pricing:**
- **Free Users**: $0.10/month credits
- **PRO Users**: $2.00/month credits + pay-as-you-go
- **Enterprise**: $2.00 per seat/month

**Rate Limits (Free):**
- 50 requests/day for free models (if <$10 credits purchased)
- 1,000 requests/day for free models (if >$10 credits purchased)
- 20 requests/minute for free models

**Key Features:**
- Unified access to multiple providers
- No markup on provider pricing
- Automatic failover between providers
- OpenAI-compatible endpoints
- Built-in rate limiting and error handling

**Environment Variable:** `HUGGINGFACE_API_KEY`

**Documentation:** https://huggingface.co/docs/inference-providers/

---

### 5. OpenRouter

**Models Available:**
- 100+ models from various providers
- GPT, Claude, Llama, Gemini, and more
- Free and paid model variants

**Pricing:**
- 5.5% fee on credit purchases ($0.80 minimum)
- No markup on model pricing
- Crypto payments: 5% fee
- BYOK (Bring Your Own Key): 5% fee

**Rate Limits:**
- Free models: 20 RPM, 50-1000 RPD (based on credits purchased)
- Paid models: Based on credit balance
- DDoS protection by Cloudflare

**Key Features:**
- Unified API for multiple providers
- Automatic fallover between providers
- Model routing and optimization
- Cost optimization (`:floor` variant)
- Speed optimization (`:nitro` variant)
- OpenAI SDK compatible

**Environment Variable:** `OPEN_ROUTER_API_KEY`

**Documentation:** https://openrouter.ai/docs

---

## Tier 2 APIs (Paid/Premium)

### 1. OpenAI

**Models Available:**
- GPT-4o (flagship multimodal)
- GPT-4o mini (cost-effective)
- o1 and o3-mini (reasoning models)
- DALL-E 3 (image generation)
- Whisper (speech-to-text)
- TTS (text-to-speech)

**Pricing (per 1M tokens):**
- **GPT-4o**: $5.00 input, $15.00 output
- **GPT-4o mini**: $0.150 input, $0.600 output
- **o1**: $15.00 input, $60.00 output
- **o3-mini**: $15.00 input, $60.00 output

**Rate Limits (Tier 1):**
- **GPT-4o**: 500 RPM, 800,000 TPM
- **GPT-4o mini**: 500 RPM, 10,000,000 TPM
- **o1**: 20 RPM, 2,000,000 TPM

**Key Features:**
- State-of-the-art reasoning capabilities
- Multimodal inputs (text, image, audio)
- Structured outputs with schema validation
- Function calling and tool use
- Vision and image understanding
- Audio input/output (beta)

**Environment Variable:** `OPENAI_API_KEY`

**Documentation:** https://platform.openai.com/docs

---

### 2. Anthropic (Claude)

**Models Available:**
- Claude 4 Sonnet (mid-tier, 200K context)
- Claude 4 Opus (top-tier, 200K context)
- Claude 3.5 Sonnet (legacy but capable)

**Pricing (per 1M tokens):**
- **Claude 4 Sonnet**: $3.00 input, $15.00 output
- **Claude 4 Opus**: $15.00 input, $75.00 output
- **Claude 3.5 Sonnet**: $3.00 input, $15.00 output

**Rate Limits:**
- Varies by tier and model
- Enterprise options for higher limits

**Key Features:**
- Excellent for complex reasoning
- Large context windows (200K tokens)
- Strong safety and alignment
- Code generation and analysis
- Document understanding
- Function calling

**Environment Variable:** `ANTHROPIC_API_KEY`

**Documentation:** https://docs.anthropic.com/

---

## Market Data APIs

### Alpha Vantage

**Services:**
- Real-time and historical stock data
- Technical indicators
- Forex and cryptocurrency data
- Economic indicators

**Pricing:**
- Free tier: 25 requests/day
- Premium plans: $49.99/month and up

**Rate Limits:**
- Free: 25 requests/day, 5 requests/minute
- Premium: Higher limits based on plan

**Environment Variable:** `ALPHA_VANTAGE_API_KEY`

**Documentation:** https://www.alphavantage.co/documentation/

---

### Yahoo Finance (yfinance)

**Services:**
- Free stock market data
- Historical prices
- Company fundamentals
- Market indices

**Pricing:** Free

**Rate Limits:** Unofficial, use responsibly

**Usage:** Python library, no API key required

**Documentation:** https://github.com/ranaroussi/yfinance

---

## Communication APIs

### Twilio SMS

**Services:**
- SMS messaging worldwide
- MMS support
- Phone number provisioning
- Delivery tracking

**Pricing (US):**
- SMS outbound: $0.0075 per segment
- SMS inbound: $0.0075 per segment
- Phone numbers: $1.15/month

**Pricing (International - varies by country):**
- Australia: $0.0515 outbound, $0.0075 inbound
- Nigeria: $0.3868 outbound
- South Africa: $0.1089 outbound, $0.0075 inbound

**Features:**
- Global delivery
- Delivery receipts
- Opt-out management
- Link shortening and tracking
- SMS pumping protection

**Environment Variables:**
- `TWILIO_ACCOUNT_SID`
- `TWILIO_AUTH_TOKEN`
- `TWILIO_FROM_NUMBER`
- `TWILIO_TO_NUMBER`

**Documentation:** https://www.twilio.com/docs/messaging

---

## Quick Reference Tables

### Tier 1 Provider Comparison

| Provider | Best For | Speed | Context | Free Tier | Key Strength |
|----------|----------|--------|---------|-----------|--------------|
| Google AI | Multimodal tasks | Fast | 1M-2M tokens | Generous | Google integration |
| Groq | Speed-critical apps | Ultra-fast | 128K tokens | Good | Inference speed |
| Cerebras | Production workloads | Ultra-fast | 131K tokens | Limited | Consistent performance |
| Hugging Face | Provider diversity | Varies | Varies | Small | Unified access |
| OpenRouter | Cost optimization | Varies | Varies | Limited | Provider routing |

### Tier 2 Provider Comparison

| Provider | Model | Input ($/1M) | Output ($/1M) | Context | Best For |
|----------|-------|--------------|---------------|---------|----------|
| OpenAI | GPT-4o | $5.00 | $15.00 | 128K | General intelligence |
| OpenAI | GPT-4o mini | $0.15 | $0.60 | 128K | Cost-effective |
| OpenAI | o1 | $15.00 | $60.00 | 200K | Complex reasoning |
| Anthropic | Claude 4 Sonnet | $3.00 | $15.00 | 200K | Balanced capability |
| Anthropic | Claude 4 Opus | $15.00 | $75.00 | 200K | Highest intelligence |

---

## Important Links

### Official Documentation
- **Google AI Gemini**: https://ai.google.dev/gemini-api/docs
- **Groq**: https://docs.api.groq.com/
- **Cerebras**: https://inference-docs.cerebras.ai/
- **Hugging Face**: https://huggingface.co/docs/inference-providers/
- **OpenRouter**: https://openrouter.ai/docs
- **OpenAI**: https://platform.openai.com/docs
- **Anthropic**: https://docs.anthropic.com/
- **Twilio**: https://www.twilio.com/docs

### Pricing Pages
- **Google AI**: https://ai.google.dev/pricing
- **Groq**: https://console.groq.com/settings/billing/plans
- **Cerebras**: https://www.cerebras.ai/pricing
- **Hugging Face**: https://huggingface.co/docs/inference-providers/en/pricing
- **OpenRouter**: https://openrouter.ai/docs/limits
- **OpenAI**: https://openai.com/api/pricing/
- **Twilio**: https://www.twilio.com/en-us/pricing/messaging

### Rate Limits and Status
- **Google AI Rate Limits**: https://ai.google.dev/gemini-api/docs/rate-limits
- **Groq Rate Limits**: https://inference-docs.cerebras.ai/support/rate-limits
- **OpenAI Rate Limits**: https://platform.openai.com/docs/guides/rate-limits
- **Twilio Status**: https://status.twilio.com/

### Community and Support
- **OpenAI Community**: https://community.openai.com/
- **Anthropic Support**: https://support.anthropic.com/
- **Twilio Help**: https://help.twilio.com/
- **Hugging Face Community**: https://huggingface.co/join/discord

---

## Environment Variables

### Required API Keys

```bash
# Tier 1 AI Providers
GOOGLE_AI_API_KEY=your_google_ai_api_key_here
GROQ_API_KEY=your_groq_api_key_here
HUGGINGFACE_API_KEY=your_huggingface_token_here
CEREBRAS_API_KEY=your_cerebras_api_key_here
OPEN_ROUTER_API_KEY=your_openrouter_api_key_here

# Tier 2 AI Providers
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Market Data
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key_here

# Communications
TWILIO_ACCOUNT_SID=your_twilio_account_sid_here
TWILIO_AUTH_TOKEN=your_twilio_auth_token_here
TWILIO_FROM_NUMBER=your_twilio_phone_number_here
TWILIO_TO_NUMBER=your_destination_phone_number_here
```

### API Key Acquisition

1. **Google AI**: Visit https://makersuite.google.com/app/apikey
2. **Groq**: Sign up at https://console.groq.com/
3. **Cerebras**: Get started at https://inference.cerebras.ai/
4. **Hugging Face**: Create token at https://huggingface.co/settings/tokens
5. **OpenRouter**: Register at https://openrouter.ai/
6. **OpenAI**: Visit https://platform.openai.com/api-keys
7. **Anthropic**: Apply at https://console.anthropic.com/
8. **Alpha Vantage**: Free key at https://www.alphavantage.co/support/#api-key
9. **Twilio**: Sign up at https://www.twilio.com/try-twilio

---

## Best Practices

### Cost Optimization
1. **Use Tier 1 for screening**: Leverage free APIs for initial analysis
2. **Reserve Tier 2 for validation**: Only use paid APIs for high-confidence signals
3. **Implement caching**: Store AI responses to reduce API calls
4. **Monitor usage**: Track token consumption and costs
5. **Use appropriate models**: Don't use expensive models for simple tasks

### Rate Limit Management
1. **Implement exponential backoff**: Handle rate limit errors gracefully
2. **Distribute requests**: Spread load across multiple providers
3. **Queue requests**: Use background processing for non-urgent tasks
4. **Monitor quotas**: Track remaining limits in real-time
5. **Fail gracefully**: Have fallback mechanisms when limits are hit

### Security
1. **Store keys securely**: Use environment variables, never commit to code
2. **Rotate keys regularly**: Update API keys periodically
3. **Monitor usage**: Watch for unusual activity
4. **Use least privilege**: Only grant necessary permissions
5. **Validate inputs**: Sanitize data before sending to APIs

### Performance
1. **Parallel processing**: Make concurrent requests when possible
2. **Batch operations**: Group related requests
3. **Use streaming**: For real-time applications where supported
4. **Cache responses**: Store frequently accessed data
5. **Monitor latency**: Track response times across providers

---

## Integration Testing

### Test Each Provider
```python
# Test script example
import os
from src.services.ai_advisor import AIAdvisor

def test_all_providers():
    config = {
        'api_keys': {
            'google_ai': os.getenv('GOOGLE_AI_API_KEY'),
            'groq': os.getenv('GROQ_API_KEY'),
            'huggingface': os.getenv('HUGGINGFACE_API_KEY'),
            'cerebras': os.getenv('CEREBRAS_API_KEY'),
            'openrouter': os.getenv('OPEN_ROUTER_API_KEY'),
            'openai': os.getenv('OPENAI_API_KEY'),
            'anthropic': os.getenv('ANTHROPIC_API_KEY'),
        }
    }
    
    advisor = AIAdvisor(config)
    print(f"Tier 1 providers: {len(advisor.tier_1_providers)}")
    print(f"Tier 2 providers: {len(advisor.tier_2_providers)}")
    
    # Test each provider
    for name, provider in advisor.all_providers.items():
        if provider.is_available():
            print(f"✅ {name}: Available")
        else:
            print(f"❌ {name}: Not available")

if __name__ == "__main__":
    test_all_providers()
```

---

*Last Updated: January 2025*
*This documentation is maintained as part of the AI-nvestor project.*