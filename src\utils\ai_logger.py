"""
AI Analysis Logger for AI-Nvestor.

This module provides comprehensive JSON logging for AI analysis flows:
- Input data sent to AI providers
- AI provider responses
- Technical indicators calculated
- Signal generation details
- Complete analysis pipeline tracking
- HTTP request/response details
- Endpoint and LLM information
"""

import json
import os
from datetime import datetime
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
from pathlib import Path

from utils.logging import get_logger


@dataclass
class AIAnalysisLog:
    """Complete AI analysis log entry."""
    timestamp: str
    symbol: str
    analysis_id: str
    input_data: Dict[str, Any]
    technical_indicators: Dict[str, Any]
    signal_analysis: Dict[str, Any]
    sentiment_data: Dict[str, Any]  # New: Sentiment analysis data
    adaptive_learning: Dict[str, Any]  # New: Adaptive learning results
    ai_prompts: Dict[str, str]
    ai_responses: Dict[str, Dict[str, Any]]
    http_requests: Dict[str, Dict[str, Any]]  # New: Detailed HTTP request logs
    http_responses: Dict[str, Dict[str, Any]]  # New: Detailed HTTP response logs
    consensus_result: Dict[str, Any]
    metadata: Dict[str, Any]


class AILogger:
    """Comprehensive AI analysis logger."""
    
    def __init__(self, log_dir: str = "outputs/logs"):
        # Create organized directory structure
        self.base_dir = Path("outputs")
        self.base_dir.mkdir(exist_ok=True)
        
        # Create date-based subdirectories
        today = datetime.now().strftime("%Y-%m-%d")
        self.log_dir = self.base_dir / "logs" / today
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger = get_logger()
        
    def log_analysis_session(self, analysis_log: AIAnalysisLog, command_description: str = "analysis") -> str:
        """
        Log a complete AI analysis session to JSON file.
        
        Args:
            analysis_log: Complete analysis log entry
            command_description: Description of the command that generated this analysis
            
        Returns:
            Path to the log file
        """
        timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        filename = f"ai_analysis_{analysis_log.symbol}_{command_description}_{timestamp}.json"
        filepath = self.log_dir / filename
        
        # Convert dataclass to dict for JSON serialization
        log_data = asdict(analysis_log)
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(log_data, f, indent=2, ensure_ascii=False, default=str)
            
            self.logger.info(f"📝 AI analysis logged to: {filepath}")
            return str(filepath)
            
        except Exception as e:
            self.logger.error(f"❌ Failed to log AI analysis: {e}")
            return ""
    
    def create_analysis_log(self, symbol: str, analysis_id: str) -> AIAnalysisLog:
        """
        Create a new analysis log entry.
        
        Args:
            symbol: Stock symbol being analyzed
            analysis_id: Unique analysis identifier
            
        Returns:
            New AIAnalysisLog instance
        """
        return AIAnalysisLog(
            timestamp=datetime.now().isoformat(),
            symbol=symbol,
            analysis_id=analysis_id,
            input_data={},
            technical_indicators={},
            signal_analysis={},
            sentiment_data={},
            adaptive_learning={},
            ai_prompts={},
            ai_responses={},
            http_requests={},
            http_responses={},
            consensus_result={},
            metadata={}
        )
    
    def log_input_data(self, analysis_log: AIAnalysisLog, data: Dict[str, Any]) -> None:
        """Log input data sent to AI providers."""
        analysis_log.input_data = data
    
    def log_technical_indicators(self, analysis_log: AIAnalysisLog, indicators: Dict[str, Any]) -> None:
        """Log technical indicators calculated."""
        analysis_log.technical_indicators = indicators
    
    def log_signal_analysis(self, analysis_log: AIAnalysisLog, signal: Dict[str, Any]) -> None:
        """Log signal analysis results."""
        analysis_log.signal_analysis = signal
    
    def log_sentiment_data(self, analysis_log: AIAnalysisLog, sentiment: Dict[str, Any]) -> None:
        """Log sentiment analysis data."""
        analysis_log.sentiment_data = sentiment
    
    def log_adaptive_learning(self, analysis_log: AIAnalysisLog, learning: Dict[str, Any]) -> None:
        """Log adaptive learning results."""
        if not hasattr(analysis_log, 'adaptive_learning'):
            analysis_log.adaptive_learning = {}
        analysis_log.adaptive_learning.update(learning)
    
    def log_ai_prompt(self, analysis_log: AIAnalysisLog, provider: str, prompt: str) -> None:
        """Log AI prompt sent to provider."""
        analysis_log.ai_prompts[provider] = prompt
    
    def log_ai_response(self, analysis_log: AIAnalysisLog, provider: str, response: Dict[str, Any]) -> None:
        """Log AI response from provider."""
        analysis_log.ai_responses[provider] = response
    
    def log_http_request(self, analysis_log: AIAnalysisLog, provider: str, request_data: Dict[str, Any]) -> None:
        """Log detailed HTTP request information."""
        analysis_log.http_requests[provider] = request_data
    
    def log_http_response(self, analysis_log: AIAnalysisLog, provider: str, response_data: Dict[str, Any]) -> None:
        """Log detailed HTTP response information."""
        analysis_log.http_responses[provider] = response_data
    
    def log_consensus_result(self, analysis_log: AIAnalysisLog, consensus: Dict[str, Any]) -> None:
        """Log final consensus result."""
        analysis_log.consensus_result = consensus
    
    def log_metadata(self, analysis_log: AIAnalysisLog, metadata: Dict[str, Any]) -> None:
        """Log additional metadata."""
        analysis_log.metadata.update(metadata)


# Global logger instance
ai_logger = AILogger() 