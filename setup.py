"""
AI-Nvestor Trading Platform Setup

Professional setup configuration for the AI-Nvestor algorithmic trading platform.
"""

from setuptools import setup, find_packages
from pathlib import Path

# Read the README file
this_directory = Path(__file__).parent
long_description = (this_directory / "README.md").read_text()

# Read requirements
requirements = []
with open("requirements.txt", "r") as f:
    for line in f:
        line = line.strip()
        if line and not line.startswith("#"):
            requirements.append(line)

setup(
    name="ai-nvestor",
    version="1.0.0",
    author="AI-Nvestor Team",
    author_email="<EMAIL>",
    description="Advanced algorithmic trading platform with AI-powered analysis",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/yourusername/ai-nvestor",
    project_urls={
        "Bug Reports": "https://github.com/yourusername/ai-nvestor/issues",
        "Source": "https://github.com/yourusername/ai-nvestor",
        "Documentation": "https://ai-nvestor.readthedocs.io/",
    },
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Financial and Insurance Industry",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Topic :: Office/Business :: Financial :: Investment",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-cov>=4.0.0",
            "black>=23.0.0",
            "flake8>=6.0.0",
            "mypy>=1.0.0",
            "pre-commit>=3.0.0",
        ],
        "docs": [
            "sphinx>=6.0.0",
            "sphinx-rtd-theme>=1.0.0",
            "myst-parser>=1.0.0",
        ],
        "dashboard": [
            "dash>=2.14.0",
            "dash-bootstrap-components>=1.5.0",
            "plotly>=5.17.0",
        ],
        "ml": [
            "scikit-learn>=1.3.0",
            "tensorflow>=2.13.0",
            "torch>=2.0.0",
            "xgboost>=1.7.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "ai-nvestor=main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.json", "*.yaml", "*.yml"],
    },
    keywords=[
        "trading",
        "algorithmic-trading",
        "quantitative-finance",
        "technical-analysis",
        "machine-learning",
        "ai",
        "investment",
        "finance",
        "backtesting",
        "portfolio-optimization",
    ],
    license="MIT",
    platforms=["any"],
    zip_safe=False,
)
