"""
Custom exceptions for the AI-Nvestor trading platform.

This module provides a hierarchy of exceptions for different types of errors
that can occur in algorithmic trading, from data retrieval issues to
trading execution problems.
"""

from typing import Optional, Dict, Any


class TradingPlatformError(Exception):
    """Base exception for all trading platform errors."""
    
    def __init__(self, message: str, error_code: Optional[str] = None, 
                 context: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.error_code = error_code
        self.context = context or {}
        self.timestamp = None  # Will be set by logging system


class DataError(TradingPlatformError):
    """Raised when there are issues with data retrieval or processing."""
    pass


class MarketDataError(DataError):
    """Raised when market data cannot be retrieved or is invalid."""
    pass


class ConfigurationError(TradingPlatformError):
    """Raised when there are configuration issues."""
    pass


class ValidationError(TradingPlatformError):
    """Raised when data validation fails."""
    pass


class TradingError(TradingPlatformError):
    """Base exception for trading-related errors."""
    pass


class InsufficientFundsError(TradingError):
    """Raised when there are insufficient funds for a trade."""
    pass


class PositionLimitError(TradingError):
    """Raised when position limits are exceeded."""
    pass


class RiskLimitError(TradingError):
    """Raised when risk management limits are exceeded."""
    pass


class SignalError(TradingPlatformError):
    """Raised when signal generation fails."""
    pass


class IndicatorError(TradingPlatformError):
    """Raised when technical indicator calculation fails."""
    pass


class BacktestError(TradingPlatformError):
    """Raised when backtesting operations fail."""
    pass


class NetworkError(TradingPlatformError):
    """Raised when network connectivity issues occur."""
    pass


class APIError(TradingPlatformError):
    """Raised when external API calls fail."""
    
    def __init__(self, message: str, api_name: str, status_code: Optional[int] = None,
                 response_data: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.api_name = api_name
        self.status_code = status_code
        self.response_data = response_data


class DatabaseError(TradingPlatformError):
    """Raised when database operations fail."""
    pass


class LoggingError(TradingPlatformError):
    """Raised when logging operations fail."""
    pass


class PerformanceError(TradingPlatformError):
    """Raised when performance calculations fail."""
    pass


class NotificationError(TradingPlatformError):
    """Raised when notification delivery fails."""
    pass


# Error codes for consistent error handling
ERROR_CODES = {
    'DATA_RETRIEVAL_FAILED': 'DR001',
    'INVALID_SYMBOL': 'DR002',
    'NO_DATA_AVAILABLE': 'DR003',
    'CONFIG_INVALID': 'CF001',
    'CONFIG_MISSING': 'CF002',
    'INSUFFICIENT_FUNDS': 'TR001',
    'POSITION_LIMIT_EXCEEDED': 'TR002',
    'RISK_LIMIT_EXCEEDED': 'TR003',
    'SIGNAL_GENERATION_FAILED': 'SG001',
    'INDICATOR_CALCULATION_FAILED': 'IN001',
    'BACKTEST_INVALID_PARAMS': 'BT001',
    'NETWORK_TIMEOUT': 'NT001',
    'API_RATE_LIMIT': 'AP001',
    'API_AUTHENTICATION_FAILED': 'AP002',
    'DATABASE_CONNECTION_FAILED': 'DB001',
    'LOGGING_SETUP_FAILED': 'LG001',
    'PERFORMANCE_CALCULATION_FAILED': 'PF001',
    'NOTIFICATION_DELIVERY_FAILED': 'NF001'
}


def get_error_code(error_type: str) -> str:
    """Get error code for a given error type."""
    return ERROR_CODES.get(error_type, 'UNKNOWN')
