#!/usr/bin/env python3
"""
Test script for AI weight backtesting.

This script demonstrates how to backtest our AI-powered weights using different approaches.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.getcwd(), 'src'))

from utils.config import get_config
from utils.logging import get_logger
from services.indicators import TechnicalIndicators, SignalType
from services.signals import SignalGenerator
import pandas as pd
import yfinance as yf
from datetime import datetime, timedelta
import json

def test_weight_backtesting():
    """Test the AI weight backtesting approach."""
    print("🔍 Testing AI Weight Backtesting Approaches")
    print("=" * 60)
    
    # Initialize services
    config = get_config()
    indicators = TechnicalIndicators(config.model_dump())
    signal_service = SignalGenerator(config.model_dump())
    
    # Test with AAPL for a short period
    symbol = "AAPL"
    start_date = "2024-01-01"
    end_date = "2024-03-31"
    
    print(f"📊 Testing with {symbol} from {start_date} to {end_date}")
    
    # Load historical data
    try:
        ticker = yf.Ticker(symbol)
        data = ticker.history(start=start_date, end=end_date, interval='1d')
        
        if data.empty or len(data) < 50:
            print(f"❌ Insufficient data for {symbol}: {len(data)} rows")
            return
        
        print(f"✅ Loaded {len(data)} rows of historical data")
        
    except Exception as e:
        print(f"❌ Failed to load data: {e}")
        return
    
    # Test different weight configurations
    weight_configs = [
        {
            'name': 'Current Weights',
            'trend_weight': 1.5,
            'momentum_weight': 1.0,
            'volatility_weight': 0.8,
            'conflict_reduction': 0.7,
            'momentum_cap': 0.4
        },
        {
            'name': 'Conservative Weights',
            'trend_weight': 2.0,
            'momentum_weight': 0.8,
            'volatility_weight': 0.6,
            'conflict_reduction': 0.5,
            'momentum_cap': 0.3
        },
        {
            'name': 'Aggressive Weights',
            'trend_weight': 1.2,
            'momentum_weight': 1.2,
            'volatility_weight': 1.0,
            'conflict_reduction': 0.8,
            'momentum_cap': 0.5
        }
    ]
    
    results = {}
    
    for weight_config in weight_configs:
        print(f"\n🔧 Testing {weight_config['name']}...")
        
        # Test technical-only approach
        tech_results = test_technical_only_backtest(data, indicators, weight_config, symbol)
        results[f"{weight_config['name']}_technical"] = tech_results
        
        # Test AI simulation approach
        ai_results = test_ai_simulation_backtest(data, indicators, weight_config, symbol)
        results[f"{weight_config['name']}_ai_simulation"] = ai_results
    
    # Generate summary report
    generate_summary_report(results, symbol, start_date, end_date)
    
    return results

def test_technical_only_backtest(data: pd.DataFrame, indicators: TechnicalIndicators, weight_config: dict, symbol: str) -> dict:
    """Test technical-only backtesting approach."""
    print(f"  📊 Running technical-only backtest...")
    
    trades = []
    current_capital = 100000
    positions = {}
    
    # Walk through the data day by day
    for i in range(50, len(data)):
        current_data = data.iloc[:i+1]
        current_date = current_data.index[-1]
        current_price = current_data['Close'].iloc[-1]
        
        # Calculate indicators
        indicator_results = indicators.calculate_all_indicators(current_data)
        
        # Generate signal with custom weights
        signal, confidence = generate_signal_with_weights(indicator_results, weight_config)
        
        # Execute signal
        trade_pnl = execute_signal(symbol, current_date, signal, current_price, confidence, positions, current_capital)
        
        if trade_pnl != 0:
            trades.append({
                'date': current_date,
                'signal': signal.value if signal else 'HOLD',
                'price': current_price,
                'confidence': confidence,
                'pnl': trade_pnl
            })
        
        current_capital += trade_pnl
    
    # Calculate metrics
    total_trades = len(trades)
    winning_trades = len([t for t in trades if t['pnl'] > 0])
    losing_trades = len([t for t in trades if t['pnl'] < 0])
    
    win_rate = winning_trades / total_trades if total_trades > 0 else 0.0
    total_return = (current_capital - 100000) / 100000
    
    print(f"    Total Return: {total_return:.2%}")
    print(f"    Win Rate: {win_rate:.2%}")
    print(f"    Total Trades: {total_trades}")
    
    return {
        'total_return': total_return,
        'win_rate': win_rate,
        'total_trades': total_trades,
        'winning_trades': winning_trades,
        'losing_trades': losing_trades,
        'final_capital': current_capital
    }

def test_ai_simulation_backtest(data: pd.DataFrame, indicators: TechnicalIndicators, weight_config: dict, symbol: str) -> dict:
    """Test AI simulation backtesting approach."""
    print(f"  🤖 Running AI simulation backtest...")
    
    trades = []
    current_capital = 100000
    positions = {}
    
    # Walk through the data day by day
    for i in range(50, len(data)):
        current_data = data.iloc[:i+1]
        current_date = current_data.index[-1]
        current_price = current_data['Close'].iloc[-1]
        
        # Calculate indicators
        indicator_results = indicators.calculate_all_indicators(current_data)
        
        # Simulate AI response
        ai_signal, ai_confidence = simulate_ai_response(indicator_results)
        
        # Generate technical signal
        tech_signal, tech_confidence = generate_signal_with_weights(indicator_results, weight_config)
        
        # Combine signals
        final_signal, final_confidence = combine_signals(tech_signal, tech_confidence, ai_signal, ai_confidence)
        
        # Execute signal
        trade_pnl = execute_signal(symbol, current_date, final_signal, current_price, final_confidence, positions, current_capital)
        
        if trade_pnl != 0:
            trades.append({
                'date': current_date,
                'signal': final_signal.value if final_signal else 'HOLD',
                'price': current_price,
                'confidence': final_confidence,
                'tech_signal': tech_signal.value if tech_signal else 'HOLD',
                'ai_signal': ai_signal.value if ai_signal else 'HOLD',
                'pnl': trade_pnl
            })
        
        current_capital += trade_pnl
    
    # Calculate metrics
    total_trades = len(trades)
    winning_trades = len([t for t in trades if t['pnl'] > 0])
    losing_trades = len([t for t in trades if t['pnl'] < 0])
    
    win_rate = winning_trades / total_trades if total_trades > 0 else 0.0
    total_return = (current_capital - 100000) / 100000
    
    print(f"    Total Return: {total_return:.2%}")
    print(f"    Win Rate: {win_rate:.2%}")
    print(f"    Total Trades: {total_trades}")
    
    return {
        'total_return': total_return,
        'win_rate': win_rate,
        'total_trades': total_trades,
        'winning_trades': winning_trades,
        'losing_trades': losing_trades,
        'final_capital': current_capital
    }

def generate_signal_with_weights(indicators: dict, weight_config: dict) -> tuple:
    """Generate signal with custom weights."""
    if not indicators:
        return None, None
    
    buy_signals = 0
    sell_signals = 0
    total_confidence = 0
    signal_count = 0
    
    # Track signal categories
    trend_signals = {'buy': 0, 'sell': 0}
    momentum_signals = {'buy': 0, 'sell': 0}
    volatility_signals = {'buy': 0, 'sell': 0}
    
    for indicator_name, result in indicators.items():
        if result.confidence is None:
            continue
        
        # Apply weights based on indicator type
        if indicator_name in ['sma', 'ema', 'macd', 'vwap']:
            weight = weight_config['trend_weight']
        elif indicator_name in ['rsi', 'stochastic', 'williams_r', 'cci', 'money_flow_index']:
            weight = weight_config['momentum_weight']
            # Cap momentum indicators
            if result.confidence > weight_config['momentum_cap']:
                result.confidence = weight_config['momentum_cap']
        else:
            weight = weight_config['volatility_weight']
        
        # Categorize and weight indicators
        if indicator_name in ['sma', 'ema', 'macd', 'vwap']:
            if result.signal in [SignalType.BUY, SignalType.STRONG_BUY]:
                trend_signals['buy'] += result.confidence * weight
                buy_signals += result.confidence * weight
            elif result.signal in [SignalType.SELL, SignalType.STRONG_SELL]:
                trend_signals['sell'] += result.confidence * weight
                sell_signals += result.confidence * weight
        elif indicator_name in ['rsi', 'stochastic', 'williams_r', 'cci', 'money_flow_index']:
            if result.signal in [SignalType.BUY, SignalType.STRONG_BUY]:
                momentum_signals['buy'] += result.confidence
                buy_signals += result.confidence
            elif result.signal in [SignalType.SELL, SignalType.STRONG_SELL]:
                momentum_signals['sell'] += result.confidence
                sell_signals += result.confidence
        else:
            if result.signal in [SignalType.BUY, SignalType.STRONG_BUY]:
                volatility_signals['buy'] += result.confidence * weight
                buy_signals += result.confidence * weight
            elif result.signal in [SignalType.SELL, SignalType.STRONG_SELL]:
                volatility_signals['sell'] += result.confidence * weight
                sell_signals += result.confidence * weight
        
        total_confidence += result.confidence
        signal_count += 1
    
    if signal_count == 0:
        return None, None
    
    avg_confidence = total_confidence / signal_count
    
    # Check for conflicts
    trend_bullish = trend_signals['buy'] > trend_signals['sell']
    momentum_bullish = momentum_signals['buy'] > momentum_signals['sell']
    signal_conflict = trend_bullish != momentum_bullish
    
    # Apply conflict resolution
    if signal_conflict:
        avg_confidence *= weight_config['conflict_reduction']
        # Reduce momentum influence when conflicting with trend
        momentum_signals['buy'] *= weight_config['conflict_reduction']
        momentum_signals['sell'] *= weight_config['conflict_reduction']
        # Recalculate totals
        buy_signals = trend_signals['buy'] + momentum_signals['buy'] + volatility_signals['buy']
        sell_signals = trend_signals['sell'] + momentum_signals['sell'] + volatility_signals['sell']
    
    # Determine final signal
    if buy_signals > sell_signals:
        if buy_signals > 2.5 and trend_bullish:
            signal = SignalType.STRONG_BUY
        elif buy_signals > 1.5:
            signal = SignalType.BUY
        else:
            signal = SignalType.HOLD
    elif sell_signals > buy_signals:
        if sell_signals > 2.5 and not trend_bullish:
            signal = SignalType.STRONG_SELL
        elif sell_signals > 1.5:
            signal = SignalType.SELL
        else:
            signal = SignalType.HOLD
    else:
        signal = SignalType.HOLD
    
    return signal, avg_confidence

def simulate_ai_response(indicators: dict) -> tuple:
    """Simulate AI response based on indicators."""
    buy_count = 0
    sell_count = 0
    total_confidence = 0
    
    for result in indicators.values():
        if result.confidence is None:
            continue
        
        if result.signal in [SignalType.BUY, SignalType.STRONG_BUY]:
            buy_count += 1
        elif result.signal in [SignalType.SELL, SignalType.STRONG_SELL]:
            sell_count += 1
        
        total_confidence += result.confidence
    
    if total_confidence == 0:
        return SignalType.HOLD, 0.5
    
    avg_confidence = total_confidence / len(indicators)
    
    # Simple simulation logic
    if buy_count > sell_count * 1.5:
        return SignalType.BUY, avg_confidence
    elif sell_count > buy_count * 1.5:
        return SignalType.SELL, avg_confidence
    else:
        return SignalType.HOLD, avg_confidence

def combine_signals(tech_signal: SignalType, tech_confidence: float, ai_signal: SignalType, ai_confidence: float) -> tuple:
    """Combine technical and AI signals."""
    if tech_signal is None:
        return ai_signal, ai_confidence
    
    # Combine signals (simple average for now)
    if tech_signal == ai_signal:
        # Agreement - boost confidence
        final_confidence = (tech_confidence + ai_confidence) / 2 * 1.2
    else:
        # Disagreement - reduce confidence
        final_confidence = (tech_confidence + ai_confidence) / 2 * 0.8
    
    # Determine final signal (prefer technical for now)
    return tech_signal, final_confidence

def execute_signal(symbol: str, date: datetime, signal: SignalType, price: float, confidence: float, positions: dict, current_capital: float) -> float:
    """Execute signal and return P&L."""
    if signal is None or signal == SignalType.HOLD:
        return 0.0
    
    # Simple position sizing based on confidence
    position_size = min(confidence, 0.1) * current_capital
    
    if signal in [SignalType.BUY, SignalType.STRONG_BUY]:
        if symbol not in positions:
            # Open position
            quantity = position_size / price
            positions[symbol] = {
                'quantity': quantity,
                'entry_price': price,
                'entry_date': date
            }
            return -position_size  # Cost
    elif signal in [SignalType.SELL, SignalType.STRONG_SELL]:
        if symbol in positions:
            # Close position
            position = positions[symbol]
            quantity = position['quantity']
            entry_price = position['entry_price']
            pnl = quantity * (price - entry_price)
            del positions[symbol]
            return pnl
    
    return 0.0

def generate_summary_report(results: dict, symbol: str, start_date: str, end_date: str):
    """Generate a summary report of the backtest results."""
    print(f"\n📊 Backtest Results Summary")
    print("=" * 60)
    print(f"Symbol: {symbol}")
    print(f"Period: {start_date} to {end_date}")
    print(f"Initial Capital: $100,000")
    print()
    
    print(f"{'Configuration':<25} {'Return':<10} {'Win Rate':<10} {'Trades':<8}")
    print("-" * 60)
    
    for config_name, metrics in results.items():
        return_pct = f"{metrics['total_return']:.2%}"
        win_rate_pct = f"{metrics['win_rate']:.2%}"
        trades = metrics['total_trades']
        
        print(f"{config_name:<25} {return_pct:<10} {win_rate_pct:<10} {trades:<8}")
    
    print()
    
    # Find best performing configuration
    best_config = max(results.items(), key=lambda x: x[1]['total_return'])
    print(f"🏆 Best Performing: {best_config[0]} ({best_config[1]['total_return']:.2%} return)")
    
    # Save results to file
    report_path = f"backtest_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_path, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n📄 Results saved to: {report_path}")

if __name__ == "__main__":
    test_weight_backtesting() 