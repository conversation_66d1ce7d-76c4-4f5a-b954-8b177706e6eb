# 🚀 AI-nvestor Quick Start Guide

> **Get up and running with the modern AI-nvestor trading platform in 15 minutes**

## 🎯 **What You'll Achieve**

By the end of this guide, you'll have:
- ✅ **AI-nvestor fully configured** with 7 AI providers
- ✅ **Real-time market analysis** running on your symbols
- ✅ **SMS alerts** for high-confidence trading signals
- ✅ **Parallel processing** analyzing multiple symbols simultaneously
- ✅ **Cost-optimized AI usage** with tiered routing

---

## 📋 **Prerequisites**

### **System Requirements**
- **Python 3.8+** (3.9+ recommended)
- **Internet connection** for API access
- **Terminal/Command Prompt** access

### **Account Setup (Optional but Recommended)**
- **Twilio account** for SMS alerts (free trial available)
- **API keys** for enhanced AI analysis (many are free)

---

## ⚡ **15-Minute Setup**

### **Step 1: Clone and Install (3 minutes)**

```bash
# Clone the repository
git clone https://github.com/your-username/ai-nvestor.git
cd ai-nvestor

# Install dependencies
pip install -r requirements.txt

# Verify installation
python -c "import src; print('✅ AI-nvestor installed successfully!')"
```

### **Step 2: Basic Configuration (2 minutes)**

The platform works out of the box with default settings, but let's customize it:

```bash
# Copy the configuration template
cp config.json my_config.json

# Edit your watchlist (optional)
# Open my_config.json and modify the "watchlist" section
```

**Quick watchlist update**:
```json
{
  "watchlist": ["AAPL", "MSFT", "GOOGL", "NVDA", "TSLA", "AMZN", "META"]
}
```

### **Step 3: Test Basic Functionality (2 minutes)**

```bash
# Test with a single symbol
python -c "
from src.main import TradingPlatform
platform = TradingPlatform('my_config.json')
results = platform.run_analysis(['AAPL'])
platform.print_analysis_results(results)
platform.cleanup()
print('\\n✅ Basic analysis working!')
"
```

**Expected output**:
```
================================================================================
AI-NVESTOR MARKET ANALYSIS REPORT
================================================================================
Analysis Time: 2025-01-01 12:00:00
Symbols Analyzed: 1
Market Status: 🟢 MARKET OPEN | Data Mode: 🟢 REAL-TIME
================================================================================

🟢 AAPL - $150.25
   Signal: STRONG_BUY (Confidence: 65.2%)
   📊 Technical Analysis Only
   RSI: 35.2 (BUY)
   MACD: -0.8 (SELL)
   BOLLINGER_BANDS: 0.3 (BUY)
   STOCHASTIC: 25.1 (STRONG_BUY)

================================================================================
```

### **Step 4: Enable Free AI Providers (5 minutes)**

Get FREE API keys to unlock AI-powered analysis:

#### **🆓 Google AI (Recommended - Most Generous)**
1. Visit https://aistudio.google.com/
2. Click "Get API Key" → "Create API Key"
3. Copy your key

#### **🆓 Groq (Recommended - Fastest)**
1. Visit https://console.groq.com/
2. Sign up and navigate to "API Keys"
3. Create a new API key

#### **🆓 Hugging Face (Recommended - Diverse Models)**
1. Visit https://huggingface.co/settings/tokens
2. Create a new token with "Read" permissions
3. Copy your token

#### **🆓 Other Free Providers** (Optional)
- **Cerebras**: https://inference.cerebras.ai/
- **OpenRouter**: https://openrouter.ai/ (free models available)

#### **Configure Your API Keys**
```bash
# Create .env file for secure API key storage
cat > .env << 'EOF'
# Tier 1 AI Providers (Free)
GOOGLE_AI_API_KEY=your_google_ai_key_here
GROQ_API_KEY=your_groq_key_here
HUGGINGFACE_API_KEY=your_huggingface_token_here
CEREBRAS_API_KEY=your_cerebras_key_here
OPEN_ROUTER_API_KEY=your_openrouter_key_here

# Market Data (Optional - Yahoo Finance works without keys)
ALPHA_VANTAGE_API_KEY=your_alphavantage_key_here

# SMS Alerts (Optional)
TWILIO_ACCOUNT_SID=your_twilio_sid_here
TWILIO_AUTH_TOKEN=your_twilio_token_here
TWILIO_FROM_NUMBER=your_twilio_phone_number
TWILIO_TO_NUMBER=your_destination_phone_number
EOF

# Keep your .env file secure
echo ".env" >> .gitignore
```

### **Step 5: Test AI-Enhanced Analysis (3 minutes)**

```bash
# Test with AI providers enabled
python -c "
from src.main import TradingPlatform
platform = TradingPlatform('my_config.json')
results = platform.run_analysis(['AAPL', 'MSFT'])
platform.print_analysis_results(results)
platform.cleanup()
print('\\n🤖 AI-enhanced analysis complete!')
"
```

**Expected enhanced output**:
```
🟢 AAPL - $150.25
   Signal: STRONG_BUY (Confidence: 73.4%)
   🆓 T1: BUY (75.0% confidence)
   📊 Technical: 65.2% | Enhanced: 73.4%
   RSI: 35.2 (BUY)
   MACD: -0.8 (SELL)
   BOLLINGER_BANDS: 0.3 (BUY)
   STOCHASTIC: 25.1 (STRONG_BUY)
```

---

## 🔥 **High-Performance Mode**

### **Parallel Processing Setup**

Now that basic functionality works, let's unlock the full power with parallel processing:

```bash
# Test parallel analysis (MUCH faster for multiple symbols)
python -c "
import asyncio
from src.core.async_trading_platform import run_async_analysis

async def test_parallel():
    symbols = ['AAPL', 'MSFT', 'GOOGL', 'NVDA', 'TSLA', 'AMZN']
    print(f'🔥 Analyzing {len(symbols)} symbols in parallel...')
    
    results = await run_async_analysis(symbols)
    
    successful = len([r for r in results.values() if r.success])
    print(f'✅ Successfully analyzed {successful}/{len(symbols)} symbols!')
    
    for symbol, result in results.items():
        if result.success:
            print(f'{symbol}: {result.execution_time:.2f}s')

asyncio.run(test_parallel())
"
```

### **Batch Processing for Large Watchlists**

```bash
# Test batch processing for large lists
python -c "
import asyncio
from src.core.async_trading_platform import AsyncTradingPlatform, create_symbol_batches

async def test_batch():
    # Simulate large watchlist
    symbols = ['AAPL', 'MSFT', 'GOOGL', 'NVDA', 'TSLA'] * 4  # 20 symbols
    batches = create_symbol_batches(symbols, batch_size=5)
    
    print(f'📊 Processing {len(symbols)} symbols in {len(batches)} batches...')
    
    platform = AsyncTradingPlatform(max_workers=10)
    try:
        results = await platform.run_batch_analysis(batches)
        successful = len([r for r in results.values() if r.success])
        print(f'✅ Batch processing complete: {successful}/{len(symbols)} successful')
    finally:
        platform.cleanup()

asyncio.run(test_batch())
"
```

---

## 📱 **SMS Alerts Setup** (Optional)

### **Twilio SMS Configuration**

1. **Sign up for Twilio** (free trial includes SMS credits):
   - Visit https://www.twilio.com/try-twilio
   - Complete signup and verify your phone number

2. **Get your credentials**:
   - **Account SID**: Found on your Twilio Console dashboard
   - **Auth Token**: Click "Show" next to Auth Token
   - **Phone Number**: Purchase a Twilio phone number (free with trial)

3. **Update your .env file**:
```bash
# Add to your .env file
TWILIO_ACCOUNT_SID=ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
TWILIO_AUTH_TOKEN=your_auth_token_here
TWILIO_FROM_NUMBER=+**********  # Your Twilio number
TWILIO_TO_NUMBER=+**********    # Your personal number
```

4. **Enable SMS in configuration**:
```json
{
  "notifications": {
    "sms_enabled": true
  }
}
```

5. **Test SMS alerts**:
```bash
python -c "
from src.main import TradingPlatform
from src.services.alerts import AlertSystem

# Test SMS functionality
platform = TradingPlatform('my_config.json')
if platform.alert_system:
    # This will send a test SMS if you have strong signals
    results = platform.run_analysis(['AAPL'])
    print('📱 Check your phone for SMS alerts!')
else:
    print('❌ SMS not configured - check your Twilio credentials')
platform.cleanup()
"
```

---

## 🎛️ **Advanced Configuration**

### **Customizing AI Thresholds**

Edit your configuration to fine-tune AI usage:

```json
{
  "ai_analysis": {
    "tier_1_threshold": 0.5,    // 50% confidence triggers free AI
    "tier_2_threshold": 0.75,   // 75% confidence triggers paid AI
    "confidence_threshold": 0.6 // Overall AI confidence threshold
  }
}
```

### **Watchlist Management**

```json
{
  "watchlist": [
    // Tech stocks
    "AAPL", "MSFT", "GOOGL", "NVDA", "META",
    
    // Growth stocks  
    "TSLA", "AMZN", "NFLX", "CRM", "ZOOM",
    
    // Financial
    "JPM", "BAC", "GS", "V", "MA",
    
    // Healthcare
    "JNJ", "PFE", "UNH", "ABBV", "TMO"
  ]
}
```

### **Risk Management Settings**

```json
{
  "risk_management": {
    "max_position_size": 0.02,          // 2% per position
    "stop_loss_percentage": 0.05,       // 5% stop loss
    "take_profit_percentage": 0.10,     // 10% take profit
    "max_daily_loss": 0.03,             // 3% daily limit
    "max_portfolio_risk": 0.15          // 15% total portfolio risk
  }
}
```

---

## 🐛 **Troubleshooting**

### **Common Issues**

#### **"AI providers initialized: 0 Tier 1 (free), 0 Tier 2 (paid)"**
**Solution**: Your API keys aren't being loaded properly.

```bash
# Check if .env file exists and has correct format
cat .env

# Verify environment variables are loaded
python -c "
import os
from dotenv import load_dotenv
load_dotenv()
print('Google AI Key:', len(os.getenv('GOOGLE_AI_API_KEY', '')), 'characters')
print('Groq Key:', len(os.getenv('GROQ_API_KEY', '')), 'characters')
"
```

#### **"Rate limit exceeded" errors**
**Solution**: The free tiers have limits. Wait or try different providers.

```bash
# Check rate limit status
python -c "
from src.services.ai_advisor import AIAdvisor
advisor = AIAdvisor({'api_keys': {}})
print('Available free providers:', len(advisor.tier_1_providers))
"
```

#### **"No data available" errors**
**Solution**: Market might be closed or symbol invalid.

```bash
# Test with a known good symbol during market hours
python -c "
from src.services.market_data import MarketDataService
service = MarketDataService({})
print('Market open:', service.is_market_hours())
"
```

### **Getting Help**

1. **Check the logs**: `tail -f logs/trading.log`
2. **Validate configuration**: Ensure JSON syntax is correct
3. **Test individual components**: Use the examples above
4. **Review API Documentation**: [API_DOCUMENTATION.md](../API_DOCUMENTATION.md)

---

## 🎯 **Next Steps**

### **Immediate Actions**
1. **Set up real-time monitoring**: `python -m src.main --mode monitor`
2. **Configure your watchlist** with your preferred symbols
3. **Enable SMS alerts** for important signals
4. **Explore parallel processing** for large analyses

### **Advanced Features**
1. **Backtesting**: Test strategies on historical data
2. **Custom indicators**: Add your own technical indicators
3. **Portfolio optimization**: Modern portfolio theory analysis
4. **API integration**: Connect to your broker's API

### **Learning Resources**
- **[Architecture Guide](ARCHITECTURE.md)**: Understand the system design
- **[Development Guide](DEVELOPMENT.md)**: Contribute to the project
- **[API Documentation](API_DOCUMENTATION.md)**: Complete API reference
- **[Service Documentation](../src/services/)**: Service details

---

## 🚀 **Production Checklist**

Before using for real trading:

- [ ] **Test thoroughly** with paper trading
- [ ] **Validate signals** against your trading strategy
- [ ] **Set appropriate risk limits** in configuration
- [ ] **Monitor costs** for paid AI providers
- [ ] **Backup configuration** and API keys securely
- [ ] **Set up proper logging** and monitoring
- [ ] **Test error recovery** scenarios

---

## 🎉 **Congratulations!**

You now have a fully functional, AI-powered trading analysis platform! 

### **What You've Achieved**
- ✅ **Automated market analysis** with 12+ technical indicators
- ✅ **AI-enhanced signals** using up to 7 AI providers
- ✅ **Cost-optimized** tiered AI routing
- ✅ **High-performance** parallel processing
- ✅ **Real-time alerts** via SMS/email
- ✅ **Professional architecture** with proper error handling

### **Ready for Trading**
Your AI-nvestor platform is now analyzing markets, generating signals, and alerting you to opportunities. The system will:

- 🔄 **Continuously monitor** your watchlist
- 🤖 **Apply AI analysis** to promising signals
- 📱 **Alert you instantly** to high-confidence opportunities
- 📊 **Provide detailed context** for every signal
- 💰 **Minimize costs** through intelligent API usage

**Happy trading! 📈🚀**

---

*For advanced usage, development, or troubleshooting, see our [comprehensive documentation](../README.md).*