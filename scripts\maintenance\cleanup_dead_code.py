#!/usr/bin/env python3
"""
Dead Code Cleanup Script

This script helps identify and clean up dead code, unused imports, and other issues
identified in the code quality report.

Usage:
    python scripts/maintenance/cleanup_dead_code.py --dry-run
    python scripts/maintenance/cleanup_dead_code.py --fix
"""

import os
import sys
import re
import argparse
from pathlib import Path
from typing import List, Dict, Set, Tuple
import ast
import astroid

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

class DeadCodeCleaner:
    """Clean up dead code, unused imports, and other issues."""
    
    def __init__(self, dry_run: bool = True):
        self.dry_run = dry_run
        self.issues_found = []
        self.files_processed = 0
        
    def scan_file(self, file_path: Path) -> List[Dict]:
        """Scan a single file for issues."""
        issues = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Check for unused imports
            unused_imports = self._find_unused_imports(content, file_path)
            if unused_imports:
                issues.append({
                    'type': 'unused_imports',
                    'line_numbers': unused_imports,
                    'description': f'Found {len(unused_imports)} unused imports'
                })
            
            # Check for dead code
            dead_code = self._find_dead_code(content, file_path)
            if dead_code:
                issues.append({
                    'type': 'dead_code',
                    'line_numbers': dead_code,
                    'description': f'Found {len(dead_code)} dead code sections'
                })
            
            # Check for magic numbers
            magic_numbers = self._find_magic_numbers(content, file_path)
            if magic_numbers:
                issues.append({
                    'type': 'magic_numbers',
                    'line_numbers': magic_numbers,
                    'description': f'Found {len(magic_numbers)} magic numbers'
                })
            
            # Check for large functions
            large_functions = self._find_large_functions(content, file_path)
            if large_functions:
                issues.append({
                    'type': 'large_functions',
                    'line_numbers': large_functions,
                    'description': f'Found {len(large_functions)} large functions (>50 lines)'
                })
            
            # Check for inconsistent logging
            logging_issues = self._find_logging_issues(content, file_path)
            if logging_issues:
                issues.append({
                    'type': 'logging_issues',
                    'line_numbers': logging_issues,
                    'description': f'Found {len(logging_issues)} logging inconsistencies'
                })
                
        except Exception as e:
            issues.append({
                'type': 'error',
                'line_numbers': [],
                'description': f'Error processing file: {e}'
            })
        
        return issues
    
    def _find_unused_imports(self, content: str, file_path: Path) -> List[int]:
        """Find unused imports in the file."""
        unused_imports = []
        
        try:
            # Parse the AST
            tree = ast.parse(content)
            
            # Find all imports
            imports = []
            for node in ast.walk(tree):
                if isinstance(node, (ast.Import, ast.ImportFrom)):
                    if isinstance(node, ast.Import):
                        for alias in node.names:
                            imports.append(alias.name)
                    else:
                        if node.module:
                            imports.append(node.module)
                        for alias in node.names:
                            imports.append(alias.name)
            
            # Find all name references
            names = set()
            for node in ast.walk(tree):
                if isinstance(node, ast.Name):
                    names.add(node.id)
                elif isinstance(node, ast.Attribute):
                    # Handle attribute access like 'np.array'
                    parts = []
                    current = node
                    while isinstance(current, ast.Attribute):
                        parts.insert(0, current.attr)
                        current = current.value
                    if isinstance(current, ast.Name):
                        parts.insert(0, current.id)
                        names.add('.'.join(parts))
            
            # Check which imports are unused
            for import_name in imports:
                if import_name not in names and not self._is_standard_library(import_name):
                    # This is a simplified check - in practice, you'd need more sophisticated analysis
                    unused_imports.append(0)  # Placeholder line number
                    
        except SyntaxError:
            # File has syntax errors, skip
            pass
        
        return unused_imports
    
    def _find_dead_code(self, content: str, file_path: Path) -> List[int]:
        """Find dead code sections."""
        dead_code = []
        lines = content.split('\n')
        
        for i, line in enumerate(lines, 1):
            # Check for TODO comments
            if 'TODO' in line and 'implement' in line.lower():
                dead_code.append(i)
            
            # Check for placeholder implementations
            if 'placeholder' in line.lower() and 'implementation' in line.lower():
                dead_code.append(i)
            
            # Check for empty functions that just return
            if re.match(r'^\s*def\s+\w+\(.*\):\s*$', line):
                # Look ahead for simple return statements
                for j in range(i, min(i + 5, len(lines))):
                    if 'return' in lines[j-1] and not lines[j-1].strip().startswith('#'):
                        if 'return None' in lines[j-1] or 'return []' in lines[j-1] or 'return {}' in lines[j-1]:
                            dead_code.append(i)
                            break
        
        return dead_code
    
    def _find_magic_numbers(self, content: str, file_path: Path) -> List[int]:
        """Find magic numbers in the code."""
        magic_numbers = []
        lines = content.split('\n')
        
        # Common magic number patterns
        magic_patterns = [
            r'\b100\b',  # Common limit
            r'\b60\b',   # Time-related
            r'\b30\b',   # Days
            r'\b24\b',   # Hours
            r'\b12\b',   # Months
            r'\b365\b',  # Days in year
        ]
        
        for i, line in enumerate(lines, 1):
            for pattern in magic_patterns:
                if re.search(pattern, line) and not self._is_comment_or_string(line):
                    magic_numbers.append(i)
                    break
        
        return magic_numbers
    
    def _find_large_functions(self, content: str, file_path: Path) -> List[int]:
        """Find functions that are too large."""
        large_functions = []
        lines = content.split('\n')
        
        in_function = False
        function_start = 0
        function_lines = 0
        
        for i, line in enumerate(lines, 1):
            if re.match(r'^\s*def\s+\w+\(.*\):\s*$', line):
                if in_function and function_lines > 50:
                    large_functions.append(function_start)
                
                in_function = True
                function_start = i
                function_lines = 0
            elif in_function:
                if line.strip() and not line.strip().startswith('#'):
                    function_lines += 1
                
                # Check if function ends
                if line.strip() and not line.startswith(' ') and not line.startswith('\t'):
                    if in_function and function_lines > 50:
                        large_functions.append(function_start)
                    in_function = False
                    function_lines = 0
        
        return large_functions
    
    def _find_logging_issues(self, content: str, file_path: Path) -> List[int]:
        """Find inconsistent logging patterns."""
        logging_issues = []
        lines = content.split('\n')
        
        for i, line in enumerate(lines, 1):
            # Check for print statements (should use logging)
            if 'print(' in line and not line.strip().startswith('#'):
                logging_issues.append(i)
            
            # Check for inconsistent logging patterns
            if 'logger.' in line and 'print(' in line:
                logging_issues.append(i)
        
        return logging_issues
    
    def _is_standard_library(self, module_name: str) -> bool:
        """Check if a module is part of the standard library."""
        stdlib_modules = {
            'os', 'sys', 're', 'json', 'datetime', 'pathlib', 'typing',
            'asyncio', 'concurrent', 'threading', 'multiprocessing',
            'collections', 'itertools', 'functools', 'contextlib'
        }
        return module_name in stdlib_modules
    
    def _is_comment_or_string(self, line: str) -> bool:
        """Check if a line is a comment or string."""
        stripped = line.strip()
        return stripped.startswith('#') or stripped.startswith('"""') or stripped.startswith("'''")
    
    def scan_directory(self, directory: Path) -> Dict[str, List[Dict]]:
        """Scan a directory for issues."""
        all_issues = {}
        
        for file_path in directory.rglob('*.py'):
            if 'venv' in str(file_path) or '__pycache__' in str(file_path):
                continue
                
            issues = self.scan_file(file_path)
            if issues:
                all_issues[str(file_path)] = issues
                self.files_processed += 1
        
        return all_issues
    
    def generate_report(self, issues: Dict[str, List[Dict]]) -> str:
        """Generate a report of all issues found."""
        report = []
        report.append("# 🔍 Dead Code Cleanup Report")
        report.append("")
        
        total_issues = 0
        for file_path, file_issues in issues.items():
            report.append(f"## 📁 {file_path}")
            report.append("")
            
            for issue in file_issues:
                report.append(f"### {issue['type'].replace('_', ' ').title()}")
                report.append(f"- **Lines**: {issue['line_numbers']}")
                report.append(f"- **Description**: {issue['description']}")
                report.append("")
                total_issues += 1
        
        report.append(f"## 📊 Summary")
        report.append(f"- **Files Processed**: {self.files_processed}")
        report.append(f"- **Total Issues Found**: {total_issues}")
        report.append("")
        
        return "\n".join(report)
    
    def fix_issues(self, issues: Dict[str, List[Dict]]) -> None:
        """Fix the identified issues."""
        if self.dry_run:
            print("🔍 Dry run mode - no changes will be made")
            return
        
        for file_path, file_issues in issues.items():
            print(f"🔧 Fixing issues in {file_path}")
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Apply fixes
                content = self._apply_fixes(content, file_issues)
                
                # Write back
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                    
                print(f"✅ Fixed issues in {file_path}")
                
            except Exception as e:
                print(f"❌ Error fixing {file_path}: {e}")
    
    def _apply_fixes(self, content: str, issues: List[Dict]) -> str:
        """Apply fixes to the content."""
        lines = content.split('\n')
        
        for issue in issues:
            if issue['type'] == 'unused_imports':
                # Remove unused imports (simplified)
                lines = self._remove_unused_imports(lines, issue['line_numbers'])
            elif issue['type'] == 'dead_code':
                # Comment out dead code
                lines = self._comment_dead_code(lines, issue['line_numbers'])
            elif issue['type'] == 'magic_numbers':
                # Replace magic numbers with constants (placeholder)
                lines = self._replace_magic_numbers(lines, issue['line_numbers'])
            elif issue['type'] == 'logging_issues':
                # Fix logging issues
                lines = self._fix_logging_issues(lines, issue['line_numbers'])
        
        return '\n'.join(lines)
    
    def _remove_unused_imports(self, lines: List[str], line_numbers: List[int]) -> List[str]:
        """Remove unused imports."""
        # This is a simplified implementation
        # In practice, you'd need more sophisticated analysis
        return lines
    
    def _comment_dead_code(self, lines: List[str], line_numbers: List[int]) -> List[str]:
        """Comment out dead code."""
        for line_num in line_numbers:
            if 0 < line_num <= len(lines):
                lines[line_num - 1] = f"# TODO: Remove dead code - {lines[line_num - 1]}"
        return lines
    
    def _replace_magic_numbers(self, lines: List[str], line_numbers: List[int]) -> List[str]:
        """Replace magic numbers with constants."""
        # This is a placeholder implementation
        return lines
    
    def _fix_logging_issues(self, lines: List[str], line_numbers: List[int]) -> List[str]:
        """Fix logging issues."""
        for line_num in line_numbers:
            if 0 < line_num <= len(lines):
                line = lines[line_num - 1]
                if 'print(' in line:
                    # Replace print with logger.info
                    line = re.sub(r'print\((.*)\)', r'self.logger.info(\1)', line)
                    lines[line_num - 1] = line
        return lines


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Clean up dead code and unused imports')
    parser.add_argument('--dry-run', action='store_true', default=True,
                       help='Run in dry-run mode (default)')
    parser.add_argument('--fix', action='store_true',
                       help='Apply fixes to the codebase')
    parser.add_argument('--directory', default='src',
                       help='Directory to scan (default: src)')
    parser.add_argument('--output', default='dead_code_report.md',
                       help='Output report file (default: dead_code_report.md)')
    
    args = parser.parse_args()
    
    if args.fix:
        args.dry_run = False
    
    # Initialize cleaner
    cleaner = DeadCodeCleaner(dry_run=args.dry_run)
    
    # Scan directory
    directory = Path(args.directory)
    if not directory.exists():
        print(f"❌ Directory {directory} does not exist")
        return 1
    
    print(f"🔍 Scanning {directory} for dead code and issues...")
    issues = cleaner.scan_directory(directory)
    
    # Generate report
    report = cleaner.generate_report(issues)
    
    # Write report
    with open(args.output, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"📊 Report generated: {args.output}")
    print(f"📁 Files processed: {cleaner.files_processed}")
    print(f"🚨 Issues found: {sum(len(file_issues) for file_issues in issues.values())}")
    
    # Apply fixes if requested
    if args.fix:
        print("🔧 Applying fixes...")
        cleaner.fix_issues(issues)
        print("✅ Fixes applied")
    
    return 0


if __name__ == '__main__':
    sys.exit(main())

