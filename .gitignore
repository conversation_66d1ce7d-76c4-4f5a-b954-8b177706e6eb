# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# PEP 582; used by e.g. github.com/David-OConnor/pyflow
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv/
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# =============================================================================
# AI-NVESTOR SPECIFIC IGNORES
# =============================================================================

# API Keys and Secrets (CRITICAL - Never commit these!)
.env
.env.local
.env.production
.env.development
secrets.json
api_keys.json
config.local.json
config.production.json

# Generated Data and Cache (Large files, regenerated frequently)
data/cache/
data/*.pkl
data/*.pickle
data/*.json
!data/README.md
!data/signals.json.example
!data/alerts.json.example

# Output Files (Generated during analysis)
outputs/
!outputs/README.md

# Generated Results Files (Root directory)
backtest_results_*.json
analysis_results_*.json
*_results_*.json
*_analysis_*.json

# Log Files (Generated during operation)
logs/
!logs/README.md

# Temporary and Generated Files
*.tmp
*.temp
*.bak
*.backup
*.old
*.orig

# Data Files (Large, generated)
*.csv
*.xlsx
*.xls
*.parquet
*.h5
*.hdf5

# Model Files (Large, generated)
*.pkl
*.pickle
*.joblib
*.model

# Chart and Report Files (Generated)
*.png
*.jpg
*.jpeg
*.pdf
*.html
!docs/images/

# Database Files
*.db
*.sqlite
*.sqlite3

# Cache Files
.cache/
cache/
*.cache

# Test Results and Reports
test_results/
test_reports/
coverage/
.pytest_cache/

# Documentation Builds
docs/build/
docs/_build/
docs/_static/
docs/_templates/

# IDE and Editor Files
.vscode/
.idea/
*.swp
*.swo
*~
.project
.settings/

# OS Generated Files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# Performance and Monitoring Files
*.log
*.out
*.err
*.pid

# Configuration Backups
config.backup.json
config.old.json
config.local.json

# Lock Files (Keep these for dependency management)
# *.lock
# poetry.lock
# Pipfile.lock

# Local Development Files
local_config.json
dev_config.json
debug_config.json

# Backup and Archive Files
*.zip
*.tar.gz
*.rar
*.7z

# Reorganization Files (Temporary, can be removed after cleanup)
REORGANIZATION_*.md
!REORGANIZATION_SUMMARY.md

# Testing and Development
.coverage
htmlcov/
.tox/
.nox/
.coverage.*
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Jupyter Notebook Checkpoints
.ipynb_checkpoints/

# PyCharm
.idea/

# VS Code
.vscode/

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.tmp
*.temp
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =============================================================================
# KEEP THESE FILES (Important for project)
# =============================================================================

# Keep important documentation
!README.md
!CHANGELOG.md
!docs/
!examples/
!scripts/
!src/
!tests/
!requirements.txt
!setup.py
!config.json
!env.example
!.gitignore

# Keep important data examples
!data/README.md
!data/signals.json.example
!data/alerts.json.example

# Keep important output examples
!outputs/README.md

# Keep important log examples
!logs/README.md
