"""
Base client for API interactions in the AI-Nvestor platform.
"""

import time
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime
import asyncio
import aiohttp
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from collections import deque

from .exceptions import APIError, NetworkError, get_error_code

@dataclass
class APIResponse:
    """Standardized API response wrapper."""
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    status_code: Optional[int] = None
    headers: Optional[Dict[str, str]] = None
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

class RateLimiter:
    """Efficient rate limiter for API calls using a deque."""
    
    def __init__(self, max_requests: int, time_window: int):
        self.max_requests = max_requests
        self.time_window = time_window
        # Use a deque for efficient, fixed-size storage of request timestamps
        self.requests = deque(maxlen=max_requests)
    
    def can_make_request(self) -> bool:
        """Check if a request can be made."""
        now = time.time()
        # Remove timestamps older than the time window
        while self.requests and now - self.requests[0] > self.time_window:
            self.requests.popleft()
        return len(self.requests) < self.max_requests
    
    def record_request(self):
        """Record a request."""
        self.requests.append(time.time())
    
    def wait_if_needed(self, timeout=30):
        """Wait if the rate limit is reached."""
        start_time = time.time()
        while not self.can_make_request():
            if time.time() - start_time > timeout:
                raise TimeoutError("Rate limiting timeout exceeded")
            # Calculate wait time to be more efficient
            wait_time = (self.requests[0] + self.time_window) - time.time()
            time.sleep(max(0, wait_time) + 0.01) # Sleep until the oldest request expires

class BaseAPIClient(ABC):
    """Base class for all API clients."""
    
    def __init__(self, base_url: str, api_key: Optional[str] = None, max_retries: int = 3,
                 retry_delay: float = 1.0, timeout: int = 30, rate_limit_requests: int = 100,
                 rate_limit_window: int = 60):
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.timeout = timeout
        self.rate_limiter = RateLimiter(rate_limit_requests, rate_limit_window)
        
        self.session = requests.Session()
        retry_strategy = Retry(
            total=max_retries,
            backoff_factor=retry_delay,
            status_forcelist=[429, 500, 502, 503, 504]
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("https://", adapter)
    
    def _get_headers(self) -> Dict[str, str]:
        headers = {'User-Agent': 'AI-Nvestor/1.0', 'Accept': 'application/json'}
        if self.api_key:
            headers['Authorization'] = f'Bearer {self.api_key}'
        return headers
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> APIResponse:
        self.rate_limiter.wait_if_needed()
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        headers = {**self._get_headers(), **kwargs.get('headers', {})}
        
        try:
            self.rate_limiter.record_request()
            response = self.session.request(method=method, url=url, headers=headers,
                                          timeout=self.timeout, **kwargs)
            response.raise_for_status()
            return APIResponse(success=True, data=response.json(), status_code=response.status_code)
        except requests.exceptions.HTTPError as e:
            return APIResponse(success=False, error=str(e), status_code=e.response.status_code)
        except requests.exceptions.RequestException as e:
            raise NetworkError(f"Request failed: {e}", context={'url': url})
    
    # ... other methods like get, post ...

class AsyncBaseAPIClient(ABC):
    """Async base class for API clients."""
    
    def __init__(self, base_url: str, api_key: Optional[str] = None, max_retries: int = 3,
                 retry_delay: float = 1.0, timeout: int = 30, rate_limit_requests: int = 100,
                 rate_limit_window: int = 60):
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.timeout = aiohttp.ClientTimeout(total=timeout)
        self.rate_limiter = RateLimiter(rate_limit_requests, rate_limit_window)
        self.session: Optional[aiohttp.ClientSession] = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession(timeout=self.timeout)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
            
    def _get_headers(self) -> Dict[str, str]:
        headers = {'User-Agent': 'AI-Nvestor/1.0', 'Accept': 'application/json'}
        if self.api_key:
            headers['Authorization'] = f'Bearer {self.api_key}'
        return headers

    async def _make_async_request(self, method: str, endpoint: str, **kwargs) -> APIResponse:
        if not self.session:
            raise RuntimeError("Client not initialized. Use async with ...")

        # Async wait for rate limiter
        while not self.rate_limiter.can_make_request():
            wait_time = (self.rate_limiter.requests[0] + self.rate_limiter.time_window) - time.time()
            await asyncio.sleep(max(0, wait_time) + 0.01)

        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        headers = {**self._get_headers(), **kwargs.get('headers', {})}
        
        try:
            self.rate_limiter.record_request()
            async with self.session.request(method=method, url=url, headers=headers, **kwargs) as response:
                response.raise_for_status()
                data = await response.json()
                return APIResponse(success=True, data=data, status_code=response.status)
        except aiohttp.ClientResponseError as e:
            return APIResponse(success=False, error=e.message, status_code=e.status)
        except asyncio.TimeoutError:
            raise NetworkError("Request timeout", context={'url': url})
        except aiohttp.ClientError as e:
            raise NetworkError(f"Request failed: {e}", context={'url': url})
    
    # ... other methods like get, post ...
    @abstractmethod
    def validate_response(self, response: APIResponse) -> bool:
        """Validate API response."""
        pass
