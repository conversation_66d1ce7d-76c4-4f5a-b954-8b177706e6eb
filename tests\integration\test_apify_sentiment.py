#!/usr/bin/env python3
"""
Integration tests for Apify historical Reddit sentiment analysis.

This module tests the Apify service integration for historical sentiment data.
"""

import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent.parent.parent / "src"))

import pytest
from services.apify_service import ApifyService
from utils.config import setup_config


class TestApifySentiment:
    """Test class for Apify historical sentiment analysis."""
    
    @pytest.fixture
    async def apify_service(self):
        """Fixture to create Apify service."""
        config_manager = setup_config("config.json")
        config = config_manager.get_config()
        config_dict = config.model_dump()
        
        service = ApifyService(config_dict)
        yield service
        await service.close()
    
    @pytest.mark.asyncio
    async def test_apify_service_initialization(self, apify_service):
        """Test Apify service initialization."""
        assert apify_service is not None
        assert hasattr(apify_service, 'api_token')
        assert hasattr(apify_service, 'reddit_scraper_id')
        assert hasattr(apify_service, 'base_url')
        assert apify_service.base_url == "https://api.apify.com/v2"
    
    @pytest.mark.asyncio
    async def test_apify_historical_data_fetching(self, apify_service):
        """Test Apify historical data fetching."""
        symbol = "AAPL"
        start_date = "2024-01-01"
        end_date = "2024-01-31"
        
        # Test historical data fetching
        sentiment_data = await apify_service.get_historical_reddit_data(
            symbol, start_date, end_date
        )
        
        # Assertions
        assert isinstance(sentiment_data, list)
        
        # If Apify is configured and data is available
        if apify_service.api_token and apify_service.reddit_scraper_id and sentiment_data:
            assert len(sentiment_data) > 0
            
            # Check data structure
            for item in sentiment_data:
                assert hasattr(item, 'symbol')
                assert hasattr(item, 'sentiment_score')
                assert hasattr(item, 'confidence')
                assert hasattr(item, 'timestamp')
                assert hasattr(item, 'text')
                assert hasattr(item, 'metadata')
                
                assert item.symbol == symbol
                assert -1.0 <= item.sentiment_score <= 1.0
                assert 0.0 <= item.confidence <= 1.0
                assert isinstance(item.metadata, dict)
    
    @pytest.mark.asyncio
    async def test_apify_backtesting_data(self, apify_service):
        """Test Apify backtesting data format."""
        symbol = "TSLA"
        start_date = "2024-01-01"
        end_date = "2024-01-15"
        
        # Test backtesting data fetching
        df = await apify_service.get_backtesting_data(symbol, start_date, end_date)
        
        # Assertions
        assert df is not None
        
        # If data is available
        if not df.empty:
            assert 'sentiment_score' in df.columns
            assert 'confidence' in df.columns
            assert 'text' in df.columns
            assert 'source' in df.columns
            assert 'type' in df.columns
            assert 'subreddit' in df.columns
            assert 'score' in df.columns
            assert 'url' in df.columns
    
    @pytest.mark.asyncio
    async def test_apify_sentiment_analysis(self, apify_service):
        """Test Apify sentiment analysis accuracy."""
        # Test with known positive text
        positive_text = "AAPL is bullish and going to the moon! Great earnings and strong fundamentals."
        sentiment_score, confidence = apify_service._analyze_sentiment(positive_text)
        
        assert sentiment_score > 0
        assert confidence > 0
        
        # Test with known negative text
        negative_text = "AAPL is bearish and going to crash! Terrible earnings and weak fundamentals."
        sentiment_score, confidence = apify_service._analyze_sentiment(negative_text)
        
        assert sentiment_score < 0
        assert confidence > 0
    
    @pytest.mark.asyncio
    async def test_apify_caching(self, apify_service):
        """Test Apify data caching."""
        symbol = "NVDA"
        start_date = "2024-01-01"
        end_date = "2024-01-31"
        
        # First fetch (should cache)
        sentiment_data1 = await apify_service.get_historical_reddit_data(
            symbol, start_date, end_date
        )
        
        # Second fetch (should use cache)
        sentiment_data2 = await apify_service.get_historical_reddit_data(
            symbol, start_date, end_date
        )
        
        # Both should return the same data
        assert len(sentiment_data1) == len(sentiment_data2)


async def test_apify_sentiment_manual():
    """Manual test function for Apify sentiment analysis."""
    print("🔍 Testing Apify Historical Reddit Sentiment Analysis (Integration Test)")
    print("=" * 70)
    
    # Load configuration
    config_manager = setup_config("config.json")
    config = config_manager.get_config()
    config_dict = config.model_dump()
    
    # Initialize Apify service
    apify_service = ApifyService(config_dict)
    
    # Check if Apify is configured
    if not apify_service.api_token or not apify_service.reddit_scraper_id:
        print("⚠️ Apify not configured. Please set up Apify credentials:")
        print("   1. Create an Apify account at https://apify.com")
        print("   2. Get your API token from https://console.apify.com/account/integrations")
        print("   3. Find or create a Reddit scraper (e.g., 'reddit-scraper')")
        print("   4. Add credentials to your .env file:")
        print("      APIFY_API_TOKEN=your_apify_api_token")
        print("      APIFY_REDDIT_SCRAPER_ID=your_reddit_scraper_id")
        print()
        return
    
    # Test parameters
    symbol = "AAPL"
    start_date = "2024-01-01"
    end_date = "2024-01-31"
    
    print(f"📊 Testing historical Reddit sentiment for {symbol}")
    print(f"   Date range: {start_date} to {end_date}")
    print()
    
    try:
        # Get historical sentiment data
        sentiment_data = await apify_service.get_historical_reddit_data(
            symbol, start_date, end_date
        )
        
        if sentiment_data:
            print(f"✅ Found {len(sentiment_data)} historical sentiment data points")
            
            # Show sample data
            posts = [item for item in sentiment_data if item.metadata.get('type') == 'post']
            comments = [item for item in sentiment_data if item.metadata.get('type') == 'comment']
            
            print(f"   📝 Posts: {len(posts)}")
            print(f"   💬 Comments: {len(comments)}")
            
            # Show sample posts
            if posts:
                print(f"   📰 Sample Posts:")
                for i, post in enumerate(posts[:3]):
                    subreddit = post.metadata.get('subreddit', 'unknown')
                    score = post.metadata.get('score', 0)
                    sentiment = post.sentiment_score
                    url = post.metadata.get('url', '')
                    print(f"      {i+1}. r/{subreddit} (score: {score}, sentiment: {sentiment:.3f})")
                    print(f"         \"{post.text[:80]}...\"")
                    print(f"         URL: {url}")
                    print()
            
            # Show sample comments
            if comments:
                print(f"   💬 Sample Comments:")
                for i, comment in enumerate(comments[:3]):
                    subreddit = comment.metadata.get('subreddit', 'unknown')
                    score = comment.metadata.get('score', 0)
                    sentiment = comment.sentiment_score
                    url = comment.metadata.get('url', '')
                    print(f"      {i+1}. r/{subreddit} (score: {score}, sentiment: {sentiment:.3f})")
                    print(f"         \"{comment.text[:80]}...\"")
                    print(f"         URL: {url}")
                    print()
            
            # Calculate aggregated metrics
            positive_count = sum(1 for item in sentiment_data if item.sentiment_score > 0.1)
            negative_count = sum(1 for item in sentiment_data if item.sentiment_score < -0.1)
            neutral_count = len(sentiment_data) - positive_count - negative_count
            
            overall_sentiment = sum(item.sentiment_score for item in sentiment_data) / len(sentiment_data)
            
            print(f"   📈 Aggregated Metrics:")
            print(f"      Overall Sentiment: {overall_sentiment:.3f}")
            print(f"      Positive: {positive_count} ({positive_count/len(sentiment_data)*100:.1f}%)")
            print(f"      Negative: {negative_count} ({negative_count/len(sentiment_data)*100:.1f}%)")
            print(f"      Neutral: {neutral_count} ({neutral_count/len(sentiment_data)*100:.1f}%)")
            
            # Test backtesting data format
            print(f"\n📊 Testing backtesting data format...")
            df = await apify_service.get_backtesting_data(symbol, start_date, end_date)
            
            if not df.empty:
                print(f"✅ Retrieved {len(df)} rows of backtesting data")
                print(f"   Date range: {df.index.min()} to {df.index.max()}")
                print(f"   Columns: {list(df.columns)}")
                
                # Show statistics
                print(f"\n   📊 Statistics:")
                print(f"      Average Sentiment: {df['sentiment_score'].mean():.3f}")
                print(f"      Sentiment Std Dev: {df['sentiment_score'].std():.3f}")
                print(f"      Average Confidence: {df['confidence'].mean():.3f}")
                print(f"      Total Posts: {len(df[df['type'] == 'post'])}")
                print(f"      Total Comments: {len(df[df['type'] == 'comment'])}")
            else:
                print(f"   ⚠️ No backtesting data available")
            
        else:
            print(f"   ⚠️ No historical sentiment data available for {symbol}")
            
    except Exception as e:
        print(f"   ❌ Error analyzing {symbol}: {e}")
        import traceback
        traceback.print_exc()
    
    # Cleanup
    await apify_service.close()
    
    print(f"\n✅ Apify sentiment integration test completed!")


if __name__ == "__main__":
    asyncio.run(test_apify_sentiment_manual()) 