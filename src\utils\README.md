# 🛠️ Utilities and Configuration

> **Essential utilities for configuration, logging, and system operations**

## 🌟 **Overview**

The `utils/` module provides essential utility functions and classes that support the core trading platform operations. These utilities handle configuration management, structured logging, and time-related operations.

## 📁 **Components**

### **⚙️ `config.py` - Pydantic Configuration Management**
> **Type-safe configuration management with validation and environment variable support**

#### **Key Features**
- **Pydantic-based models** for type safety and validation
- **Environment variable substitution** with automatic loading
- **Nested configuration structures** for organized settings
- **Default value management** with sensible defaults
- **Configuration validation** with detailed error messages

#### **Configuration Models**

##### **Core Configuration Classes**
```python
class TradingConfigModel(BaseModel):
    watchlist: List[str]
    timeframes: TimeframeConfig
    indicators: IndicatorConfig
    risk_management: RiskManagementConfig
    trading: TradingConfig
    market_hours: MarketHoursConfig
    data_sources: DataSourcesConfig
    logging: LoggingConfig
    notifications: NotificationsConfig
    performance: PerformanceConfig
    ai_analysis: AIAnalysisConfig
    database: DatabaseConfig
    api_keys: APIKeysConfig
```

##### **AI Analysis Configuration**
```python
class AIAnalysisConfig(BaseModel):
    enabled: bool = True
    model_type: str = "tiered"
    confidence_threshold: float = 0.6
    tier_1_threshold: float = 0.5  # Free AI threshold
    tier_2_threshold: float = 0.75  # Paid AI threshold
    
    # Tier 1 Providers (Free)
    tier_1_providers: Dict[str, AIProviderConfig] = {
        "google_ai": AIProviderConfig(enabled=True, model="gemini-pro", cost_tier="free"),
        "groq": AIProviderConfig(enabled=True, model="llama3-8b-8192", cost_tier="free"),
        "huggingface": AIProviderConfig(enabled=True, model="meta-llama/Llama-2-7b-chat-hf", cost_tier="free"),
        "cerebras": AIProviderConfig(enabled=True, model="llama3.1-8b", cost_tier="free"),
        "openrouter": AIProviderConfig(enabled=True, model="meta-llama/llama-3.1-8b-instruct:free", cost_tier="free")
    }
    
    # Tier 2 Providers (Paid)
    tier_2_providers: Dict[str, AIProviderConfig] = {
        "openai": AIProviderConfig(enabled=True, model="gpt-4o-mini", cost_tier="paid"),
        "anthropic": AIProviderConfig(enabled=True, model="claude-3-sonnet-********", cost_tier="paid")
    }
```

##### **API Keys Configuration**
```python
class APIKeysConfig(BaseModel):
    # Market Data
    alpha_vantage: str = ""
    polygon: str = ""
    quandl: str = ""
    
    # Tier 1 AI Providers (Free)
    google_ai: str = ""
    groq: str = ""
    huggingface: str = ""
    cerebras: str = ""
    openrouter: str = ""
    
    # Tier 2 AI Providers (Paid)
    openai: str = ""
    anthropic: str = ""
    
    # Communications
    twilio_account_sid: str = ""
    twilio_auth_token: str = ""
```

#### **Configuration Manager**
```python
class ConfigManager:
    def __init__(self, config_path: str):
        self.config_path = config_path
        self._config = None
    
    def get_config(self) -> TradingConfigModel:
        if self._config is None:
            self._config = self._load_config()
        return self._config
    
    def reload_config(self) -> TradingConfigModel:
        self._config = self._load_config()
        return self._config
    
    def _load_config(self) -> TradingConfigModel:
        # Load from JSON with environment variable substitution
        # Validate using Pydantic models
        # Handle errors gracefully
```

#### **Usage Examples**
```python
from src.utils.config import setup_config

# Initialize configuration
config_manager = setup_config("config.json")
config = config_manager.get_config()

# Access nested configuration
watchlist = config.watchlist
ai_config = config.ai_analysis
tier_1_providers = ai_config.tier_1_providers

# Check specific provider settings
if ai_config.tier_1_providers["groq"].enabled:
    model = ai_config.tier_1_providers["groq"].model
    print(f"Groq enabled with model: {model}")
```

#### **Environment Variable Support**
```bash
# .env file
GROQ_API_KEY=your_groq_key_here
OPENAI_API_KEY=your_openai_key_here
TWILIO_ACCOUNT_SID=your_twilio_sid
```

```json
// config.json
{
  "api_keys": {
    "groq": "${GROQ_API_KEY}",
    "openai": "${OPENAI_API_KEY}",
    "twilio_account_sid": "${TWILIO_ACCOUNT_SID}"
  }
}
```

---

### **📋 `logging.py` - Structured Logging System**
> **Comprehensive logging system with structured data, performance tracking, and error context**

#### **Key Features**
- **Structured logging** with JSON formatting
- **Performance tracking** with metrics collection
- **Error logging** with full context and stack traces
- **Log rotation** and size management
- **Multiple log levels** with filtering
- **Real-time log streaming** for monitoring

#### **TradingLogger Class**
```python
class TradingLogger:
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.setup_logger()
    
    def log_startup(self):
        """Log system startup with configuration."""
    
    def log_shutdown(self):
        """Log system shutdown."""
    
    def log_performance(self, metrics: Dict[str, Any]):
        """Log performance metrics."""
    
    def log_signal(self, symbol: str, signal_type: str, confidence: float, indicators: Dict):
        """Log trading signal generation."""
    
    def log_error(self, error: Exception, context: Dict[str, Any]):
        """Log error with full context."""
    
    def log_market_data(self, symbol: str, data_source: str, status: str):
        """Log market data operations."""
```

#### **Structured Log Format**
```json
{
  "timestamp": "2025-01-01T12:00:00.000Z",
  "level": "INFO",
  "event_type": "signal_generated",
  "symbol": "AAPL",
  "signal_type": "STRONG_BUY",
  "confidence": 0.85,
  "indicators": {
    "rsi": 25.5,
    "macd": 1.2,
    "bollinger_bands": 0.8
  },
  "system_info": {
    "cpu_percent": 15.2,
    "memory_percent": 45.8,
    "process_id": 12345
  }
}
```

#### **Usage Examples**
```python
from src.utils.logging import setup_logging, get_logger

# Setup logging
logger = setup_logging(config.logging.model_dump())

# Log different types of events
logger.log_signal("AAPL", "STRONG_BUY", 0.85, indicators)
logger.log_performance({"event": "analysis_completed", "duration": 2.5})
logger.log_error(exception, {"symbol": "AAPL", "operation": "data_fetch"})

# Standard logging
logger.info("System initialized successfully")
logger.warning("API rate limit approaching")
logger.error("Failed to fetch market data")
```

#### **Log Categories**
- **Startup/Shutdown**: System lifecycle events
- **Performance**: Timing, resource usage, throughput metrics
- **Trading Signals**: Signal generation with full context
- **Market Data**: Data fetching, caching, validation
- **AI Analysis**: AI provider calls, responses, costs
- **Alerts**: Notification sending, delivery status
- **Errors**: Exceptions with full stack traces and context

---

### **⏰ `time_utils.py` - Time and Market Hours**
> **Market hours detection, timezone handling, and time-based operations**

#### **Key Features**
- **Market hours detection** including extended hours
- **Timezone conversion** for global markets
- **Trading calendar** integration
- **Time-based caching** logic
- **Holiday detection** for market closures

#### **Market Hours Functions**
```python
def is_market_hours(timezone="America/New_York") -> bool:
    """Check if current time is during trading hours."""

def is_extended_hours(timezone="America/New_York") -> bool:
    """Check if current time is during extended trading hours."""

def get_market_status() -> MarketStatus:
    """Get detailed market status (OPEN, CLOSED, PRE_MARKET, AFTER_HOURS)."""

def time_until_market_open() -> timedelta:
    """Get time remaining until market opens."""

def should_fetch_real_time() -> bool:
    """Determine if real-time data should be fetched based on market hours."""
```

#### **Usage Examples**
```python
from src.utils.time_utils import is_market_hours, get_market_status

# Check market status
if is_market_hours():
    # Fetch real-time data
    data = market_data.get_stock_data(symbol, real_time=True)
else:
    # Use cached data
    data = market_data.get_cached_data(symbol)

# Get detailed status
status = get_market_status()
print(f"Market is currently: {status.value}")
```

---

## 🔧 **Configuration Best Practices**

### **Environment Variables**
1. **Never commit API keys** to version control
2. **Use descriptive variable names** for clarity
3. **Provide defaults** for optional configuration
4. **Validate required variables** at startup

### **Configuration Structure**
1. **Group related settings** in nested objects
2. **Use meaningful names** for configuration keys
3. **Provide documentation** for complex settings
4. **Version your configuration** schema

### **Logging Guidelines**
1. **Use appropriate log levels** (DEBUG, INFO, WARNING, ERROR)
2. **Include context** in all log messages
3. **Structure log data** for easy parsing
4. **Monitor log file sizes** and implement rotation

---

## 🚀 **Performance Considerations**

### **Configuration Loading**
- **Cache configuration** after initial load
- **Lazy load** complex configuration sections
- **Validate early** to catch errors at startup

### **Logging Performance**
- **Use structured logging** for better performance
- **Filter logs** based on level to reduce overhead
- **Rotate logs** to prevent disk space issues
- **Use async logging** for high-frequency events

### **Time Operations**
- **Cache timezone objects** to avoid repeated lookups
- **Use UTC internally** and convert for display
- **Minimize datetime operations** in hot paths

---

## 🧪 **Testing Utilities**

### **Configuration Testing**
```python
def test_config_loading():
    config_manager = setup_config("test_config.json")
    config = config_manager.get_config()
    
    assert config.watchlist is not None
    assert len(config.api_keys.openai) > 0
```

### **Logging Testing**
```python
def test_structured_logging(caplog):
    logger = get_logger()
    logger.log_signal("AAPL", "BUY", 0.7, {"rsi": 30})
    
    assert "signal_generated" in caplog.text
    assert "AAPL" in caplog.text
```

### **Time Testing**
```python
def test_market_hours():
    # Mock current time to trading hours
    with patch('datetime.datetime.now') as mock_now:
        mock_now.return_value = datetime(2025, 1, 1, 10, 0)  # 10 AM ET
        assert is_market_hours() == True
```

---

**🛠️ Essential utilities powering reliable trading operations** ⚙️