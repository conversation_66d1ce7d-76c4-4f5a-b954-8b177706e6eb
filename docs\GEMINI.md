# 🤖 AI-Nvestor: Gemini AI Assistant Instructions

## Project Overview

AI-Nvestor is an advanced algorithmic trading platform that combines multi-source market data aggregation, technical analysis, and tiered AI orchestration to generate actionable trading signals. The platform is designed for cost-conscious engineering with a focus on performance, reliability, and financial accuracy.

**Key Philosophy**: 80% of trading signals can be effectively screened using free AI providers, with expensive premium AI reserved only for high-confidence validation scenarios.

## 🎯 **Primary Objectives**

1. **Generate accurate trading signals** using technical indicators and AI analysis
2. **Optimize costs** through tiered AI approach (free Tier 1 for screening, paid Tier 2 for validation)
3. **Provide real-time market analysis** with comprehensive logging and monitoring
4. **Ensure system reliability** with robust error handling and fallback mechanisms
5. **Maintain financial accuracy** with proper data validation and conflict detection

## 🏗️ **Architecture Context**

### Core Components

- **Service Factory Pattern**: Dependency injection container for service management
- **Async Trading Platform**: Parallel processing with controlled concurrency
- **Tiered AI Orchestrator**: Free AI providers (Google AI, Groq, OpenRouter) for screening, paid providers (OpenAI, Anthropic) for validation
- **Multi-Source Data Aggregation**: Yahoo Finance, Alpha Vantage, Polygon with intelligent caching
- **Technical Analysis Engine**: 13+ indicators (RSI, MACD, Bollinger Bands, etc.)
- **Error Handler**: Centralized recovery with exponential backoff and circuit breakers

### Key Design Principles

- **Cost-Conscious Engineering**: 80% of signals screened with free AI, expensive AI only for validation
- **Resilience First**: Multiple fallback mechanisms and graceful degradation
- **Performance Through Parallelism**: Async processing with controlled concurrency
- **Financial Accuracy**: Enhanced fallback system using `N/A` and `(undefined)` instead of misleading values

## 🔑 **API Key Configuration & Namespaces**

### Required API Keys

**Data Source APIs (Required for market data):**
```bash
# Alpha Vantage (Free tier: 5 calls/minute, 500 calls/day)
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key_here

# Polygon.io (Free tier: 5 calls/minute, 100 calls/day)
POLYGON_API_KEY=your_polygon_key_here

# Quandl (Optional - additional data source)
QUANDL_API_KEY=your_quandl_key_here
```

**AI Provider APIs (Tier 1 - Free):**
```bash
# Google AI (Gemini-1.5-Flash) - Free tier: 15 requests/minute
GOOGLE_AI_API_KEY=your_google_ai_key_here

# Groq (Llama3-8B) - Free tier: 100 requests/minute
GROQ_API_KEY=your_groq_key_here

# OpenRouter (Claude-3-Haiku) - Free tier: 10 requests/minute
OPEN_ROUTER_API_KEY=your_openrouter_key_here
```

**AI Provider APIs (Tier 2 - Paid, for validation only):**
```bash
# OpenAI (GPT-4) - Paid tier: $0.03/1K tokens
OPENAI_API_KEY=your_openai_key_here

# Anthropic (Claude-3-Sonnet) - Paid tier: $0.015/1K tokens
ANTHROPIC_API_KEY=your_anthropic_key_here
```

**Notification APIs (Optional):**
```bash
# Twilio (SMS notifications)
TWILIO_ACCOUNT_SID=your_twilio_account_sid_here
TWILIO_AUTH_TOKEN=your_twilio_auth_token_here
TWILIO_FROM_NUMBER=your_twilio_phone_number_here
TWILIO_TO_NUMBER=your_phone_number_here
```

### Environment Variable Setup

1. **Copy the example file:**
```bash
cp env.example .env
```

2. **Edit the .env file** with your actual API keys:
```bash
# Open in your preferred editor
nano .env
# or
code .env
```

3. **Verify configuration:**
```bash
# Test configuration loading
python -c "from src.utils.config import setup_config; config = setup_config('config.json'); print('✅ Config loaded successfully')"
```

### API Key Namespaces in Code

**Configuration Structure:**
```json
{
  "api_keys": {
    "alpha_vantage": "${ALPHA_VANTAGE_API_KEY}",
    "polygon": "${POLYGON_API_KEY}",
    "quandl": "${QUANDL_API_KEY}",
    "openai": "${OPENAI_API_KEY}",
    "anthropic": "${ANTHROPIC_API_KEY}",
    "google_ai": "${GOOGLE_AI_API_KEY}",
    "groq": "${GROQ_API_KEY}",
    "openrouter": "${OPEN_ROUTER_API_KEY}",
    "cerebras": "${CEREBRAS_API_KEY}",
    "twilio_account_sid": "${TWILIO_ACCOUNT_SID}",
    "twilio_auth_token": "${TWILIO_AUTH_TOKEN}"
  }
}
```

**Code Access Pattern:**
```python
# In src/utils/config.py
class APIKeysConfig(BaseModel):
    alpha_vantage: str = Field(default="", description="Alpha Vantage API key")
    polygon: str = Field(default="", description="Polygon API key")
    openai: str = Field(default="", description="OpenAI API key")
    anthropic: str = Field(default="", description="Anthropic API key")
    google_ai: str = Field(default="", description="Google AI API key")
    groq: str = Field(default="", description="Groq API key")
    openrouter: str = Field(default="", description="OpenRouter API key")
    # ... more keys
```

## 🚀 **Common Tasks & Instructions**

### 1. **Initial Setup (Step-by-Step)**

**Prerequisites:**
```bash
# Ensure Python 3.8+ is installed
python --version

# Install dependencies
pip install -r requirements.txt

# Create necessary directories
mkdir -p outputs/analysis outputs/logs outputs/temp data logs
```

**Configuration Setup:**
```bash
# 1. Copy configuration files
cp config.json.example config.json
cp env.example .env

# 2. Edit config.json with your watchlist
nano config.json

# 3. Add your API keys to .env
nano .env

# 4. Validate configuration
python -c "from src.utils.config import setup_config; config = setup_config('config.json'); print('✅ Configuration valid')"
```

### 2. **Running Analysis**

**Manual Analysis (Recommended for testing):**
```bash
# Analyze specific symbols with detailed output
python scripts/analyze_manual.py --symbols AAPL MSFT GOOGL --force-realtime --output txt

# Analyze all symbols in config
python scripts/analyze_manual.py --all --force-realtime --output txt

# Console output for quick testing
python scripts/analyze_manual.py --symbols AAPL --force-realtime --output console

# Test with AI analysis disabled
python scripts/analyze_manual.py --symbols TSLA --no-ai --output console
```

**Standard Platform Analysis:**
```bash
# Analyze a list of symbols
python run.py --mode analysis --symbols AAPL MSFT GOOGL

# Real-time monitoring (runs analysis every 5 minutes)
python run.py --mode monitor

# Backtest mode
python run.py --mode backtest --start-date 2023-01-01 --end-date 2023-12-31
```

### 3. **Configuration Management**

**Key Configuration Files:**
- `config.json`: Main configuration (watchlist, timeframes, indicators, risk management)
- `.env`: API keys and sensitive data (never commit to version control)
- `src/constants/settings.py`: System constants and enums

**Important Configuration Sections:**
```json
{
  "watchlist": ["AAPL", "MSFT", "GOOGL", "NVDA", "TSLA"],
  "timeframes": {
    "interval": "5m",
    "days_of_history": 30,
    "backtest_days": 365
  },
  "ai_analysis": {
    "enabled": true,
    "tier_1_threshold": 0.5,
    "tier_2_threshold": 0.75,
    "stop_at_tier_1": true,
    "tier_1_providers": {
      "google_ai": {"enabled": true, "model": "gemini-1.5-flash"},
      "groq": {"enabled": true, "model": "llama3-8b-8192"},
      "openrouter": {"enabled": true, "model": "claude-3-haiku"}
    },
    "tier_2_providers": {
      "openai": {"enabled": false, "model": "gpt-4o-mini"},
      "anthropic": {"enabled": false, "model": "claude-3-sonnet-20240229"}
    }
  }
}
```

### 4. **AI Integration & Analysis**

**Tiered AI Approach:**
- **Tier 1 (Free)**: Google AI (Gemini-1.5-Flash), Groq (Llama3-8B), OpenRouter (Claude-3-Haiku)
- **Tier 2 (Paid)**: OpenAI GPT-4, Anthropic Claude for validation only
- **Smart Escalation**: Free AI for screening, paid AI for validation when confidence > 0.75

**AI Analysis Types:**
- Sentiment analysis of news and social media
- Pattern recognition in price data
- Predictive analytics for price movements
- Risk assessment using AI models
- Portfolio optimization recommendations

**AI Provider Configuration:**
```python
# Tier 1 Providers (Free)
tier_1_providers = {
    "google_ai": {
        "enabled": True,
        "model": "gemini-1.5-flash",
        "max_tokens": 500,
        "temperature": 0.3,
        "cost_tier": "free"
    },
    "groq": {
        "enabled": True,
        "model": "llama3-8b-8192",
        "max_tokens": 500,
        "temperature": 0.3,
        "cost_tier": "free"
    },
    "openrouter": {
        "enabled": True,
        "model": "claude-3-haiku",
        "max_tokens": 500,
        "temperature": 0.3,
        "cost_tier": "free"
    }
}

# Tier 2 Providers (Paid)
tier_2_providers = {
    "openai": {
        "enabled": False,  # Set to True for paid validation
        "model": "gpt-4o-mini",
        "max_tokens": 1000,
        "temperature": 0.3,
        "cost_tier": "paid"
    },
    "anthropic": {
        "enabled": False,  # Set to True for paid validation
        "model": "claude-3-sonnet-20240229",
        "max_tokens": 1000,
        "temperature": 0.3,
        "cost_tier": "paid"
    }
}
```

### 5. **Error Handling & Debugging**

**Common Error Scenarios:**
- **API Rate Limits**: Automatic retry with exponential backoff
- **Data Quality Issues**: Fallback to cached data or skip analysis
- **AI Provider Failures**: Rotate to alternative providers
- **Network Timeouts**: Retry with increasing delays

**Debugging Commands:**
```bash
# Check system status
python -c "from src.core.service_factory import ServiceFactory; sf = ServiceFactory(); print(sf.get_service_status())"

# Test specific service
python -c "from src.services.market_data import MarketDataService; mds = MarketDataService(); print(mds.get_stock_data('AAPL'))"

# Validate configuration
python -c "from src.utils.config import setup_config; config = setup_config('config.json'); print('Config valid')"

# Test AI providers
python -c "from src.services.ai_advisor import AIAdvisor; ai = AIAdvisor({}); print('AI Advisor initialized')"

# Check API key loading
python -c "import os; from dotenv import load_dotenv; load_dotenv(); print('API Keys:', [k for k in os.environ.keys() if 'API_KEY' in k])"
```

### 6. **Performance Optimization**

**Current Performance Metrics:**
- 66 symbols analyzed in ~4 minutes
- ~40% realistic conflict rate (corrected from 56.1% inflated rate)
- 99.9% uptime target with comprehensive error recovery
- 90% cost reduction through tiered AI approach

**Optimization Strategies:**
- Use `--force-realtime` for bypassing market hours restrictions
- Implement `stop_at_tier_1` configuration to control Tier 2 escalation
- Batch processing for multiple symbols
- Intelligent caching with 5-minute freshness

## 🔧 **Development Guidelines**

### Code Structure

```
src/
├── core/                    # Core platform components
│   ├── async_trading_platform.py  # Main orchestrator
│   ├── service_factory.py         # Dependency injection
│   ├── error_handler.py           # Centralized error handling
│   └── exceptions.py              # Custom exceptions
├── services/                # Business logic services
│   ├── ai_advisor.py             # AI analysis and orchestration
│   ├── market_data.py            # Data aggregation
│   ├── indicators.py             # Technical analysis
│   └── signals.py                # Signal generation
├── utils/                   # Utility functions
│   ├── config.py                # Configuration management
│   ├── logging.py               # Logging setup
│   └── ai_logger.py             # AI-specific logging
└── constants/               # System constants
    └── settings.py              # Enums and constants
```

### Best Practices

1. **Error Handling**: Always use the centralized error handler
2. **Logging**: Use structured JSON logging with full context
3. **Configuration**: Validate configuration with Pydantic models
4. **Testing**: Test individual services before integration
5. **Documentation**: Update documentation for any architectural changes

### Testing Commands

```bash
# Test all symbols
python tests/test_all_symbols.py

# Test specific indicators
python tests/test_indicators.py

# Test platform functionality
python scripts/test_platform.py

# Verify mathematical calculations
python scripts/verify_math.py

# Verify real data integration
python scripts/verify_real_data.py
```

## 📊 **Analysis Output Interpretation**

### Signal Types
- `STRONG_BUY`: Very strong bullish signal
- `BUY`: Moderate bullish signal
- `HOLD`: Neutral position
- `SELL`: Moderate bearish signal
- `STRONG_SELL`: Very strong bearish signal

### Confidence Levels
- `0.0-0.25`: Very Low confidence
- `0.25-0.50`: Low confidence
- `0.50-0.75`: Moderate confidence
- `0.75-0.90`: High confidence
- `0.90-1.00`: Very High confidence

### Conflict Detection
- **Genuine Conflicts**: Only opposite-direction disagreements between technical signals and AI analysis
- **Conflict Rate**: ~40% realistic rate (corrected from inflated 56.1%)
- **Resolution**: AI consensus with confidence scoring

## 🚨 **Troubleshooting**

### Common Issues & Solutions

**1. API Key Errors:**
```bash
# Error: "No API key found for provider X"
# Solution: Check .env file and ensure key is set
grep -i "API_KEY" .env

# Error: "Invalid API key for provider X"
# Solution: Verify key is correct and has proper permissions
python -c "from src.services.ai_advisor import AIAdvisor; ai = AIAdvisor({}); print('Testing AI providers...')"
```

**2. Rate Limiting Issues:**
```bash
# Error: "Rate limit exceeded for provider X"
# Solution: Implement exponential backoff or reduce concurrent requests
# Check current rate limits:
python -c "from src.core.async_trading_platform import AsyncTradingPlatform; atp = AsyncTradingPlatform(); print('Rate limits configured')"
```

**3. Data Quality Issues:**
```bash
# Error: "No data available for symbol X"
# Solution: Check if symbol exists and data source is working
python -c "from src.services.market_data import MarketDataService; mds = MarketDataService(); data = mds.get_stock_data('AAPL'); print(f'Data shape: {data.shape if data is not None else None}')"
```

**4. Memory Issues:**
```bash
# Error: "Memory usage exceeded"
# Solution: Reduce batch size or clear cache
python -c "import gc; gc.collect(); print('Memory cleared')"
```

**5. Network Issues:**
```bash
# Error: "Connection timeout"
# Solution: Check internet connection and retry with backoff
python -c "import requests; response = requests.get('https://api.polygon.io/v2/aggs/ticker/AAPL/range/1/day/2023-01-09/2023-01-09?apiKey=DEMO_KEY'); print(f'Status: {response.status_code}')"
```

### Emergency Procedures

**1. System Overload:**
```bash
# Reduce max_workers in async platform
# Edit config.json:
{
  "performance": {
    "max_workers": 5  # Reduce from default 10
  }
}
```

**2. API Failures:**
```bash
# Disable problematic providers in configuration
# Edit config.json:
{
  "ai_analysis": {
    "tier_1_providers": {
      "google_ai": {"enabled": false},  # Disable if failing
      "groq": {"enabled": true},
      "openrouter": {"enabled": true}
    }
  }
}
```

**3. Data Corruption:**
```bash
# Clear cache and restart system
rm -rf outputs/temp/*
rm -rf data/cache/*
python -c "from src.utils.config import setup_config; config = setup_config('config.json'); print('Cache cleared')"
```

**4. Performance Issues:**
```bash
# Check logging for bottlenecks
tail -f logs/trading.log | grep -i "error\|warning\|performance"
```

## 📈 **Monitoring & Alerts**

### Key Metrics to Monitor

**1. Analysis Performance:**
```bash
# Monitor analysis execution time
python -c "
import time
from src.core.async_trading_platform import AsyncTradingPlatform
start_time = time.time()
atp = AsyncTradingPlatform()
# Run analysis
end_time = time.time()
print(f'Analysis time: {end_time - start_time:.2f} seconds')
"
```

**2. AI Provider Success Rates:**
```bash
# Check AI provider status
python -c "
from src.services.ai_advisor import AIAdvisor
ai = AIAdvisor({})
providers = ['google_ai', 'groq', 'openrouter']
for provider in providers:
    print(f'{provider}: {ai.is_provider_available(provider) if hasattr(ai, \"is_provider_available\") else \"Unknown\"}')
"
```

**3. System Resource Usage:**
```bash
# Monitor CPU and memory usage
python -c "
import psutil
cpu_percent = psutil.cpu_percent(interval=1)
memory_percent = psutil.virtual_memory().percent
print(f'CPU: {cpu_percent}%, Memory: {memory_percent}%')
"
```

**4. API Rate Limit Utilization:**
```bash
# Check API usage
python -c "
import requests
import json
# This would need to be implemented based on your API providers
print('API rate limit monitoring not implemented')
"
```

### Alert Conditions

**1. Performance Alerts:**
- Analysis time > 5 minutes for 66 symbols
- Memory usage > 80%
- CPU usage > 90%

**2. Quality Alerts:**
- AI provider failure rate > 20%
- Data quality errors > 10%
- Signal conflict rate > 50%

**3. System Alerts:**
- Error rate > 10%
- API rate limit exceeded
- Disk space < 10%

## 🔐 **Security Considerations**

### 1. API Key Management

**Best Practices:**
```bash
# Never commit API keys to version control
echo ".env" >> .gitignore
echo "*.key" >> .gitignore
echo "secrets/" >> .gitignore

# Use environment variables
export ALPHA_VANTAGE_API_KEY="your_key_here"
export OPENAI_API_KEY="your_key_here"

# Rotate keys regularly
# Set up key rotation schedule in your calendar
```

**Key Validation:**
```bash
# Validate API keys are loaded correctly
python -c "
import os
from dotenv import load_dotenv
load_dotenv()
required_keys = ['ALPHA_VANTAGE_API_KEY', 'GOOGLE_AI_API_KEY', 'GROQ_API_KEY']
missing_keys = [key for key in required_keys if not os.getenv(key)]
if missing_keys:
    print(f'Missing keys: {missing_keys}')
else:
    print('All required keys found')
"
```

### 2. Data Privacy

**Data Anonymization:**
```python
# Example data anonymization
def anonymize_data(data):
    """Anonymize sensitive data before logging."""
    if 'api_key' in data:
        data['api_key'] = '***'
    if 'password' in data:
        data['password'] = '***'
    return data
```

### 3. Access Control

**File Permissions:**
```bash
# Set proper file permissions
chmod 600 .env
chmod 644 config.json
chmod 755 src/
```

### 4. Audit Logging

**Comprehensive Logging:**
```python
# Example audit logging
import logging
from datetime import datetime

def log_audit_event(event_type, user, action, details):
    """Log audit events for security monitoring."""
    audit_logger = logging.getLogger('audit')
    audit_logger.info(f'{datetime.now()} | {event_type} | {user} | {action} | {details}')
```

## 📚 **Additional Resources**

### Documentation Files

- **Architecture Documentation**: `ARCHITECTURE.md` - Detailed system architecture
- **API Documentation**: `API_DOCUMENTATION.md` - API reference for all integrations
- **Quick Start Guide**: `docs/QUICK_START.md` - Step-by-step setup guide
- **AI Integration Guide**: `docs/AI_INTEGRATION.md` - AI provider integration details
- **Analysis Guide**: `ANALYSIS_GUIDE.md` - How to interpret analysis results

### Code Examples

**1. Basic Analysis Example:**
```python
from src.core.async_trading_platform import AsyncTradingPlatform
from src.utils.config import setup_config

# Initialize platform
config = setup_config('config.json')
platform = AsyncTradingPlatform('config.json')

# Run analysis
symbols = ['AAPL', 'MSFT', 'GOOGL']
results = await platform.run_analysis_async(symbols)

# Process results
for symbol, result in results.items():
    if result.success:
        print(f"{symbol}: {result.data['signal']} (Confidence: {result.data['confidence']})")
    else:
        print(f"{symbol}: Error - {result.error}")
```

**2. Custom AI Analysis:**
```python
from src.services.ai_advisor import AIAdvisor
import pandas as pd

# Initialize AI advisor
ai_advisor = AIAdvisor(config={})

# Analyze sentiment
news_data = [{'title': 'Apple reports strong earnings', 'sentiment': 'positive'}]
sentiment_result = await ai_advisor.analyze_sentiment('AAPL', news_data)

# Detect patterns
price_data = pd.DataFrame({'Close': [100, 101, 102, 103, 104]})
pattern_result = await ai_advisor.detect_patterns('AAPL', price_data)
```

**3. Error Handling Example:**
```python
from src.core.error_handler import handle_trading_error

try:
    # Your trading logic here
    result = await platform.analyze_symbol('AAPL')
except Exception as e:
    # Handle error with centralized error handler
    handled_result = handle_trading_error(e, context={'symbol': 'AAPL'})
    print(f"Error handled: {handled_result}")
```

## 🎯 **Success Criteria**

### Performance Metrics

1. **Speed**: 66 symbols analyzed in <5 minutes
2. **Accuracy**: >90% signal accuracy with <60% false positives
3. **Reliability**: 99.9% uptime with comprehensive error recovery
4. **Cost Efficiency**: 90% cost reduction through tiered AI approach
5. **User Experience**: Clear, actionable insights with detailed reasoning

### Quality Assurance

**1. Signal Quality:**
- Technical indicators properly calculated
- AI analysis provides meaningful insights
- Conflict detection accurately identifies disagreements
- Confidence scores reflect actual reliability

**2. System Reliability:**
- Graceful handling of API failures
- Automatic retry with exponential backoff
- Comprehensive error logging
- Data validation and quality checks

**3. Cost Optimization:**
- Tier 1 providers used for screening
- Tier 2 providers only for high-confidence validation
- Intelligent routing to minimize costs
- Cost tracking and monitoring

## 🔄 **Maintenance & Updates**

### Regular Maintenance Tasks

**1. Weekly:**
- Review error logs and resolve issues
- Check API key validity and quotas
- Update watchlist based on market conditions
- Monitor performance metrics

**2. Monthly:**
- Review and update configuration
- Check for new AI provider options
- Update dependencies and security patches
- Review and optimize performance

**3. Quarterly:**
- Comprehensive system audit
- Update documentation
- Review and adjust risk parameters
- Performance optimization

### Update Procedures

**1. Configuration Updates:**
```bash
# Backup current configuration
cp config.json config.json.backup.$(date +%Y%m%d)

# Update configuration
nano config.json

# Validate new configuration
python -c "from src.utils.config import setup_config; config = setup_config('config.json'); print('✅ Configuration updated successfully')"
```

**2. Code Updates:**
```bash
# Pull latest changes
git pull origin main

# Update dependencies
pip install -r requirements.txt

# Run tests
python tests/test_all_symbols.py
```

---

**Remember**: This platform is designed for educational and research purposes. Always validate signals with additional analysis and never risk more than you can afford to lose.

**Support**: For issues or questions, check the troubleshooting section above or refer to the documentation files listed in the Additional Resources section. 