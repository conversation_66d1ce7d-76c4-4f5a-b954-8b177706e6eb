"""
Time utilities for AI-Nvestor.

This module provides:
- Market hours detection
- Timezone handling
- Date range calculations
- Trading session utilities
"""

from datetime import datetime, timedelta, time
from typing import Optional, Tuple
import pytz


def is_market_hours(current_time: Optional[datetime] = None, 
                   timezone: str = "America/New_York") -> bool:
    """
    Check if current time is during market hours.
    
    Args:
        current_time: Current time (defaults to now)
        timezone: Market timezone
        
    Returns:
        True if during market hours
    """
    if current_time is None:
        current_time = datetime.now()
    
    # Convert to market timezone
    tz = pytz.timezone(timezone)
    # Ensure datetime is timezone-aware
    if current_time.tzinfo is None:
        # If naive, assume it's in the local timezone
        current_time = pytz.UTC.localize(current_time)
    market_time = current_time.astimezone(tz)
    
    # Market hours: 9:30 AM - 4:00 PM ET
    market_start = time(9, 30)
    market_end = time(16, 0)
    
    current_time_only = market_time.time()
    return market_start <= current_time_only <= market_end


def get_market_session_dates(start_date: datetime, end_date: datetime) -> list:
    """
    Get list of trading session dates.
    
    Args:
        start_date: Start date
        end_date: End date
        
    Returns:
        List of trading session dates
    """
    dates = []
    current_date = start_date
    
    while current_date <= end_date:
        # Skip weekends (Saturday=5, Sunday=6)
        if current_date.weekday() < 5:
            dates.append(current_date)
        current_date += timedelta(days=1)
    
    return dates


def get_next_market_open(current_time: Optional[datetime] = None,
                        timezone: str = "America/New_York") -> datetime:
    """
    Get the next market open time.
    
    Args:
        current_time: Current time (defaults to now)
        timezone: Market timezone
        
    Returns:
        Next market open datetime
    """
    if current_time is None:
        current_time = datetime.now()
    
    tz = pytz.timezone(timezone)
    # Ensure datetime is timezone-aware
    if current_time.tzinfo is None:
        # If naive, assume it's in the local timezone
        current_time = pytz.UTC.localize(current_time)
    market_time = current_time.astimezone(tz)
    
    # If it's weekend, move to next Monday
    while market_time.weekday() >= 5:  # Saturday or Sunday
        market_time += timedelta(days=1)
    
    # Set to 9:30 AM
    next_open = market_time.replace(hour=9, minute=30, second=0, microsecond=0)
    
    # If today is past market hours, move to next day
    if market_time.time() >= time(16, 0):
        next_open += timedelta(days=1)
        # Handle weekend
        while next_open.weekday() >= 5:
            next_open += timedelta(days=1)
    
    return next_open


def format_timestamp(timestamp: datetime, format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
    """
    Format timestamp for display.
    
    Args:
        timestamp: Datetime to format
        format_str: Format string
        
    Returns:
        Formatted timestamp string
    """
    return timestamp.strftime(format_str)


def parse_date_range(date_range: str) -> Tuple[datetime, datetime]:
    """
    Parse date range string.
    
    Args:
        date_range: String like "1d", "1w", "1m", "3m", "6m", "1y"
        
    Returns:
        Tuple of (start_date, end_date)
    """
    end_date = datetime.now()
    
    if date_range.endswith('d'):
        days = int(date_range[:-1])
        start_date = end_date - timedelta(days=days)
    elif date_range.endswith('w'):
        weeks = int(date_range[:-1])
        start_date = end_date - timedelta(weeks=weeks)
    elif date_range.endswith('m'):
        months = int(date_range[:-1])
        # Proper month calculation using date arithmetic
        year = end_date.year
        month = end_date.month
        
        for _ in range(months):
            month -= 1
            if month <= 0:
                month = 12
                year -= 1
        
        start_date = end_date.replace(year=year, month=month)
    elif date_range.endswith('y'):
        years = int(date_range[:-1])
        # Proper year calculation accounting for leap years
        try:
            start_date = end_date.replace(year=end_date.year - years)
        except ValueError:
            # Handle Feb 29 in leap years
            start_date = end_date.replace(year=end_date.year - years, month=2, day=28)
    else:
        raise ValueError(f"Invalid date range format: {date_range}")
    
    return start_date, end_date
