# 📁 Source Code Architecture

> **Modern trading engine with dependency injection, async processing, and centralized error handling**

## 🏗️ **Directory Structure**

```
src/
├── core/              # Core architecture components
│   ├── error_handler.py       # Centralized error handling
│   ├── service_factory.py     # Dependency injection & service management
│   ├── base_client.py         # Base API client with rate limiting
│   └── exceptions.py          # Custom exception hierarchy
├── services/          # Trading services
│   ├── ai_advisor.py          # Tiered AI orchestrator
│   ├── market_data.py         # Multi-source data aggregation
│   ├── indicators.py          # Technical indicators
│   └── alerts.py              # Notification system
├── utils/             # Configuration and utilities
│   ├── config.py              # Pydantic-based config management
│   └── logging.py             # Structured logging system
├── constants/         # Centralized settings and enums
│   └── settings.py            # Application constants
└── main.py            # Application entry point logic
run.py                 # 🚀 Official script to run the application
```

## 🔧 **Core Components**

### **`core/` - The Foundation**
- **`service_factory.py`**: Manages dependency injection, creating and providing all necessary services.
- **`base_client.py`**: A robust, async-first API client with rate limiting, retries, and error handling.
- **`error_handler.py`**: Centralized error handling and recovery strategies.
- **`exceptions.py`**: A hierarchy of custom exceptions for predictable error states.

### **`services/` - Business Logic**
- **`ai_advisor.py`**: Tiered AI orchestrator with 7+ providers, optimized for cost and performance.
- **`market_data.py`**: Multi-source data aggregation with intelligent, time-aware caching.
- **`indicators.py`**: Vectorized technical indicator calculation engine.
- **`signals.py`**: Advanced signal generation with a multi-indicator consensus algorithm.

## 🚀 **Service Architecture**

### **ServiceFactory Pattern**
All services are instantiated and managed through a central factory. This promotes loose coupling and makes the system easier to test and maintain.

```python
# services are created and managed by the factory
factory = ServiceFactory(config_manager)
services = factory.create_container()

market_data_service = services.market_data
ai_advisor_service = services.ai_advisor
```

### **Asynchronous by Default**
The entire platform is built on `asyncio` to handle concurrent operations efficiently. All I/O-bound tasks, especially API calls for market data and AI analysis, are non-blocking.

```python
# Example of concurrent analysis
async def run_multiple_analyses(symbols):
    tasks = [platform.run_analysis([symbol]) for symbol in symbols]
    await asyncio.gather(*tasks)
```

### **Tiered AI Orchestrator**
To manage costs and optimize performance, the system uses a tiered approach to AI analysis.

1.  **Tier 1 (Free Providers)**: Fast, free models are used for initial analysis and signal screening.
2.  **Tier 2 (Paid Providers)**: More powerful, paid models are only engaged to validate high-confidence signals, minimizing API costs.

This architecture ensures that expensive resources are used judiciously without sacrificing the quality of the final trading signal.
