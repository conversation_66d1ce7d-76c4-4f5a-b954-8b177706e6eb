#!/usr/bin/env python3
"""
Indicator Weight Optimizer Service

This module provides comprehensive tools for iterating on and optimizing
financial indicator weights and values using multiple approaches:

1. Machine Learning-based Optimization
2. Backtesting-driven Optimization
3. Adaptive Learning Optimization
4. Genetic Algorithm Optimization
5. Bayesian Optimization
6. Ensemble-based Optimization
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Union, Any
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
import json
import pickle
from pathlib import Path
import asyncio
from concurrent.futures import ThreadPoolExecutor
import warnings
warnings.filterwarnings('ignore')

# Machine Learning imports
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import Ridge, Lasso
from sklearn.model_selection import TimeSeriesSplit, cross_val_score
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.optimization import minimize

# Optimization imports
from scipy.optimize import differential_evolution, minimize_scalar
import optuna

# Visualization
import matplotlib.pyplot as plt
import seaborn as sns

from utils.logging import get_logger
from utils.config import get_config
from services.indicators import TechnicalIndicators, IndicatorResult, SignalType
from services.adaptive_learning import AdaptiveLearningService
from backtesting.ai_weight_backtester import AIWeightBacktester, WeightConfig, BacktestMetrics


class OptimizationMethod(Enum):
    """Different optimization methods for indicator weights."""
    MACHINE_LEARNING = "machine_learning"
    BACKTESTING = "backtesting"
    ADAPTIVE_LEARNING = "adaptive_learning"
    GENETIC_ALGORITHM = "genetic_algorithm"
    BAYESIAN_OPTIMIZATION = "bayesian_optimization"
    ENSEMBLE = "ensemble"
    HYBRID = "hybrid"


@dataclass
class IndicatorWeight:
    """Represents an indicator weight with optimization capabilities."""
    name: str
    current_weight: float
    min_weight: float = 0.0
    max_weight: float = 2.0
    base_weight: float = 1.0
    category: str = "technical"
    importance_score: float = 0.5
    performance_history: List[float] = field(default_factory=list)
    last_updated: datetime = field(default_factory=datetime.now)
    
    def update_weight(self, new_weight: float, performance: float = None):
        """Update weight with performance tracking."""
        if performance is not None:
            self.performance_history.append(performance)
            if len(self.performance_history) > 100:
                self.performance_history = self.performance_history[-100:]
        
        self.current_weight = np.clip(new_weight, self.min_weight, self.max_weight)
        self.last_updated = datetime.now()


@dataclass
class OptimizationResult:
    """Result of weight optimization process."""
    method: OptimizationMethod
    optimized_weights: Dict[str, float]
    performance_metrics: Dict[str, float]
    feature_importance: Dict[str, float]
    optimization_time: float
    iterations: int
    convergence: bool
    metadata: Dict[str, Any]


class IndicatorWeightOptimizer:
    """
    Comprehensive indicator weight optimizer using multiple approaches.
    
    Features:
    - Machine learning-based weight optimization
    - Backtesting-driven optimization
    - Adaptive learning integration
    - Genetic algorithm optimization
    - Bayesian optimization
    - Ensemble methods
    - Cross-validation and out-of-sample testing
    """
    
    def __init__(self, config: Dict):
        """Initialize the indicator weight optimizer."""
        self.config = config
        self.logger = get_logger()
        
        # Initialize services
        self.indicators = TechnicalIndicators(config)
        self.adaptive_learning = AdaptiveLearningService(config)
        self.backtester = AIWeightBacktester(config)
        
        # Optimization configuration
        self.optimization_config = config.get('indicator_optimization', {})
        self.enabled = self.optimization_config.get('enabled', True)
        self.optimization_frequency = self.optimization_config.get('frequency', 'weekly')
        self.min_data_points = self.optimization_config.get('min_data_points', 100)
        
        # Weight storage
        self.data_dir = Path("data/indicator_optimization")
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize weights
        self.indicator_weights = self._initialize_indicator_weights()
        
        # Performance tracking
        self.optimization_history = []
        self.performance_tracking = {}
        
        self.logger.info("🚀 Indicator Weight Optimizer initialized")
    
    def _initialize_indicator_weights(self) -> Dict[str, IndicatorWeight]:
        """Initialize indicator weights with default values."""
        weights = {}
        
        # Technical indicators
        technical_indicators = {
            'rsi': IndicatorWeight('rsi', 1.0, category='momentum'),
            'macd': IndicatorWeight('macd', 1.2, category='trend'),
            'sma': IndicatorWeight('sma', 1.1, category='trend'),
            'ema': IndicatorWeight('ema', 1.1, category='trend'),
            'bollinger_bands': IndicatorWeight('bollinger_bands', 0.9, category='volatility'),
            'stochastic': IndicatorWeight('stochastic', 0.8, category='momentum'),
            'williams_r': IndicatorWeight('williams_r', 0.7, category='momentum'),
            'cci': IndicatorWeight('cci', 0.8, category='momentum'),
            'atr': IndicatorWeight('atr', 0.6, category='volatility'),
            'obv': IndicatorWeight('obv', 0.5, category='volume'),
            'vwap': IndicatorWeight('vwap', 1.0, category='volume'),
            'money_flow_index': IndicatorWeight('money_flow_index', 0.7, category='volume'),
            'keltner_channels': IndicatorWeight('keltner_channels', 0.6, category='volatility')
        }
        
        weights.update(technical_indicators)
        return weights
    
    async def optimize_weights(self, symbols: List[str], start_date: str, end_date: str,
                             method: OptimizationMethod = OptimizationMethod.HYBRID) -> OptimizationResult:
        """
        Optimize indicator weights using the specified method.
        
        Args:
            symbols: List of symbols to optimize for
            start_date: Start date for optimization
            end_date: End date for optimization
            method: Optimization method to use
            
        Returns:
            OptimizationResult with optimized weights and metrics
        """
        if not self.enabled:
            return self._get_default_result(method)
        
        self.logger.info(f"🔧 Starting weight optimization using {method.value}")
        start_time = datetime.now()
        
        try:
            # Load historical data
            data = await self._load_historical_data(symbols, start_date, end_date)
            if not data:
                return self._get_default_result(method)
            
            # Run optimization based on method
            if method == OptimizationMethod.MACHINE_LEARNING:
                result = await self._optimize_with_machine_learning(data, symbols)
            elif method == OptimizationMethod.BACKTESTING:
                result = await self._optimize_with_backtesting(data, symbols)
            elif method == OptimizationMethod.ADAPTIVE_LEARNING:
                result = await self._optimize_with_adaptive_learning(data, symbols)
            elif method == OptimizationMethod.GENETIC_ALGORITHM:
                result = await self._optimize_with_genetic_algorithm(data, symbols)
            elif method == OptimizationMethod.BAYESIAN_OPTIMIZATION:
                result = await self._optimize_with_bayesian_optimization(data, symbols)
            elif method == OptimizationMethod.ENSEMBLE:
                result = await self._optimize_with_ensemble(data, symbols)
            elif method == OptimizationMethod.HYBRID:
                result = await self._optimize_with_hybrid(data, symbols)
            else:
                raise ValueError(f"Unknown optimization method: {method}")
            
            # Update optimization history
            optimization_time = (datetime.now() - start_time).total_seconds()
            result.optimization_time = optimization_time
            
            await self._store_optimization_result(result)
            
            self.logger.info(f"✅ Weight optimization completed in {optimization_time:.2f}s")
            return result
            
        except Exception as e:
            self.logger.error(f"❌ Error in weight optimization: {e}")
            return self._get_default_result(method)
    
    async def _optimize_with_machine_learning(self, data: Dict[str, pd.DataFrame], symbols: List[str]) -> OptimizationResult:
        """Optimize weights using machine learning approach."""
        self.logger.info("🤖 Starting machine learning-based optimization")
        
        # Prepare features and targets
        features, targets = await self._prepare_ml_data(data, symbols)
        
        if features is None or targets is None:
            return self._get_default_result(OptimizationMethod.MACHINE_LEARNING)
        
        # Initialize models
        models = {
            'random_forest': RandomForestRegressor(n_estimators=100, random_state=42),
            'gradient_boosting': GradientBoostingRegressor(n_estimators=100, random_state=42),
            'ridge': Ridge(alpha=1.0)
        }
        
        best_weights = {}
        best_performance = -np.inf
        feature_importance = {}
        
        # Train models and extract feature importance
        for model_name, model in models.items():
            self.logger.info(f"Training {model_name} model...")
            
            # Time series cross-validation
            tscv = TimeSeriesSplit(n_splits=5)
            scores = []
            
            for train_idx, val_idx in tscv.split(features):
                X_train, X_val = features.iloc[train_idx], features.iloc[val_idx]
                y_train, y_val = targets.iloc[train_idx], targets.iloc[val_idx]
                
                model.fit(X_train, y_train)
                y_pred = model.predict(X_val)
                score = r2_score(y_val, y_pred)
                scores.append(score)
            
            avg_score = np.mean(scores)
            
            if avg_score > best_performance:
                best_performance = avg_score
                
                # Extract feature importance
                if hasattr(model, 'feature_importances_'):
                    feature_importance = dict(zip(features.columns, model.feature_importances_))
                elif hasattr(model, 'coef_'):
                    feature_importance = dict(zip(features.columns, np.abs(model.coef_)))
                
                # Convert feature importance to weights
                best_weights = self._convert_importance_to_weights(feature_importance)
        
        return OptimizationResult(
            method=OptimizationMethod.MACHINE_LEARNING,
            optimized_weights=best_weights,
            performance_metrics={'r2_score': best_performance},
            feature_importance=feature_importance,
            optimization_time=0.0,
            iterations=len(models),
            convergence=True,
            metadata={'models_tested': list(models.keys())}
        )
    
    async def _optimize_with_backtesting(self, data: Dict[str, pd.DataFrame], symbols: List[str]) -> OptimizationResult:
        """Optimize weights using backtesting approach."""
        self.logger.info("📊 Starting backtesting-based optimization")
        
        # Define weight ranges to test
        weight_ranges = {
            'trend_weight': np.linspace(0.5, 2.0, 10),
            'momentum_weight': np.linspace(0.5, 2.0, 10),
            'volatility_weight': np.linspace(0.3, 1.5, 8),
            'conflict_reduction': np.linspace(0.3, 0.9, 7)
        }
        
        best_config = None
        best_metrics = None
        best_sharpe = -np.inf
        
        total_combinations = len(weight_ranges['trend_weight']) * len(weight_ranges['momentum_weight']) * \
                           len(weight_ranges['volatility_weight']) * len(weight_ranges['conflict_reduction'])
        
        self.logger.info(f"Testing {total_combinations} weight combinations...")
        
        for trend_weight in weight_ranges['trend_weight']:
            for momentum_weight in weight_ranges['momentum_weight']:
                for volatility_weight in weight_ranges['volatility_weight']:
                    for conflict_reduction in weight_ranges['conflict_reduction']:
                        
                        weight_config = WeightConfig(
                            name=f"opt_{trend_weight}_{momentum_weight}_{volatility_weight}_{conflict_reduction}",
                            trend_weight=trend_weight,
                            momentum_weight=momentum_weight,
                            volatility_weight=volatility_weight,
                            conflict_reduction=conflict_reduction
                        )
                        
                        # Run backtest
                        try:
                            metrics = self.backtester._backtest_technical_only(data, weight_config)
                            
                            if metrics.sharpe_ratio > best_sharpe:
                                best_sharpe = metrics.sharpe_ratio
                                best_config = weight_config
                                best_metrics = metrics
                                
                        except Exception as e:
                            self.logger.warning(f"Backtest failed for config {weight_config.name}: {e}")
                            continue
        
        if best_config is None:
            return self._get_default_result(OptimizationMethod.BACKTESTING)
        
        # Convert best config to weights
        optimized_weights = self._convert_config_to_weights(best_config)
        
        return OptimizationResult(
            method=OptimizationMethod.BACKTESTING,
            optimized_weights=optimized_weights,
            performance_metrics={
                'sharpe_ratio': best_metrics.sharpe_ratio,
                'total_return': best_metrics.total_return,
                'max_drawdown': best_metrics.max_drawdown,
                'win_rate': best_metrics.win_rate
            },
            feature_importance={},
            optimization_time=0.0,
            iterations=total_combinations,
            convergence=True,
            metadata={'best_config': best_config.__dict__}
        )
    
    async def _optimize_with_adaptive_learning(self, data: Dict[str, pd.DataFrame], symbols: List[str]) -> OptimizationResult:
        """Optimize weights using adaptive learning approach."""
        self.logger.info("🧠 Starting adaptive learning-based optimization")
        
        # Use the existing adaptive learning service
        learning_results = {}
        
        for symbol in symbols:
            if symbol in data:
                symbol_data = data[symbol]
                
                # Prepare features for adaptive learning
                features = await self.adaptive_learning._prepare_features(
                    symbol, symbol_data, None, None
                )
                
                if features is not None:
                    # Learn from data
                    learning_result = await self.adaptive_learning.learn_from_data(
                        symbol, symbol_data, None, None
                    )
                    learning_results[symbol] = learning_result
        
        # Aggregate results across symbols
        aggregated_weights = self._aggregate_adaptive_weights(learning_results)
        
        return OptimizationResult(
            method=OptimizationMethod.ADAPTIVE_LEARNING,
            optimized_weights=aggregated_weights,
            performance_metrics={'learning_completed': len(learning_results)},
            feature_importance={},
            optimization_time=0.0,
            iterations=len(symbols),
            convergence=True,
            metadata={'learning_results': learning_results}
        )
    
    async def _optimize_with_genetic_algorithm(self, data: Dict[str, pd.DataFrame], symbols: List[str]) -> OptimizationResult:
        """Optimize weights using genetic algorithm approach."""
        self.logger.info("🧬 Starting genetic algorithm optimization")
        
        # Define objective function for genetic algorithm
        def objective_function(weights):
            try:
                # Convert weights array to weight config
                weight_config = WeightConfig(
                    name="genetic_opt",
                    trend_weight=weights[0],
                    momentum_weight=weights[1],
                    volatility_weight=weights[2],
                    conflict_reduction=weights[3]
                )
                
                # Run backtest
                metrics = self.backtester._backtest_technical_only(data, weight_config)
                
                # Return negative Sharpe ratio (minimize negative = maximize positive)
                return -metrics.sharpe_ratio
                
            except Exception:
                return 0.0
        
        # Define bounds for weights
        bounds = [
            (0.5, 2.0),  # trend_weight
            (0.5, 2.0),  # momentum_weight
            (0.3, 1.5),  # volatility_weight
            (0.3, 0.9)   # conflict_reduction
        ]
        
        # Run genetic algorithm
        result = differential_evolution(
            objective_function,
            bounds,
            maxiter=50,
            popsize=15,
            seed=42
        )
        
        if result.success:
            optimized_weights = self._convert_genetic_result_to_weights(result.x)
            
            return OptimizationResult(
                method=OptimizationMethod.GENETIC_ALGORITHM,
                optimized_weights=optimized_weights,
                performance_metrics={'sharpe_ratio': -result.fun},
                feature_importance={},
                optimization_time=0.0,
                iterations=result.nit,
                convergence=result.success,
                metadata={'genetic_result': result.__dict__}
            )
        else:
            return self._get_default_result(OptimizationMethod.GENETIC_ALGORITHM)
    
    async def _optimize_with_bayesian_optimization(self, data: Dict[str, pd.DataFrame], symbols: List[str]) -> OptimizationResult:
        """Optimize weights using Bayesian optimization approach."""
        self.logger.info("🔮 Starting Bayesian optimization")
        
        def objective(trial):
            # Define hyperparameters
            trend_weight = trial.suggest_float('trend_weight', 0.5, 2.0)
            momentum_weight = trial.suggest_float('momentum_weight', 0.5, 2.0)
            volatility_weight = trial.suggest_float('volatility_weight', 0.3, 1.5)
            conflict_reduction = trial.suggest_float('conflict_reduction', 0.3, 0.9)
            
            try:
                weight_config = WeightConfig(
                    name="bayesian_opt",
                    trend_weight=trend_weight,
                    momentum_weight=momentum_weight,
                    volatility_weight=volatility_weight,
                    conflict_reduction=conflict_reduction
                )
                
                metrics = self.backtester._backtest_technical_only(data, weight_config)
                return metrics.sharpe_ratio
                
            except Exception:
                return 0.0
        
        # Run Bayesian optimization
        study = optuna.create_study(direction='maximize')
        study.optimize(objective, n_trials=100)
        
        if study.best_params:
            optimized_weights = self._convert_bayesian_result_to_weights(study.best_params)
            
            return OptimizationResult(
                method=OptimizationMethod.BAYESIAN_OPTIMIZATION,
                optimized_weights=optimized_weights,
                performance_metrics={'sharpe_ratio': study.best_value},
                feature_importance={},
                optimization_time=0.0,
                iterations=len(study.trials),
                convergence=True,
                metadata={'study_best_params': study.best_params}
            )
        else:
            return self._get_default_result(OptimizationMethod.BAYESIAN_OPTIMIZATION)
    
    async def _optimize_with_ensemble(self, data: Dict[str, pd.DataFrame], symbols: List[str]) -> OptimizationResult:
        """Optimize weights using ensemble approach."""
        self.logger.info("🎯 Starting ensemble-based optimization")
        
        # Run multiple optimization methods
        methods = [
            OptimizationMethod.MACHINE_LEARNING,
            OptimizationMethod.BACKTESTING,
            OptimizationMethod.ADAPTIVE_LEARNING
        ]
        
        results = {}
        for method in methods:
            try:
                result = await self._run_single_optimization(data, symbols, method)
                results[method] = result
            except Exception as e:
                self.logger.warning(f"Method {method.value} failed: {e}")
        
        # Ensemble the results
        ensemble_weights = self._ensemble_weights(results)
        
        return OptimizationResult(
            method=OptimizationMethod.ENSEMBLE,
            optimized_weights=ensemble_weights,
            performance_metrics={'methods_used': len(results)},
            feature_importance={},
            optimization_time=0.0,
            iterations=len(results),
            convergence=True,
            metadata={'ensemble_results': results}
        )
    
    async def _optimize_with_hybrid(self, data: Dict[str, pd.DataFrame], symbols: List[str]) -> OptimizationResult:
        """Optimize weights using hybrid approach combining multiple methods."""
        self.logger.info("🔄 Starting hybrid optimization")
        
        # Run multiple optimization methods
        methods = [
            OptimizationMethod.MACHINE_LEARNING,
            OptimizationMethod.BACKTESTING,
            OptimizationMethod.ADAPTIVE_LEARNING,
            OptimizationMethod.GENETIC_ALGORITHM
        ]
        
        results = {}
        for method in methods:
            try:
                result = await self._run_single_optimization(data, symbols, method)
                results[method] = result
            except Exception as e:
                self.logger.warning(f"Method {method.value} failed: {e}")
        
        # Combine results using weighted average
        hybrid_weights = self._combine_hybrid_weights(results)
        
        return OptimizationResult(
            method=OptimizationMethod.HYBRID,
            optimized_weights=hybrid_weights,
            performance_metrics={'methods_used': len(results)},
            feature_importance={},
            optimization_time=0.0,
            iterations=len(results),
            convergence=True,
            metadata={'hybrid_results': results}
        )
    
    async def _run_single_optimization(self, data: Dict[str, pd.DataFrame], symbols: List[str], method: OptimizationMethod) -> OptimizationResult:
        """Run a single optimization method."""
        if method == OptimizationMethod.MACHINE_LEARNING:
            return await self._optimize_with_machine_learning(data, symbols)
        elif method == OptimizationMethod.BACKTESTING:
            return await self._optimize_with_backtesting(data, symbols)
        elif method == OptimizationMethod.ADAPTIVE_LEARNING:
            return await self._optimize_with_adaptive_learning(data, symbols)
        elif method == OptimizationMethod.GENETIC_ALGORITHM:
            return await self._optimize_with_genetic_algorithm(data, symbols)
        elif method == OptimizationMethod.BAYESIAN_OPTIMIZATION:
            return await self._optimize_with_bayesian_optimization(data, symbols)
        else:
            raise ValueError(f"Unknown method: {method}")
    
    async def _prepare_ml_data(self, data: Dict[str, pd.DataFrame], symbols: List[str]) -> Tuple[Optional[pd.DataFrame], Optional[pd.Series]]:
        """Prepare data for machine learning optimization."""
        features_list = []
        targets_list = []
        
        for symbol in symbols:
            if symbol not in data:
                continue
            
            symbol_data = data[symbol]
            if len(symbol_data) < self.min_data_points:
                continue
            
            # Calculate indicators
            indicators = self.indicators.calculate_all_indicators(symbol_data)
            
            # Extract features
            symbol_features = self._extract_indicator_features(indicators)
            if symbol_features is not None:
                features_list.append(symbol_features)
                
                # Calculate target (future returns)
                future_returns = symbol_data['Close'].pct_change().shift(-1)
                targets_list.append(future_returns)
        
        if not features_list:
            return None, None
        
        # Combine features and targets
        features = pd.concat(features_list, axis=0)
        targets = pd.concat(targets_list, axis=0)
        
        # Align features and targets
        features, targets = features.align(targets, join='inner')
        
        return features, targets
    
    def _extract_indicator_features(self, indicators: Dict[str, IndicatorResult]) -> Optional[pd.DataFrame]:
        """Extract features from indicators."""
        features = {}
        
        for name, result in indicators.items():
            if result.value is not None:
                features[f'{name}_value'] = [result.value]
                features[f'{name}_confidence'] = [result.confidence if result.confidence else 0.0]
                
                if result.signal:
                    signal_value = self._encode_signal(result.signal)
                    features[f'{name}_signal'] = [signal_value]
        
        if not features:
            return None
        
        return pd.DataFrame(features)
    
    def _encode_signal(self, signal: SignalType) -> float:
        """Encode signal to numerical value."""
        signal_encoding = {
            SignalType.STRONG_SELL: -1.0,
            SignalType.SELL: -0.5,
            SignalType.HOLD: 0.0,
            SignalType.BUY: 0.5,
            SignalType.STRONG_BUY: 1.0
        }
        return signal_encoding.get(signal, 0.0)
    
    def _convert_importance_to_weights(self, feature_importance: Dict[str, float]) -> Dict[str, float]:
        """Convert feature importance to indicator weights."""
        weights = {}
        
        for feature, importance in feature_importance.items():
            # Extract indicator name from feature
            if '_value' in feature:
                indicator_name = feature.replace('_value', '')
                weights[indicator_name] = importance
            elif '_signal' in feature:
                indicator_name = feature.replace('_signal', '')
                if indicator_name not in weights:
                    weights[indicator_name] = importance
        
        # Normalize weights
        if weights:
            max_weight = max(weights.values())
            if max_weight > 0:
                weights = {k: v / max_weight for k, v in weights.items()}
        
        return weights
    
    def _convert_config_to_weights(self, config: WeightConfig) -> Dict[str, float]:
        """Convert weight config to indicator weights."""
        weights = {}
        
        # Map config weights to indicators
        trend_indicators = ['sma', 'ema', 'macd', 'vwap']
        momentum_indicators = ['rsi', 'stochastic', 'williams_r', 'cci', 'money_flow_index']
        volatility_indicators = ['bollinger_bands', 'atr', 'keltner_channels']
        
        for indicator in trend_indicators:
            weights[indicator] = config.trend_weight
        
        for indicator in momentum_indicators:
            weights[indicator] = config.momentum_weight
        
        for indicator in volatility_indicators:
            weights[indicator] = config.volatility_weight
        
        return weights
    
    def _convert_genetic_result_to_weights(self, genetic_result: np.ndarray) -> Dict[str, float]:
        """Convert genetic algorithm result to weights."""
        config = WeightConfig(
            name="genetic_result",
            trend_weight=genetic_result[0],
            momentum_weight=genetic_result[1],
            volatility_weight=genetic_result[2],
            conflict_reduction=genetic_result[3]
        )
        return self._convert_config_to_weights(config)
    
    def _convert_bayesian_result_to_weights(self, bayesian_params: Dict[str, float]) -> Dict[str, float]:
        """Convert Bayesian optimization result to weights."""
        config = WeightConfig(
            name="bayesian_result",
            trend_weight=bayesian_params['trend_weight'],
            momentum_weight=bayesian_params['momentum_weight'],
            volatility_weight=bayesian_params['volatility_weight'],
            conflict_reduction=bayesian_params['conflict_reduction']
        )
        return self._convert_config_to_weights(config)
    
    def _aggregate_adaptive_weights(self, learning_results: Dict[str, Any]) -> Dict[str, float]:
        """Aggregate weights from adaptive learning results."""
        aggregated_weights = {}
        
        for symbol, result in learning_results.items():
            if hasattr(result, 'weights'):
                for weight_name, weight_obj in result.weights.items():
                    if weight_name not in aggregated_weights:
                        aggregated_weights[weight_name] = []
                    aggregated_weights[weight_name].append(weight_obj.current_weight)
        
        # Average the weights
        final_weights = {}
        for weight_name, weight_values in aggregated_weights.items():
            if weight_values:
                final_weights[weight_name] = np.mean(weight_values)
        
        return final_weights
    
    def _ensemble_weights(self, results: Dict[OptimizationMethod, OptimizationResult]) -> Dict[str, float]:
        """Ensemble weights from multiple optimization methods."""
        all_weights = {}
        
        for method, result in results.items():
            for indicator, weight in result.optimized_weights.items():
                if indicator not in all_weights:
                    all_weights[indicator] = []
                all_weights[indicator].append(weight)
        
        # Average the weights
        ensemble_weights = {}
        for indicator, weight_values in all_weights.items():
            if weight_values:
                ensemble_weights[indicator] = np.mean(weight_values)
        
        return ensemble_weights
    
    def _combine_hybrid_weights(self, results: Dict[OptimizationMethod, OptimizationResult]) -> Dict[str, float]:
        """Combine weights from hybrid optimization."""
        # Weight the methods based on their performance
        method_weights = {
            OptimizationMethod.MACHINE_LEARNING: 0.3,
            OptimizationMethod.BACKTESTING: 0.4,
            OptimizationMethod.ADAPTIVE_LEARNING: 0.2,
            OptimizationMethod.GENETIC_ALGORITHM: 0.1
        }
        
        hybrid_weights = {}
        
        for method, result in results.items():
            weight_multiplier = method_weights.get(method, 0.1)
            
            for indicator, weight in result.optimized_weights.items():
                if indicator not in hybrid_weights:
                    hybrid_weights[indicator] = 0.0
                hybrid_weights[indicator] += weight * weight_multiplier
        
        return hybrid_weights
    
    async def _load_historical_data(self, symbols: List[str], start_date: str, end_date: str) -> Dict[str, pd.DataFrame]:
        """Load historical data for optimization."""
        data = {}
        
        for symbol in symbols:
            try:
                # Use yfinance to load data
                import yfinance as yf
                ticker = yf.Ticker(symbol)
                symbol_data = ticker.history(start=start_date, end=end_date)
                
                if not symbol_data.empty:
                    data[symbol] = symbol_data
                    self.logger.info(f"Loaded {len(symbol_data)} data points for {symbol}")
                else:
                    self.logger.warning(f"No data found for {symbol}")
                    
            except Exception as e:
                self.logger.error(f"Error loading data for {symbol}: {e}")
        
        return data
    
    async def _store_optimization_result(self, result: OptimizationResult):
        """Store optimization result."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"optimization_result_{result.method.value}_{timestamp}.json"
        filepath = self.data_dir / filename
        
        # Convert result to JSON-serializable format
        result_dict = {
            'method': result.method.value,
            'optimized_weights': result.optimized_weights,
            'performance_metrics': result.performance_metrics,
            'feature_importance': result.feature_importance,
            'optimization_time': result.optimization_time,
            'iterations': result.iterations,
            'convergence': result.convergence,
            'metadata': result.metadata,
            'timestamp': timestamp
        }
        
        with open(filepath, 'w') as f:
            json.dump(result_dict, f, indent=2, default=str)
        
        self.logger.info(f"💾 Optimization result stored: {filepath}")
    
    def _get_default_result(self, method: OptimizationMethod) -> OptimizationResult:
        """Get default optimization result."""
        return OptimizationResult(
            method=method,
            optimized_weights={},
            performance_metrics={},
            feature_importance={},
            optimization_time=0.0,
            iterations=0,
            convergence=False,
            metadata={'error': 'Optimization failed'}
        )
    
    def get_current_weights(self) -> Dict[str, float]:
        """Get current indicator weights."""
        return {name: weight.current_weight for name, weight in self.indicator_weights.items()}
    
    def update_weights(self, new_weights: Dict[str, float]):
        """Update indicator weights."""
        for name, weight in new_weights.items():
            if name in self.indicator_weights:
                self.indicator_weights[name].update_weight(weight)
    
    def get_optimization_history(self) -> List[Dict]:
        """Get optimization history."""
        return self.optimization_history
    
    async def close(self):
        """Close the optimizer and cleanup resources."""
        self.logger.info("🔒 Closing Indicator Weight Optimizer")
        await self.adaptive_learning.close()


