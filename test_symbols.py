#!/usr/bin/env python3
"""
Test script for AAPL, MSFT, and GOOG symbols.
Tests market data retrieval, technical indicators, and analysis.
"""

import sys
import os
sys.path.insert(0, 'src')

from utils.config import get_config
from utils.logging import setup_logging
from services.market_data import MarketDataService
from services.indicators import TechnicalIndicators
# from services.signals import SignalGenerator  # Not used in this test
import asyncio

async def test_symbol_analysis(symbol: str):
    """Test comprehensive analysis for a single symbol."""
    print(f"\n🔍 Testing {symbol}")
    print("=" * 40)
    
    # Get configuration
    config = get_config()
    config_dict = config.model_dump()
    
    # Initialize services
    market_data = MarketDataService(config_dict)
    indicators = TechnicalIndicators(config_dict)
    
    try:
        # Test 1: Market Data Retrieval
        print(f"📊 Fetching market data for {symbol}...")
        data = market_data.get_stock_data(symbol, interval='1d', days=60)
        
        if data is None or data.empty:
            print(f"❌ No market data available for {symbol}")
            return False
        
        print(f"✅ Market data retrieved: {len(data)} data points")
        print(f"   Latest price: ${data['Close'].iloc[-1]:.2f}")
        print(f"   Date range: {data.index[0].date()} to {data.index[-1].date()}")
        
        # Test 2: Technical Indicators
        print(f"📈 Calculating technical indicators for {symbol}...")
        indicator_results = indicators.calculate_all_indicators(data)
        
        if not indicator_results:
            print(f"❌ No indicators calculated for {symbol}")
            return False
        
        print(f"✅ Technical indicators calculated: {len(indicator_results)} indicators")
        
        # Display key indicators
        for indicator_name, result in indicator_results.items():
            if hasattr(result, 'iloc') and len(result) > 0:
                latest_value = result.iloc[-1]
                if not pd.isna(latest_value):
                    print(f"   {indicator_name}: {latest_value:.2f}")
        
        # Test 3: Display Key Indicators
        print(f"🎯 Key indicators for {symbol}:")
        key_indicators = ['rsi', 'macd', 'sma', 'ema', 'bollinger_bands']
        for indicator_name in key_indicators:
            if indicator_name in indicator_results:
                result = indicator_results[indicator_name]
                if result.value is not None:
                    print(f"   {indicator_name.upper()}: {result.value:.2f} ({result.signal.value}, confidence: {result.confidence:.2f})")
                else:
                    print(f"   {indicator_name.upper()}: N/A")

        print(f"✅ {symbol} analysis completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error testing {symbol}: {e}")
        return False

async def main():
    """Run tests for AAPL, MSFT, and GOOG."""
    print("🚀 AI-Nvestor Symbol Testing")
    print("Testing AAPL, MSFT, and GOOG")
    print("=" * 50)
    
    # Import pandas here to avoid issues
    global pd
    import pandas as pd
    
    symbols = ["AAPL", "MSFT", "GOOG"]
    results = {}
    
    for symbol in symbols:
        success = await test_symbol_analysis(symbol)
        results[symbol] = success
    
    # Summary
    print("\n📊 Test Summary")
    print("=" * 30)
    passed = sum(results.values())
    total = len(results)
    
    for symbol, success in results.items():
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{symbol}: {status}")
    
    print(f"\nOverall: {passed}/{total} symbols tested successfully")
    
    if passed == total:
        print("🎉 All tests passed!")
    else:
        print("⚠️ Some tests failed. Check the output above for details.")

if __name__ == "__main__":
    asyncio.run(main())
