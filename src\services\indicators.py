"""
Advanced technical indicators service for AI-Nvestor.

This module provides:
- Comprehensive technical indicator calculations
- Multiple timeframe analysis
- Signal generation and validation
- Performance optimization with vectorized operations
- Real-time indicator updates
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Union
from dataclasses import dataclass
import ta
from numba import jit

from constants.settings import SignalType


@dataclass
class IndicatorResult:
    """Result of technical indicator calculation."""
    value: Optional[float]  # Can be None for undefined values
    signal: SignalType
    confidence: Optional[float]  # Can be None for undefined confidence
    metadata: Dict[str, Union[float, str]]


class TechnicalIndicators:
    """
    Advanced technical indicators calculator.
    
    Features:
    - Vectorized calculations for performance
    - Multiple timeframe support
    - Signal generation with confidence levels
    - Real-time updates
    - Comprehensive indicator library
    """
    
    def __init__(self, config: Dict):
        """
        Initialize the technical indicators calculator.
        
        Args:
            config: Configuration dictionary or Pydantic model with indicator parameters
        """
        self.config = config
        
        # Handle both dict and Pydantic model
        if hasattr(config, 'indicators'):
            # Pydantic model
            self.indicators_config = config.indicators
        else:
            # Dictionary
            self.indicators_config = config.get('indicators', {})
        
    def calculate_all_indicators(self, data: pd.DataFrame) -> Dict[str, IndicatorResult]:
        """
        Calculate all technical indicators for the given data.
        
        Args:
            data: OHLCV DataFrame
            
        Returns:
            Dictionary of indicator results
        """
        results = {}
        
        # Validate input data
        if not self._validate_data(data):
            return results
        
        try:
            # Trend indicators
            results['rsi'] = self.calculate_rsi(data)
            results['macd'] = self.calculate_macd(data)
            results['sma'] = self.calculate_sma(data)
            results['ema'] = self.calculate_ema(data)
            results['bollinger_bands'] = self.calculate_bollinger_bands(data)
            
            # Momentum indicators
            results['stochastic'] = self.calculate_stochastic(data)
            results['williams_r'] = self.calculate_williams_r(data)
            results['cci'] = self.calculate_cci(data)
            
            # Volatility indicators
            results['atr'] = self.calculate_atr(data)
            results['keltner_channels'] = self.calculate_keltner_channels(data)
            
            # Volume indicators
            results['obv'] = self.calculate_obv(data)
            results['vwap'] = self.calculate_vwap(data)
            results['money_flow_index'] = self.calculate_money_flow_index(data)
            
        except Exception as e:
            # Log error and return empty results
            import logging
            logging.error(f"Error calculating indicators: {e}")
            
        return results
    
    def _validate_data(self, data: pd.DataFrame) -> bool:
        """Validate input data for indicator calculations."""
        try:
            if data is None or data.empty:
                return False
            
            # Check for required columns
            required_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
            if not all(col in data.columns for col in required_columns):
                return False
            
            # Check for sufficient data points
            if len(data) < 30:  # Need minimum data for indicators
                return False
            
            # Check for reasonable values
            if (data['High'] < data['Low']).any():
                return False
            
            if (data['Open'] < 0).any() or (data['Close'] < 0).any():
                return False
            
            # Check for NaN values
            if data.isnull().any().any():
                return False
            
            return True
            
        except Exception:
            return False
    
    def _get_default_result(self, error_message: str) -> IndicatorResult:
        """Get default indicator result when calculation fails."""
        return IndicatorResult(
            value=None,  # N/A instead of fake 0.0
            signal=SignalType.HOLD,
            confidence=None,  # N/A instead of fake 0.5
            metadata={'error': error_message, 'fallback': True}
        )
    
    def calculate_rsi(self, data: pd.DataFrame, period: Optional[int] = None) -> IndicatorResult:
        """
        Calculate Relative Strength Index with signal generation.
        
        Args:
            data: OHLCV DataFrame
            period: RSI period (default from config)
            
        Returns:
            RSI indicator result
        """
        try:
            if not self._validate_data(data):
                return self._get_default_result("RSI validation failed")
            
            # Handle both dict and Pydantic model
            if hasattr(self.indicators_config, 'rsi_period'):
                # Pydantic model
                period = period or self.indicators_config.rsi_period
                overbought = self.indicators_config.rsi_overbought
                oversold = self.indicators_config.rsi_oversold
            else:
                # Dictionary
                period = period or self.indicators_config.get('rsi_period', 14)
                overbought = self.indicators_config.get('rsi_overbought', 70)
                oversold = self.indicators_config.get('rsi_oversold', 30)
            
            # Calculate RSI using ta library for efficiency
            rsi_values = ta.momentum.RSIIndicator(
                close=data['Close'], 
                window=period
            ).rsi()
            
            # Handle NaN values
            if rsi_values.isnull().all():
                return self._get_default_result("RSI calculation failed - all NaN values")
            
            current_rsi = rsi_values.iloc[-1]
            
            # Handle NaN or infinite values
            if pd.isna(current_rsi) or np.isinf(current_rsi):
                return self._get_default_result("RSI calculation failed - invalid value")
            
            # Generate signal
            if current_rsi < oversold:
                signal = SignalType.STRONG_BUY
                confidence = min(0.9, max(0.1, (oversold - current_rsi) / oversold))
            elif current_rsi < 50:
                signal = SignalType.BUY
                confidence = 0.6
            elif current_rsi > overbought:
                signal = SignalType.STRONG_SELL
                confidence = min(0.9, max(0.1, (current_rsi - overbought) / (100 - overbought)))
            elif current_rsi > 50:
                signal = SignalType.SELL
                confidence = 0.6
            else:
                signal = SignalType.HOLD
                confidence = 0.5
                
            return IndicatorResult(
                value=current_rsi,
                signal=signal,
                confidence=confidence,
                metadata={
                    'period': period,
                    'overbought': overbought,
                    'oversold': oversold,
                    'series': rsi_values
                }
            )
            
        except Exception as e:
            import logging
            logging.error(f"Error calculating RSI: {e}")
            return self._get_default_result(f"⚠️ FALLBACK: RSI calculation error: {str(e)}")
    
    def calculate_macd(self, data: pd.DataFrame) -> IndicatorResult:
        """
        Calculate MACD with signal generation.
        
        Args:
            data: OHLCV DataFrame
            
        Returns:
            MACD indicator result
        """
        try:
            if not self._validate_data(data):
                return self._get_default_result("MACD validation failed")
            
            # Handle both dict and Pydantic model
            if hasattr(self.indicators_config, 'macd'):
                # Pydantic model
                macd_config = self.indicators_config.macd
                fast_period = macd_config.get('fast_period', 12)
                slow_period = macd_config.get('slow_period', 26)
                signal_period = macd_config.get('signal_period', 9)
            else:
                # Dictionary
                macd_config = self.indicators_config.get('macd', {})
                fast_period = macd_config.get('fast_period', 12)
                slow_period = macd_config.get('slow_period', 26)
                signal_period = macd_config.get('signal_period', 9)
            
            # Calculate MACD using ta library
            macd_indicator = ta.trend.MACD(
                close=data['Close'],
                window_fast=fast_period,
                window_slow=slow_period,
                window_sign=signal_period
            )
            
            macd_line = macd_indicator.macd()
            signal_line = macd_indicator.macd_signal()
            histogram = macd_indicator.macd_diff()
            
            # Handle NaN values
            if macd_line.isnull().all() or signal_line.isnull().all():
                return self._get_default_result("MACD calculation failed - all NaN values")
            
            current_macd = macd_line.iloc[-1]
            current_signal = signal_line.iloc[-1]
            current_histogram = histogram.iloc[-1]
            
            # Handle NaN or infinite values
            if (pd.isna(current_macd) or np.isinf(current_macd) or 
                pd.isna(current_signal) or np.isinf(current_signal)):
                return self._get_default_result("MACD calculation failed - invalid values")
            
            # Generate signal based on MACD crossover
            if current_macd > current_signal and current_histogram > 0:
                signal = SignalType.BUY
                # Avoid division by zero
                confidence = min(0.8, abs(current_histogram) / max(abs(current_macd), 0.001))
            elif current_macd < current_signal and current_histogram < 0:
                signal = SignalType.SELL
                confidence = min(0.8, abs(current_histogram) / max(abs(current_macd), 0.001))
            else:
                signal = SignalType.HOLD
                confidence = 0.5
                
            return IndicatorResult(
                value=current_macd,
                signal=signal,
                confidence=confidence,
                metadata={
                    'signal_line': current_signal,
                    'histogram': current_histogram,
                    'fast_period': fast_period,
                    'slow_period': slow_period,
                    'signal_period': signal_period,
                    'macd_line': macd_line,
                    'signal_series': signal_line,
                    'histogram_series': histogram
                }
            )
            
        except Exception as e:
            import logging
            logging.error(f"Error calculating MACD: {e}")
            return self._get_default_result(f"MACD calculation error: {str(e)}")
    
    def calculate_bollinger_bands(self, data: pd.DataFrame) -> IndicatorResult:
        """
        Calculate Bollinger Bands with signal generation.
        
        Args:
            data: OHLCV DataFrame
            
        Returns:
            Bollinger Bands indicator result
        """
        try:
            if not self._validate_data(data):
                return self._get_default_result("Bollinger Bands validation failed")
            
            # Handle both dict and Pydantic model
            if hasattr(self.indicators_config, 'bollinger_bands'):
                # Pydantic model
                bb_config = self.indicators_config.bollinger_bands
                period = bb_config.get('period', 20)
                std_dev = bb_config.get('std_dev', 2)
            else:
                # Dictionary
                bb_config = self.indicators_config.get('bollinger_bands', {})
                period = bb_config.get('period', 20)
                std_dev = bb_config.get('std_dev', 2)
            
            # Calculate Bollinger Bands
            bb_indicator = ta.volatility.BollingerBands(
                close=data['Close'],
                window=period,
                window_dev=std_dev
            )
            
            upper_band = bb_indicator.bollinger_hband()
            middle_band = bb_indicator.bollinger_mavg()
            lower_band = bb_indicator.bollinger_lband()
            
            # Handle NaN values
            if upper_band.isnull().all() or middle_band.isnull().all() or lower_band.isnull().all():
                return self._get_default_result("Bollinger Bands calculation failed - all NaN values")
            
            current_price = data['Close'].iloc[-1]
            current_upper = upper_band.iloc[-1]
            current_lower = lower_band.iloc[-1]
            current_middle = middle_band.iloc[-1]
            
            # Handle NaN or infinite values
            if (pd.isna(current_price) or np.isinf(current_price) or 
                pd.isna(current_upper) or np.isinf(current_upper) or
                pd.isna(current_lower) or np.isinf(current_lower) or
                pd.isna(current_middle) or np.isinf(current_middle)):
                return self._get_default_result("Bollinger Bands calculation failed - invalid values")
            
            # Calculate position within bands
            band_width = current_upper - current_lower
            if band_width <= 0:
                position = 0.5
            else:
                position = (current_price - current_lower) / band_width
            
            # Generate signal
            if current_price <= current_lower:
                signal = SignalType.STRONG_BUY
                confidence = 0.8
            elif current_price <= current_middle:
                signal = SignalType.BUY
                confidence = 0.6
            elif current_price >= current_upper:
                signal = SignalType.STRONG_SELL
                confidence = 0.8
            elif current_price >= current_middle:
                signal = SignalType.SELL
                confidence = 0.6
            else:
                signal = SignalType.HOLD
                confidence = 0.5
                
            return IndicatorResult(
                value=position,
                signal=signal,
                confidence=confidence,
                metadata={
                    'upper_band': current_upper,
                    'middle_band': current_middle,
                    'lower_band': current_lower,
                    'period': period,
                    'std_dev': std_dev,
                    'upper_series': upper_band,
                    'middle_series': middle_band,
                    'lower_series': lower_band
                }
            )
            
        except Exception as e:
            import logging
            logging.error(f"Error calculating Bollinger Bands: {e}")
            return self._get_default_result(f"Bollinger Bands calculation error: {str(e)}")
    
    def calculate_stochastic(self, data: pd.DataFrame) -> IndicatorResult:
        """
        Calculate Stochastic Oscillator.
        
        Args:
            data: OHLCV DataFrame
            
        Returns:
            Stochastic indicator result
        """
        stoch_config = self.indicators_config.get('stochastic', {})
        k_period = stoch_config.get('k_period', 14)
        d_period = stoch_config.get('d_period', 3)
        
        # Calculate Stochastic
        stoch_indicator = ta.momentum.StochasticOscillator(
            high=data['High'],
            low=data['Low'],
            close=data['Close'],
            window=k_period,
            smooth_window=d_period
        )
        
        k_percent = stoch_indicator.stoch()
        d_percent = stoch_indicator.stoch_signal()
        
        current_k = k_percent.iloc[-1]
        current_d = d_percent.iloc[-1]
        
        # Generate signal
        if current_k < 20 and current_d < 20:
            signal = SignalType.STRONG_BUY
            confidence = 0.8
        elif current_k < current_d and current_k < 50:
            signal = SignalType.BUY
            confidence = 0.6
        elif current_k > 80 and current_d > 80:
            signal = SignalType.STRONG_SELL
            confidence = 0.8
        elif current_k > current_d and current_k > 50:
            signal = SignalType.SELL
            confidence = 0.6
        else:
            signal = SignalType.HOLD
            confidence = 0.5
            
        return IndicatorResult(
            value=current_k,
            signal=signal,
            confidence=confidence,
            metadata={
                'k_percent': current_k,
                'd_percent': current_d,
                'k_period': k_period,
                'd_period': d_period,
                'k_series': k_percent,
                'd_series': d_percent
            }
        )
    
    def calculate_sma(self, data: pd.DataFrame, period: int = 20) -> IndicatorResult:
        """Calculate Simple Moving Average."""
        sma = ta.trend.SMAIndicator(close=data['Close'], window=period).sma_indicator()
        current_sma = sma.iloc[-1]
        current_price = data['Close'].iloc[-1]
        
        signal = SignalType.BUY if current_price > current_sma else SignalType.SELL
        confidence = min(0.7, abs(current_price - current_sma) / current_sma)
        
        return IndicatorResult(
            value=current_sma,
            signal=signal,
            confidence=confidence,
            metadata={'period': period, 'series': sma}
        )
    
    def calculate_ema(self, data: pd.DataFrame, period: int = 20) -> IndicatorResult:
        """Calculate Exponential Moving Average."""
        ema = ta.trend.EMAIndicator(close=data['Close'], window=period).ema_indicator()
        current_ema = ema.iloc[-1]
        current_price = data['Close'].iloc[-1]
        
        signal = SignalType.BUY if current_price > current_ema else SignalType.SELL
        confidence = min(0.7, abs(current_price - current_ema) / current_ema)
        
        return IndicatorResult(
            value=current_ema,
            signal=signal,
            confidence=confidence,
            metadata={'period': period, 'series': ema}
        )
    
    def calculate_atr(self, data: pd.DataFrame) -> IndicatorResult:
        """Calculate Average True Range."""
        period = self.indicators_config.get('atr_period', 14)
        atr = ta.volatility.AverageTrueRange(
            high=data['High'],
            low=data['Low'],
            close=data['Close'],
            window=period
        ).average_true_range()
        
        current_atr = atr.iloc[-1]
        
        return IndicatorResult(
            value=current_atr,
            signal=SignalType.HOLD,
            confidence=0.5,
            metadata={'period': period, 'series': atr}
        )
    
    def calculate_obv(self, data: pd.DataFrame) -> IndicatorResult:
        """Calculate On-Balance Volume."""
        obv = ta.volume.OnBalanceVolumeIndicator(
            close=data['Close'],
            volume=data['Volume']
        ).on_balance_volume()
        
        current_obv = obv.iloc[-1]
        
        return IndicatorResult(
            value=current_obv,
            signal=SignalType.HOLD,
            confidence=0.5,
            metadata={'series': obv}
        )
    
    def calculate_vwap(self, data: pd.DataFrame) -> IndicatorResult:
        """Calculate Volume Weighted Average Price."""
        vwap = ta.volume.VolumeWeightedAveragePrice(
            high=data['High'],
            low=data['Low'],
            close=data['Close'],
            volume=data['Volume']
        ).volume_weighted_average_price()
        
        current_vwap = vwap.iloc[-1]
        current_price = data['Close'].iloc[-1]
        
        signal = SignalType.BUY if current_price > current_vwap else SignalType.SELL
        confidence = min(0.6, abs(current_price - current_vwap) / current_vwap)
        
        return IndicatorResult(
            value=current_vwap,
            signal=signal,
            confidence=confidence,
            metadata={'series': vwap}
        )
    
    def calculate_williams_r(self, data: pd.DataFrame, period: int = 14) -> IndicatorResult:
        """Calculate Williams %R."""
        williams_r = ta.momentum.WilliamsRIndicator(
            high=data['High'],
            low=data['Low'],
            close=data['Close'],
            lbp=period
        ).williams_r()
        
        current_wr = williams_r.iloc[-1]
        
        if current_wr < -80:
            signal = SignalType.STRONG_BUY
            confidence = 0.8
        elif current_wr < -50:
            signal = SignalType.BUY
            confidence = 0.6
        elif current_wr > -20:
            signal = SignalType.STRONG_SELL
            confidence = 0.8
        elif current_wr > -50:
            signal = SignalType.SELL
            confidence = 0.6
        else:
            signal = SignalType.HOLD
            confidence = 0.5
            
        return IndicatorResult(
            value=current_wr,
            signal=signal,
            confidence=confidence,
            metadata={'period': period, 'series': williams_r}
        )
    
    def calculate_cci(self, data: pd.DataFrame, period: int = 20) -> IndicatorResult:
        """Calculate Commodity Channel Index."""
        cci = ta.trend.CCIIndicator(
            high=data['High'],
            low=data['Low'],
            close=data['Close'],
            window=period
        ).cci()
        
        current_cci = cci.iloc[-1]
        
        if current_cci > 100:
            signal = SignalType.STRONG_SELL
            confidence = 0.8
        elif current_cci > 0:
            signal = SignalType.SELL
            confidence = 0.6
        elif current_cci < -100:
            signal = SignalType.STRONG_BUY
            confidence = 0.8
        elif current_cci < 0:
            signal = SignalType.BUY
            confidence = 0.6
        else:
            signal = SignalType.HOLD
            confidence = 0.5
            
        return IndicatorResult(
            value=current_cci,
            signal=signal,
            confidence=confidence,
            metadata={'period': period, 'series': cci}
        )
    
    def calculate_keltner_channels(self, data: pd.DataFrame, period: int = 20) -> IndicatorResult:
        """Calculate Keltner Channels."""
        keltner = ta.volatility.KeltnerChannel(
            high=data['High'],
            low=data['Low'],
            close=data['Close'],
            window=period
        )
        
        upper = keltner.keltner_channel_hband()
        middle = keltner.keltner_channel_mband()
        lower = keltner.keltner_channel_lband()
        
        current_price = data['Close'].iloc[-1]
        current_upper = upper.iloc[-1]
        current_lower = lower.iloc[-1]
        
        if current_price <= current_lower:
            signal = SignalType.STRONG_BUY
            confidence = 0.8
        elif current_price >= current_upper:
            signal = SignalType.STRONG_SELL
            confidence = 0.8
        else:
            signal = SignalType.HOLD
            confidence = 0.5
            
        return IndicatorResult(
            value=current_price,
            signal=signal,
            confidence=confidence,
            metadata={
                'upper': current_upper,
                'middle': middle.iloc[-1],
                'lower': current_lower,
                'period': period,
                'upper_series': upper,
                'middle_series': middle,
                'lower_series': lower
            }
        )
    
    def calculate_money_flow_index(self, data: pd.DataFrame, period: int = 14) -> IndicatorResult:
        """Calculate Money Flow Index."""
        mfi = ta.volume.MFIIndicator(
            high=data['High'],
            low=data['Low'],
            close=data['Close'],
            volume=data['Volume'],
            window=period
        ).money_flow_index()
        
        current_mfi = mfi.iloc[-1]
        
        if current_mfi > 80:
            signal = SignalType.STRONG_SELL
            confidence = 0.8
        elif current_mfi > 50:
            signal = SignalType.SELL
            confidence = 0.6
        elif current_mfi < 20:
            signal = SignalType.STRONG_BUY
            confidence = 0.8
        elif current_mfi < 50:
            signal = SignalType.BUY
            confidence = 0.6
        else:
            signal = SignalType.HOLD
            confidence = 0.5
            
        return IndicatorResult(
            value=current_mfi,
            signal=signal,
            confidence=confidence,
            metadata={'period': period, 'series': mfi}
        )
    
    def generate_consensus_signal(self, indicators: Dict[str, IndicatorResult]) -> Tuple[SignalType, float]:
        """
        Generate consensus signal from multiple indicators with improved weighting.
        
        Args:
            indicators: Dictionary of indicator results
            
        Returns:
            Tuple of (consensus_signal, confidence)
        """
        try:
            if not indicators:
                return None, None  # N/A instead of fake 0.0
            
            buy_signals = 0
            sell_signals = 0
            total_confidence = 0
            signal_count = 0
            valid_indicators = 0
            
            # Track signal categories for better weighting
            trend_signals = {'buy': 0, 'sell': 0, 'confidence': 0}
            momentum_signals = {'buy': 0, 'sell': 0, 'confidence': 0}
            volatility_signals = {'buy': 0, 'sell': 0, 'confidence': 0}
            
            # Determine trend direction for regime-based weighting
            trend_direction = self._determine_trend_direction(indicators)
            
            for indicator_name, result in indicators.items():
                # Skip indicators with errors
                if result.metadata.get('error'):
                    continue
                
                # Only count indicators with valid confidence
                if result.confidence is not None:
                    # Apply regime-based weighting (ChatGPT's suggestion)
                    adjusted_confidence = self._apply_regime_weighting(
                        indicator_name, result.confidence, trend_direction
                    )
                    
                    # Categorize indicators for weighted analysis
                    if indicator_name in ['sma', 'ema', 'macd', 'vwap']:
                        # Trend indicators - higher weight
                        if result.signal in [SignalType.BUY, SignalType.STRONG_BUY]:
                            trend_signals['buy'] += adjusted_confidence * 1.5
                            buy_signals += adjusted_confidence * 1.5
                        elif result.signal in [SignalType.SELL, SignalType.STRONG_SELL]:
                            trend_signals['sell'] += adjusted_confidence * 1.5
                            sell_signals += adjusted_confidence * 1.5
                        trend_signals['confidence'] += adjusted_confidence
                    elif indicator_name in ['rsi', 'stochastic', 'williams_r', 'cci', 'money_flow_index']:
                        # Momentum indicators - capped weight when trend is up (ChatGPT's suggestion)
                        if trend_direction == 'up':
                            # Cap oscillator weight to 0.4 when trend is up
                            adjusted_confidence = min(adjusted_confidence, 0.4)
                        
                        if result.signal in [SignalType.BUY, SignalType.STRONG_BUY]:
                            momentum_signals['buy'] += adjusted_confidence
                            buy_signals += adjusted_confidence
                        elif result.signal in [SignalType.SELL, SignalType.STRONG_SELL]:
                            momentum_signals['sell'] += adjusted_confidence
                            sell_signals += adjusted_confidence
                        momentum_signals['confidence'] += adjusted_confidence
                    else:
                        # Volatility and other indicators - lower weight
                        if result.signal in [SignalType.BUY, SignalType.STRONG_BUY]:
                            volatility_signals['buy'] += adjusted_confidence * 0.8
                            buy_signals += adjusted_confidence * 0.8
                        elif result.signal in [SignalType.SELL, SignalType.STRONG_SELL]:
                            volatility_signals['sell'] += adjusted_confidence * 0.8
                            sell_signals += adjusted_confidence * 0.8
                        volatility_signals['confidence'] += adjusted_confidence
                    
                    total_confidence += adjusted_confidence
                    signal_count += 1
                
                valid_indicators += 1
            
            if signal_count == 0:
                return None, None  # N/A instead of fake 0.0
            
            avg_confidence = total_confidence / signal_count if signal_count > 0 else None
            
            # Ensure confidence is within valid range (only if not None)
            if avg_confidence is not None:
                avg_confidence = max(0.1, min(0.9, avg_confidence))
            
            # Enhanced consensus logic with trend consideration (ChatGPT's suggestion)
            trend_strength = abs(trend_signals['buy'] - trend_signals['sell'])
            momentum_strength = abs(momentum_signals['buy'] - momentum_signals['sell'])
            
            # Check for conflicting signals (trend vs momentum)
            trend_bullish = trend_signals['buy'] > trend_signals['sell']
            momentum_bullish = momentum_signals['buy'] > momentum_signals['sell']
            signal_conflict = trend_bullish != momentum_bullish
            
            # Adjust confidence based on signal conflicts
            if signal_conflict:
                avg_confidence = avg_confidence * 0.7  # Reduce confidence when signals conflict
                
                # Aggressive conflict resolution: always prioritize trend over momentum when conflict exists
                # Reduce momentum influence by 70% when conflicting with trend
                momentum_signals['buy'] *= 0.3
                momentum_signals['sell'] *= 0.3
                # Recalculate totals
                buy_signals = trend_signals['buy'] + momentum_signals['buy'] + volatility_signals['buy']
                sell_signals = trend_signals['sell'] + momentum_signals['sell'] + volatility_signals['sell']
            
            # Determine final signal with improved logic (ChatGPT's grading system)
            if buy_signals > sell_signals:
                if buy_signals > 2.5 and trend_bullish:
                    signal = SignalType.STRONG_BUY
                elif buy_signals > 1.5:
                    signal = SignalType.BUY
                else:
                    signal = SignalType.HOLD
            elif sell_signals > buy_signals:
                # Apply ChatGPT's grading system for SELL signals
                if sell_signals > 2.5 and not trend_bullish:
                    signal = SignalType.STRONG_SELL
                elif sell_signals > 1.5:
                    signal = SignalType.SELL
                else:
                    signal = SignalType.HOLD
            else:
                signal = SignalType.HOLD
                
            return signal, avg_confidence
            
        except Exception as e:
            import logging
            logging.error(f"Error generating consensus signal: {e}")
            return None, None  # N/A instead of fake 0.0
    
    def _determine_trend_direction(self, indicators: Dict[str, IndicatorResult]) -> str:
        """Determine overall trend direction for regime-based weighting."""
        try:
            trend_score = 0
            
            # Check trend indicators
            for indicator_name in ['sma', 'ema', 'macd']:
                if indicator_name in indicators:
                    result = indicators[indicator_name]
                    if result.signal in [SignalType.BUY, SignalType.STRONG_BUY]:
                        trend_score += 1
                    elif result.signal in [SignalType.SELL, SignalType.STRONG_SELL]:
                        trend_score -= 1
            
            if trend_score > 0:
                return 'up'
            elif trend_score < 0:
                return 'down'
            else:
                return 'neutral'
                
        except Exception:
            return 'neutral'
    
    def _apply_regime_weighting(self, indicator_name: str, confidence: float, trend_direction: str) -> float:
        """Apply regime-based weighting to indicators (ChatGPT's suggestion)."""
        try:
            # Base confidence
            adjusted_confidence = confidence
            
            # Regime-based adjustments
            if trend_direction == 'up':
                # Reduce oscillator weights when trend is up
                if indicator_name in ['rsi', 'stochastic', 'williams_r', 'cci']:
                    adjusted_confidence = min(adjusted_confidence, 0.4)
                # Increase trend indicator weights when trend is up
                elif indicator_name in ['sma', 'ema', 'macd']:
                    adjusted_confidence = min(adjusted_confidence * 1.2, 0.9)
            elif trend_direction == 'down':
                # Reduce oscillator weights when trend is down
                if indicator_name in ['rsi', 'stochastic', 'williams_r', 'cci']:
                    adjusted_confidence = min(adjusted_confidence, 0.4)
                # Increase trend indicator weights when trend is down
                elif indicator_name in ['sma', 'ema', 'macd']:
                    adjusted_confidence = min(adjusted_confidence * 1.2, 0.9)
            
            return adjusted_confidence
            
        except Exception:
            return confidence
